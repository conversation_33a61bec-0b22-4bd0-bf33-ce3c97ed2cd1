using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
    public class Lettering : IEntity
  {
    public int Id { get; set; }
    public int IdCounterparty { get; set; }
    public double AmountIn { get; set; }
    public double AmountOut { get; set; }
    /// <summary>
    /// +1 for sales bill, -1 for purchase bills
    /// </summary>
    public int Direction { get; set; }
    public DateTime LetteringDate { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime Createddate { get; set; } = DateTime.Now;
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public Counterparties Counterparty { get; set; }

    public virtual ICollection<LetteringDetail> LetteringDetails { get; set; }
    public Lettering()
    {
      LetteringDetails = new HashSet<LetteringDetail>();
    }
  }
}
