  class Timesheet {
    final int id;
    final int? idProject;
    final int? idProjectLot;
    final int? idTask;
    final int? idUser;
    final DateTime tsDay;
    final double tsValue;
    final bool isValidated;
    final String? validatedBy;
    final DateTime? dateValidation;
    final bool isBillable;
    final bool isDeleted;
    final String createdBy;
    final DateTime? createdDate;
    final String? updatedBy;
    final DateTime? updatedDate;
    final double tsScore;
    final int? idUserBusinessUnit;

    Timesheet({
      required this.id,
      this.idProject,
      this.idProjectLot,
      this.idTask,
      this.idUser,
      required this.tsDay,
      required this.tsValue,
      required this.isValidated,
      this.validatedBy,
      this.dateValidation,
      required this.isBillable,
      required this.isDeleted,
      required this.createdBy,
      this.createdDate,
      this.updatedBy,
      this.updatedDate,
      required this.tsScore,
      this.idUserBusinessUnit,
    });

    factory Timesheet.fromJson(Map<String, dynamic> json) {
      return Timesheet(
        id: json['id'],
        idProject: json['idProject'],
        idProjectLot: json['idProjectLot'],
        idTask: json['idTask'],
        idUser: json['idUser'],
        tsDay: DateTime.parse(json['tsDay']),
        tsValue: json['tsValue'].toDouble(),
        isValidated: json['isValidated'],
        validatedBy: json['validatedBy'],
        dateValidation: json['dateValidation'] != null
            ? DateTime.parse(json['dateValidation'])
            : null,
        isBillable: json['isBillable'],
        isDeleted: json['isDeleted'],
        createdBy: json['createdBy'],
        createdDate: json['createdDate'] != null
            ? DateTime.parse(json['createdDate'])
            : null,
        updatedBy: json['updatedBy'],
        updatedDate: json['updatedDate'] != null
            ? DateTime.parse(json['updatedDate'])
            : null,
        tsScore: json['tsScore'].toDouble(),
        idUserBusinessUnit: json['idUserBusinessUnit'],
      );
    }

    Map<String, dynamic> toJson() {
      return {
        'id': id,
        'idProject': idProject,
        'idProjectLot': idProjectLot,
        'idTask': idTask,
        'idUser': idUser,
        'tsDay': tsDay.toIso8601String(),
        'tsValue': tsValue,
        'isValidated': isValidated,
        'validatedBy': validatedBy,
        'dateValidation': dateValidation?.toIso8601String(),
        'isBillable': isBillable,
        'isDeleted': isDeleted,
        'createdBy': createdBy,
        'createdDate': createdDate?.toIso8601String(),
        'updatedBy': updatedBy,
        'updatedDate': updatedDate?.toIso8601String(),
        'tsScore': tsScore,
        'idUserBusinessUnit': idUserBusinessUnit,
      };
    }
  }
