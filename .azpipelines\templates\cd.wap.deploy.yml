parameters:
- name: ServiceConnection
  type: string
- name: resourceGroup
  type: string
- name: WebAppName
  type: string
- name: TargetEnvironment
  type: string
- name: appsParameters
  type: object

jobs:
- deployment:
  displayName: "TMT Mobile App Deployment"
  environment: ${{ parameters.TargetEnvironment }}
  strategy:
    runOnce:
        deploy:
            steps:
            - task: AzureKeyVault@2
              displayName: Azure Key Vault
              inputs:
                azureSubscription: ${{parameters.ServiceConnection}}
                KeyVaultName: '$(keyVaultName)'
                SecretsFilter: '*'
                RunAsPreJob: true
            - task: AzureWebApp@1
              displayName: Deploy on Web App Service
              inputs:
                azureSubscription: ${{parameters.ServiceConnection}}
                appType: 'webAppLinux'
                appName: ${{parameters.WebAppName}}
                package: '$(Agent.BuildDirectory)/${{parameters.appsParameters.FileTransformFolderPath}}'

            - task: AzureAppServiceSettings@1
              displayName: Configure Web App Service
              inputs:
                azureSubscription: '${{parameters.ServiceConnection}}'
                appName: '${{parameters.WebAppName}}'
                appSettings: ${{parameters.appsParameters.appSettings}}
