using System;
using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
  public partial class Projectorders : IEntity
  {
    public Projectorders()
    {
      Projectbills = new HashSet<Projectbills>();
   //   OrderFiles = new HashSet<Projectorderfiles>();
      RebillableFees = new HashSet<Projectfees>();
    }

    public int Id { get; set; }
    public DateTime? Dateorder { get; set; }
    public int? Idordertype { get; set; }
    public int? Idproject { get; set; }
    public string? Reference { get; set; }
    public string? Comment { get; set; }
    public double? Quantity { get; set; }
    public double? Rate { get; set; }
    public double? Totalamount { get; set; }
    public string? Contractreference { get; set; }
    public DateTime? Datecontract { get; set; }
    public DateTime? Datecontractend { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public string? Ordernumber { get; set; }
    public int? IdAgreementType { get; set; }
    public int? IdCurrency { get; set; }
    public int? IdProduct { get; set; }

    public virtual Appvariables OrderType { get; set; }
    public virtual Appvariables AgreementType { get; set; }
    public virtual Projects Project { get; set; }
    public virtual Currency? Currency { get; set; }
 //   public virtual Product? Product { get; set; }
    public virtual ICollection<Projectbills> Projectbills { get; set; }
   // public ICollection<Projectorderfiles> OrderFiles { get; set; }
    public ICollection<Projectfees> RebillableFees { get; set; }

  }
}
