using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class EmailLogsRepository : CoreRepository<EmailsLog, DynamicDbContext>
    {
        public EmailLogsRepository(DynamicDbContext context) : base(context)
        {
        }

        
    }
}
