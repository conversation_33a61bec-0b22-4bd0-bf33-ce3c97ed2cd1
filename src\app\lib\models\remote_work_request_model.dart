class RemoteWorkRequest {
  int? id;
  int requestorId;
  int? assignedToManagerId;
  double quantity;
  DateTime dateStart;
  String? comment;
  int statusId;
  String? statusName;
  String? approvedBy;
  DateTime? approvedDate;
  bool? isDeleted; // Changed to nullable
  DateTime? createdDate;
  DateTime? updatedDate;

  RemoteWorkRequest({
    this.id,
    required this.requestorId,
    this.assignedToManagerId,
    required this.quantity,
    required this.dateStart,
    this.comment,
    required this.statusId,
    this.statusName,
    this.approvedBy,
    this.approvedDate,
    this.isDeleted, // Changed to nullable
    this.createdDate,
    this.updatedDate,
  });

  factory RemoteWorkRequest.fromJson(Map<String, dynamic> json) {
    return RemoteWorkRequest(
      id: json['id'],
      requestorId: json['requestorId'],
      assignedToManagerId: json['assignedToManagerId'],
      quantity:
          (json['quantity'] as num).toDouble(), // Handle both int and double
      dateStart: DateTime.parse(json['dateStart']),
      comment: json['comment'],
      statusId: json['statusId'],
      statusName: json['statusName'],
      approvedBy: json['approvedBy'],
      approvedDate: json['approvedDate'] != null
          ? DateTime.parse(json['approvedDate'])
          : null,
      isDeleted: json['isDeleted'] ?? false, // Provide a default value if null
      createdDate: json['createddate'] != null
          ? DateTime.parse(json['createddate'])
          : null,
      updatedDate: json['updateddate'] != null
          ? DateTime.parse(json['updateddate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id ?? 0,
      'requestorId': requestorId,
      'assignedToManagerId': assignedToManagerId,
      'quantity': quantity,
      'dateStart': dateStart.toIso8601String(),
      'comment': comment,
      'statusId': statusId,
      'statusName': statusName,
      'approvedBy': approvedBy,
      'approvedDate': approvedDate?.toIso8601String(),
      'isDeleted': isDeleted ?? false, // Provide a default value if null
      'createddate': createdDate?.toIso8601String(),
      'updateddate': updatedDate?.toIso8601String(),
    };
  }
}
