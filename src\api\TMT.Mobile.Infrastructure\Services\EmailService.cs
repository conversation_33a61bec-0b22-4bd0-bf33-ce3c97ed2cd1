﻿using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Services.Interfaces;

namespace TMT.Mobile.Infrastructure.Services
{
    public static class EmailService
    {
        public static string DevAPIURL = "http://wh.tmt-software.com/";
        public static string DevURL = "http://localhost:4220/";

        /// <summary>
        /// Send Email confirmation registery
        /// </summary>
        /// <param name="user"></param>
        /// <param name="_emailService"></param>
        public static void SendEmailConfirmationRegistry(User user, IBPEmailService _emailService, string validationCode)
        {
            string emailBody = "Bonjour {0}, <Br/>" +
                               "Nous avons bien reçu votre demande de création de compte chez TMT Softwares.<Br/> " +
                               "Votre code de validation est : {1},<Br/>" +
                               "Bien cordialement,<Br/>" +
                               "L’équipe TMT Softwares.";
            _emailService.SendEmail(_emailService._sender, _emailService._senderEmail, _emailService._senderPwd, user.FirstName!, user.Email!,
                                         "TMT Softwares | Confirmation de l'adresse email",
                                         string.Format(emailBody, UppercaseFirst(user.FirstName!),user.AccountValidationNumber),
                                         _emailService._smtpServer, _emailService._smtpPort, true);
        }
      
        /// <summary>
        /// Send Email Reset Password
        /// </summary>
        /// <param name="user"></param>
        /// <param name="_emailService"></param>
        public static void SendEmailResetPassword(User user, IBPEmailService _emailService)
        {

            string emailBody = "Bonjour {0}, <Br/>" +

                               "Nous avons bien reçu votre demande de réinitialisation de mot de passe.<Br/>" +
                               "Votre code de récupération : {1},<Br/>" +
                               "Cordialement,<Br/>" +
                               "L'équipe TMT Softwares.";
            _emailService.SendEmail(_emailService._sender, _emailService._senderEmail, _emailService._senderPwd,
                                         user.FirstName!, user.Email!,
                                         "TMT Softwares | Réinitialisation de mot de passe",
                                         string.Format(emailBody, UppercaseFirst(user.FirstName!), user.ResetPasswordNumber),
                                         _emailService._smtpServer, _emailService._smtpPort, true);
        }

        /// <summary>
        /// Send Email Desacivate Account
        /// </summary>
        /// <param name="user"></param>
        /// <param name="_emailService"></param>
        public static void SendEmailDesacivateAccount(User user, IBPEmailService _emailService)
        {

            string emailBody = "Bonjour {0}, <Br/>" +
                               "Par ce message, nous vous informons que votre compte a été desactivé.<Br/> " +
                               "Si cette démarche est accidentelle ou si vous souhaitez faire restaurer votre compte,nous vous invitons a contacter votre interlocuteur Domino ou <Br/>  " +
                               "<a href=\"{1}\">  nous envoyer un message </a><Br/> " +
                               "Espérant vous revoir trés vite,<Br/>" +
                               "Cordialement,<Br/>" +
                               "L'équipe Domino.";
            _emailService.SendEmail(_emailService._sender, _emailService._senderEmail, _emailService._senderPwd,
                                         user.FirstName!, user.Email!,
                                         "Domino | Votre compte a été desactivé",
                                         string.Format(emailBody, UppercaseFirst(user.FirstName!), EmailService.DevURL, user.Email, user.ResetPasswordNumber),
                                         _emailService._smtpServer, _emailService._smtpPort, true);
        }

        /// <summary>
        /// Send Email Unsubscribe
        /// </summary>
        /// <param name="user"></param>
        /// <param name="_emailService"></param>
        public static void SendEmailUnsubscribe(User user, IBPEmailService _emailService)
        {

            string emailBody = "Bonjour {0}, <Br/>" +
                               "Nous avons bien reçu votre demande de désinscription à l'espace Domino - Brand Pulse<Br/> " +
                               "S'il s'agit d'une erreur, il vous suffit de vous recréer un compte en cliquant sur ce lien :<Br/>" +
                               "Cordialement,<Br/>" +
                               "L'équipe Domino.";
            _emailService.SendEmail(_emailService._sender, _emailService._senderEmail, _emailService._senderPwd,
                                         user.FirstName!, user.Email!,
                                         "Domino | Votre compte a été desactivé",
                                         string.Format(emailBody, UppercaseFirst(user.FirstName!), user.Email, user.ResetPasswordNumber),
                                         _emailService._smtpServer, _emailService._smtpPort, true);
        }
        static string UppercaseFirst(string s)
        {
            // Check for empty string.
            if (string.IsNullOrEmpty(s))
            {
                return string.Empty;
            }
            // Return char and concat substring.
            return char.ToUpper(s[0]) + s.Substring(1);
        }

        /// <summary>
        /// Rappel pour Terminer le remplissage du Brand Pulse.
        /// </summary>
        /// <param name="user"></param>
        /// <param name="_emailService"></param>
        /// <param name="listDemand"></param>
        public static void SendEmailAchievetmt_mobile_api(User user, IBPEmailService _emailService, List<string> listDemand)
        {
            string emailBody = "Bonjour {0}, <Br/>" +
                               "Domino Vous rappelle de bien vouloir terminer le remplissage de votre Brand Pulse.<Br/> " +
                               "Merci.<Br/>" +
                               "{1},<Br/>" +
                               "";
            //string demandList = string.Join("<Br/>", listDemand);
            _emailService.SendEmail(_emailService._sender, _emailService._senderEmail, _emailService._senderPwd,
                                         user.FirstName!, "<EMAIL>",
                                         "Domino | Rappel pour terminer le Brand ",
                                         //string.Format(emailBody, UppercaseFirst(person.Firstname), person.Email, person.ResetPasswordToken),
                                         //_emailService._smtpServer, _emailService._smtpPort, true);
                                         // string.Format(emailBody, UppercaseFirst(user.Name!), demandList),
                                         string.Format(emailBody, UppercaseFirst(user.FirstName!)),
                                       _emailService._smtpServer, _emailService._smtpPort, true);
        }

        public static void SendEmailContactertmt_mobile_api(User user, IBPEmailService _emailService)
        {
            string emailBody = "Bonjour {0}, <Br/>" +
                               "Nous avons bien reçu votre demande de désinscription à l'espace Domino - Brand Pulse.<Br/>" +
                               "S'il s'agit d'une erreur ou si vous changez d’avis, il vous suffit de vous recréer un compte en cliquant sur ce lien : http://bp.tmt-software.com/.<Br/>" +
                               "N’hésitez pas à nous contacter par retour de mail <NAME_EMAIL> pour tout renseignement complémentaire.<Br/>" +
                               "<p></p>" +
                               "En espérant vous revoir vite.<Br/>" +
                               "Cordialement,<Br/>" +
                               "L'équipe Ween Hub.";

            _emailService.SendEmail(_emailService._sender, _emailService._senderEmail, _emailService._senderPwd,
                                         user.FirstName!, user.Email!,
                                         "Ween Hub | Contact Ween-Hub",
                                         string.Format(emailBody, UppercaseFirst(user.FirstName!), user.Email, user.ResetPasswordNumber),
                                         _emailService._smtpServer, _emailService._smtpPort, true);

        }
    }
}
