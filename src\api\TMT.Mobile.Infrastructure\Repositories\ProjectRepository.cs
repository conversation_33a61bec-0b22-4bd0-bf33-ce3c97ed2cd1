﻿
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
namespace TMT.Mobile.Infrastructure.Repositories
{
    public class ProjectRepository : CoreRepository<Projects, DynamicDbContext>
    {
        public ProjectRepository(DynamicDbContext context) : base(context)
        {
        }


        public async Task<Projects> GetProject(int id)
        {
            try
            {
                var project = await context.Project.Where(p=> p.Id==id).SingleAsync();
            return project;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
    }

