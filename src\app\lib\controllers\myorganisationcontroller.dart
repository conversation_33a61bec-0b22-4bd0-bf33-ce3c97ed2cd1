import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/homepagecontroller.dart';
import 'package:tmt_mobile/models/userdata.dart';
import 'package:tmt_mobile/screens/landingScreen.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/utils/userPrefrences.dart';
import 'package:tmt_mobile/utils/userServices.dart';
import 'package:tmt_mobile/widgets/big_text.dart';

class MyOrganisationController extends GetxController {
  final GlobalController global;
  MyOrganisationController(this.global);

  RxInt selectedOrgIndex = (-1).obs;
  RxList<organisation> orglist = <organisation>[].obs;
  RxBool isLoading = false.obs;
  RxString errorMessage = ''.obs;
  var context = UserServices();
  Future getUserOrg({int retryCount = 0}) async {
    if (retryCount == 0) {
      isLoading.value = true;
      errorMessage.value = '';
    }

    try {
      orglist.clear();

      // Check if user email is available, if not try to load from preferences
      String? userEmail = global.appuser.value.email;
      if (userEmail == null || userEmail.isEmpty) {
        userEmail = UserPrefrences.getUserEmail();
        if (userEmail != null && userEmail.isNotEmpty) {
          global.appuser.value.email = userEmail;
        } else {
          throw Exception(
              "User email not available in global state or preferences");
        }
      }

      var response = await context.getAllOrgs(userEmail!);

      if (response.statusCode == 200) {
        var temp = json.decode(response.body);
        print("Fetched Orgs: $temp"); // Debug print

        if (temp != null && temp.isNotEmpty) {
          int index = 0;
          for (var a in temp) {
            if (a["guid"] == global.appuser.value.organisation) {
              selectedOrgIndex.value = index;
            }
            orglist.add(organisation(
              guid: a["guid"],
              name: a["name"],
            ));
            index++;
          }
          errorMessage.value = '';
        } else {
          // Empty response but successful API call
          print(
              "API returned empty organization list for user: ${global.appuser.value.email}");
        }
        orglist.refresh();
      } else if (response.statusCode == 401) {
        // Token might be expired, try to refresh or redirect to login
        print("Unauthorized access - token might be expired");
        errorMessage.value = "Session expired. Please login again.";

        // Clear stored data and redirect to login
        await _handleTokenExpired();
      } else {
        // Other HTTP errors
        print(
            "Failed to fetch organizations. Status: ${response.statusCode}, Body: ${response.body}");
        errorMessage.value =
            "Failed to load organizations. Status: ${response.statusCode}";

        // Retry logic for network errors
        if (retryCount < 2 &&
            (response.statusCode >= 500 || response.statusCode == 0)) {
          print("Retrying... Attempt ${retryCount + 1}");
          await Future.delayed(Duration(seconds: 2));
          return getUserOrg(retryCount: retryCount + 1);
        }

        orglist.clear();
        orglist.refresh();
      }
    } catch (e) {
      print("Exception while fetching organizations: $e");
      errorMessage.value = "Network error: ${e.toString()}";

      // Retry logic for network exceptions
      if (retryCount < 2) {
        print("Retrying due to exception... Attempt ${retryCount + 1}");
        await Future.delayed(Duration(seconds: 2));
        return getUserOrg(retryCount: retryCount + 1);
      }

      orglist.clear();
      orglist.refresh();
    } finally {
      if (retryCount == 0) {
        isLoading.value = false;
      }
    }
  }

  Future _handleTokenExpired() async {
    try {
      // Clear all stored data
      final storage = FlutterSecureStorage();
      await storage.delete(key: 'jwt');
      await UserPrefrences.deleteAll();

      // Reset global user data
      global.appuser.value = appUser();

      // Show message and redirect to login
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: "Session Expired",
          size: 18,
          color: Colors.red,
        ),
        messageText: Text(
          "Your session has expired. Please login again.",
          style: TextStyle(fontSize: 17),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        duration: const Duration(seconds: 3),
        overlayBlur: 0.7,
      );

      Get.offAll(LandingScreen());
    } catch (e) {
      print("Error handling token expiration: $e");
    }
  }

  // Method for pull-to-refresh functionality
  Future<void> refreshOrganizations() async {
    await getUserOrg();
  }

  Future selectedOrg(String guid, int index) async {
    // If the same organization is clicked again, deselect it
    if (index == selectedOrgIndex.value) {
      selectedOrgIndex.value = -1;
      // Clear organization data
      global.appuser.value.organisation = "";
      UserPrefrences.setCureentOrg("");
      selectedOrgIndex.refresh();
      return;
    }

    // Otherwise proceed with selecting the new organization
    if (index != selectedOrgIndex.value) {
      var response = await context.getOneOrg(guid);
      if (response.statusCode == 200) {
        var temp = json.decode(response.body);
        global.appuser.value.guid = temp["guid"];
        global.appuser.value.organisation = guid;
        global.appuser.value.token = temp["token"];
        final storage = FlutterSecureStorage();
        UserPrefrences.setUserEmail(global.appuser.value.email!);
        UserPrefrences.setUserGuid(global.appuser.value.guid!);
        UserPrefrences.setCureentOrg(guid);
        await storage.write(key: 'jwt', value: temp["token"]);

        selectedOrgIndex.value = index;
        Get.delete<HomePageController>();
        await Get.putAsync<HomePageController>(
          () async => HomePageController(),
        );
        selectedOrgIndex.refresh();
      } else if (response.statusCode == 400 ||
          response.statusCode == 401 ||
          response.statusCode == 500) {
        Get.offAll(LandingScreen());
      }
    }
  }

  @override
  void onInit() async {
    print("MyOrganisationController onInit called"); // ✅ Debug print

    var connected = await context.connected();
    print("Connected status: $connected"); // ✅ Debug print

    await getUserOrg(); // ✅ Load orgs

    if (connected == 0) {
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: "Oops! It seems like you've lost connection to the server",
          size: 18,
          color: Colors.green,
        ),
        messageText: Text(
          "Consider logging in again to re-establish your connection.",
          style: TextStyle(fontSize: 17),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        duration: const Duration(seconds: 1),
        overlayBlur: 0.7,
      );
      Get.offAll(LandingScreen());
    }

    super.onInit(); // ✅ Always call super at the end
  }
}
