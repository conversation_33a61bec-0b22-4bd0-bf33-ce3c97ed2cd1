import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/user_mood_controller.dart';
import 'package:tmt_mobile/models/user_mood_model.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/widgets/buttonwithicon.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';

class UserMoodFeedbackPage extends StatelessWidget {
  final UserMoodController controller;

  UserMoodFeedbackPage({super.key})
      : controller = Get.put(UserMoodController());

  @override
  Widget build(BuildContext context) {
    // Trigger fetch when screen is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.fetchUserMoods();
    });

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Obx(() {
        // Access GlobalController inside Obx to properly observe language changes
        GlobalController global = Get.find<GlobalController>();

        if (controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(MyColors.MainRedSecond),
                ),
                SizedBox(height: 16),
                Text(
                  global.lang.value == "fr"
                      ? 'Chargement des retours d\'humeur...'
                      : 'Loading mood feedbacks...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }
        if (controller.userMoods.isEmpty) {
          return Center(
            child: Container(
              padding: EdgeInsets.all(32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: MyColors.MainRedSecond.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Icon(
                      Icons.mood_bad,
                      size: 64,
                      color: MyColors.MainRedSecond,
                    ),
                  ),
                  SizedBox(height: 24),
                  Text(
                    global.lang.value == "fr"
                        ? 'Aucun retour d\'humeur'
                        : 'No Mood Feedbacks',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    global.lang.value == "fr"
                        ? 'Aucun retour d\'humeur utilisateur trouvé pour le moment.'
                        : 'No user mood feedbacks found at the moment.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 32),
                  ButtonWithIcon(
                    text: global.lang.value == "fr" ? 'Actualiser' : 'Refresh',
                    mainColor: MyColors.MainRedSecond,
                    textcolor: Colors.white,
                    onPressed: () => controller.fetchUserMoods(),
                    height: 48,
                    width: 140,
                    fontSize: 16,
                  ),
                ],
              ),
            ),
          );
        }
        return RefreshIndicator(
          color: MyColors.MainRedSecond,
          onRefresh: () async {
            await controller.fetchUserMoods();
          },
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: controller.userMoods.length,
            itemBuilder: (context, index) {
              var mood = controller.userMoods[index];
              return Obx(() {
                GlobalController global = Get.find<GlobalController>();
                return Container(
                  margin: EdgeInsets.only(bottom: 16),
                  child: _buildModernMoodCard(mood, global),
                );
              });
            },
          ),
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          controller.fetchUserMoods();
        },
        backgroundColor: MyColors.MainRedSecond,
        child: Icon(Icons.refresh, color: Colors.white),
      ),
    );
  }

  Widget _buildModernMoodCard(UserMood mood, GlobalController global) {
    return GestureDetector(
      onTap: () => _showDetailsDialog(Get.context!, mood),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _showDetailsDialog(Get.context!, mood),
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _getMoodColor(mood.score).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getMoodEmoji(mood.score),
                          style: TextStyle(fontSize: 24),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getMoodText(mood.score),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  mood.isAnonymous
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  size: 16,
                                  color: mood.isAnonymous
                                      ? Colors.orange
                                      : Colors.green,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  mood.isAnonymous
                                      ? (global.lang.value == "fr"
                                          ? 'Anonyme'
                                          : 'Anonymous')
                                      : (global.lang.value == "fr"
                                          ? 'Non anonyme'
                                          : 'Not Anonymous'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: mood.isAnonymous
                                        ? Colors.orange
                                        : Colors.green,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: MyColors.MainRedSecond.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: MyColors.MainRedSecond,
                        ),
                      ),
                    ],
                  ),
                  if (mood.comment != null && mood.comment!.isNotEmpty) ...[
                    SizedBox(height: 16),
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.format_quote,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              mood.comment!,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[700],
                                fontStyle: FontStyle.italic,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getMoodColor(int? score) {
    switch (score) {
      case 1:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 5:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _showDetailsDialog(BuildContext context, UserMood mood) {
    showDialog(
      context: context,
      builder: (context) {
        return Obx(() {
          GlobalController global = Get.find<GlobalController>();
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Container(
              padding: EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                color: MyColors.MainRedSecond.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.info_outline,
                    color: MyColors.MainRedSecond,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text(
                    global.lang.value == "fr"
                        ? 'Détails de l\'humeur'
                        : 'Mood Details',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: MyColors.MainRedSecond,
                    ),
                  ),
                ],
              ),
            ),
            content: SingleChildScrollView(
              child: SizedBox(
                width: double.maxFinite,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailCard(
                      icon: Icons.sentiment_satisfied,
                      label: global.lang.value == "fr" ? 'Humeur' : 'Mood',
                      value: _getMoodText(mood.score),
                    ),
                    _buildDetailCard(
                      icon: Icons.comment,
                      label:
                          global.lang.value == "fr" ? 'Commentaire' : 'Comment',
                      value: mood.comment ??
                          (global.lang.value == "fr"
                              ? 'Aucun commentaire'
                              : 'No comment'),
                    ),
                    _buildDetailCard(
                      icon: Icons.visibility,
                      label:
                          global.lang.value == "fr" ? 'Anonymat' : 'Anonymity',
                      value: mood.isAnonymous
                          ? (global.lang.value == "fr"
                              ? 'Anonyme'
                              : 'Anonymous')
                          : (global.lang.value == "fr"
                              ? 'Non anonyme'
                              : 'Not Anonymous'),
                      valueColor:
                          mood.isAnonymous ? Colors.orange : Colors.green,
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ButtonWithIcon(
                  text: global.lang.value == "fr" ? 'Fermer' : 'Close',
                  mainColor: MyColors.MainRedSecond,
                  textcolor: Colors.white,
                  onPressed: () => Get.back(),
                  height: 45,
                  width: double.infinity,
                  fontSize: 16,
                ),
              ),
            ],
          );
        });
      },
    );
  }

  Widget _buildDetailCard({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: MyColors.MainRedSecond.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: MyColors.MainRedSecond,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[600],
                    letterSpacing: 0.5,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: valueColor ?? Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getMoodEmoji(int? score) {
    switch (score) {
      case 1:
        return '😢';
      case 3:
        return '😐';
      case 5:
        return '😊';
      default:
        return score?.toString() ?? 'No score';
    }
  }

  String _getMoodText(int? score) {
    GlobalController global = Get.find<GlobalController>();
    switch (score) {
      case 1:
        return global.lang.value == "fr" ? 'Triste' : 'Sad';
      case 3:
        return global.lang.value == "fr" ? 'Neutre' : 'Neutral';
      case 5:
        return global.lang.value == "fr" ? 'Heureux' : 'Happy';
      default:
        return global.lang.value == "fr" ? 'Score inconnu' : 'Unknown score';
    }
  }
}
