class VacationRequest {
  int? id;
  int idtype;
  String? title;
  String? description;
  DateTime datestart;
  DateTime? dateend; // Make dateend optional
  int quantity;
  int? idassignedtomanager;
  int? idmanagervalidationstatus;
  int? idhrvalidationstatus;
  String? managercomment;
  String? managerfullname;

  VacationRequest({
    this.id,
    required this.idtype,
    this.title,
    this.description,
    required this.datestart,
    this.dateend, // Include dateend
    required this.quantity,
    this.idassignedtomanager,
    this.idmanagervalidationstatus,
    this.idhrvalidationstatus,
    this.managercomment,
    this.managerfullname,
  });

  factory VacationRequest.fromJson(Map<String, dynamic> json) {
    return VacationRequest(
      id: json['id'],
      idtype: json['idtype'],
      title: json['title'],
      description: json['description'],
      datestart: DateTime.parse(json['datestart']),
      dateend: json['dateend'] != null
          ? DateTime.parse(json['dateend'])
          : null, // Parse dateend if it exists
      quantity: json['quantity'],
      idassignedtomanager: json['idassignedtomanager'],
      idmanagervalidationstatus: json['idmanagervalidationstatus'],
      idhrvalidationstatus: json['idhrvalidationstatus'],
      managercomment: json['managercomment'],
      managerfullname: json['managerfullname'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id ?? 0, // Ensure id is not null
      'idtype': idtype,
      'title': title,
      'description': description,
      'datestart': datestart.toIso8601String(),
      'dateend': dateend?.toIso8601String(), // Include dateend if it exists
      'quantity': quantity,
      'idassignedtomanager': idassignedtomanager,
      'idmanagervalidationstatus': idmanagervalidationstatus,
      'idhrvalidationstatus': idhrvalidationstatus,
      'managercomment': managercomment,
      'managerfullname': managerfullname,
    };
  }
}
