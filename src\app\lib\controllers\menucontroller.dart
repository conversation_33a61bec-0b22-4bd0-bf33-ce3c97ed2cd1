import 'package:get/get.dart';
import 'package:tmt_mobile/screens/homePage.dart';
import 'package:tmt_mobile/screens/MyOrganisationScreen.dart';
import 'package:tmt_mobile/screens/remote_work_request_validation_screen.dart';
import 'package:tmt_mobile/screens/remote_work_requests_list_screen.dart';
import 'package:tmt_mobile/screens/user_mood_list_screen.dart';
import 'package:tmt_mobile/screens/user_tag_screen.dart';
import 'package:tmt_mobile/screens/vacation_request_screen.dart';
import 'package:tmt_mobile/screens/remote_work_request_screen.dart';
import 'package:tmt_mobile/screens/user_mood_screen.dart';
import 'package:tmt_mobile/screens/vacation_request_validation_screen.dart';
import 'package:tmt_mobile/screens/vacation_requests_list_screen.dart';

import '../screens/manager_admin_screen.dart'; // Import the new screen

class Menucontroller extends GetxController {
  var Screens = [
    HomePage(),
    MyOrganisationScreen(),
    VacationRequestScreen(),
    RemoteWorkRequestScreen(),
    UserMoodScreen(),
    VacationRequestsListScreen(),
    RemoteWorkRequestsListScreen(),
    VacationRequestValidationPage(), // Add the new screen
    RemoteWorkRequestValidationPage(), // Add the new screen
    ManagerAdminPage(),
    UserTagScreen(),
    UserMoodFeedbackPage(), // Add the new screen
  ].obs;

  var screenindex = 0.obs;
}
