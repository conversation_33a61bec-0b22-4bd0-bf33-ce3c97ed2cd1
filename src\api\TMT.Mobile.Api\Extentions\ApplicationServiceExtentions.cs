﻿using TMT.Mobile.Api.Interfaces;
using TMT.Mobile.Api.Services;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using TMT.Mobile.Infrastructure.Services.Interfaces;
using TMT.Mobile.Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using System.Configuration;


using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using System.Security.Claims; 
using TMT.Mobile.Core.Consts;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Api.Extentions
{
  public static class ApplicationServiceExtentions
  {
    public static IServiceCollection AddApplicationServices(this IServiceCollection services, IConfiguration config)
    {
    services.AddLogging();
            services.AddScoped<ITokenService, TokenService>();
            services.AddScoped<IBPEmailService, BPEmailService>();

            services.AddScoped<TmtMobileContext>();
            services.AddDbContext<TmtMobileContext>(options =>
            {
                options.UseNpgsql(config.GetConnectionString("TmtMobileApiContextConnection"));
                AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            });

            services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            services.AddScoped<DynamicDbContext>(serviceProvider =>
            {
                var dbContext = serviceProvider.GetRequiredService<TmtMobileContext>();
                var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
                return BuildServiceProvider(serviceProvider, dbContext, httpContextAccessor);
            });

            services.AddScoped<UserRepository>();
            services.AddScoped<AppUserRepository>();
            services.AddScoped<ProjectRepository>();
            services.AddScoped<ProjectLotRepository>();
            services.AddScoped<ProjectTasksRepository>();
            services.AddScoped<CommonRepository>();
            services.AddScoped<LeaveRequestRepository>();
            services.AddScoped<AppvariableRepository>();
            services.AddScoped<RemoteWorkRequestRepository>();
            services.AddScoped<TagsRepository>();
            services.AddScoped<UserTagsRepository>();
            services.AddScoped<UserMoodRepository>();
            services.AddScoped<ProjectfeesRepository>();
            services.AddScoped<CurrencyRepository>();
            services.AddScoped<CounterpartiesRepository>();

            services.AddScoped<IEmailService,EmailDistributionService>();
            return services;
        }

        private static DynamicDbContext BuildServiceProvider(IServiceProvider serviceProvider, TmtMobileContext dbContext, IHttpContextAccessor httpContextAccessor)
{
    var user = httpContextAccessor.HttpContext.User as ClaimsPrincipal;
    var identity = user.Identity as ClaimsIdentity;
    var Org = user.Claims.SingleOrDefault(c => c.Type == TMTClaimsTypes.OrgGuid);

    if (Org == null)
    {
        throw new InvalidOperationException("OrgGuid claim is missing in the token.");
    }

    var OrgGuid = new Guid(Org.Value);
    Console.WriteLine($"OrgGuid from token: {OrgGuid}");

    var organization = dbContext.Organizations.FirstOrDefault(o => o.GUID == OrgGuid);
    if (organization == null)
    {
        throw new InvalidOperationException($"Organization with GUID {OrgGuid} not found in the database.");
    }

    var options = new DbContextOptionsBuilder<DynamicDbContext>()
        .UseNpgsql(organization.ConnectionString)
        .Options;
    return new DynamicDbContext(options);
}
    }
}