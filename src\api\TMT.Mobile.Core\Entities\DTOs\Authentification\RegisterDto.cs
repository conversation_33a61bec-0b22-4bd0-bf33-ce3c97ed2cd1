﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TMT.Mobile.Core.Entities.DTOs.Authentification
{
    public class RegisterDto
    {

    

        [Required]
        [StringLength(8, MinimumLength = 4)]
        public string? Password { get; set; }
        public string? Email { get; set; }
        public string?Firstname { get; set; }
        public string?Lastname{ get; set; }
    

    }

}
