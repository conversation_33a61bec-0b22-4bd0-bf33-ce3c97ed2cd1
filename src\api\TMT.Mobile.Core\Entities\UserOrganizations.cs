﻿using System;



using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
    public class UserOrganizations : IEntity
    {
        public int Id { get; set; }
        public int IdUser { get; set; }
        public int IdOrganization { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool IsAdmin { get; set; }
        public bool IsSuperUser { get; set; }
        public bool IsDeleted { get; set; }
        public OrgRoles Role { get; set; }
        public User User { get; set; }
        public Organization Organization { get; set; }
    }
}   