﻿using TMT.Mobile.Api.Interfaces;

using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using TMT.Mobile.Core.Entities;
using System;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Api.Services
{
  public class TokenService : ITokenService
  {

    // crypt and decrypt electronic information.
    private readonly SymmetricSecurityKey _key;

    public TokenService(IConfiguration config)
    {

      string? s = config["TokenKey"];
      _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(s));
    }


    public bool IsTokenValid(string token)
    {
      var tokenHandler = new JwtSecurityTokenHandler();


      try
      {
        var tokenValidationParameters = new TokenValidationParameters
        {
          ValidateIssuerSigningKey = true,
          IssuerSigningKey = _key,
          ValidateIssuer = false, // Set to true if you want to validate the issuer
          ValidateAudience = false, // Set to true if you want to validate the audience
          ClockSkew = TimeSpan.Zero // No tolerance for the time gap
        };

        // Try to validate the token and extract the claims if successful
        var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);

        // Check if the token is a JWT token
        if (validatedToken is JwtSecurityToken jwtSecurityToken)
        {
          // Check if the token has not expired
          if (jwtSecurityToken.ValidTo >= DateTime.UtcNow)
          {
            // Optionally, you can also check if the token is currently active
            if (jwtSecurityToken.ValidFrom <= DateTime.UtcNow)
            {
              return true;
            }
          }
        }
      }
      catch
      {
        // Token validation failed
      }

      return false;
    }

    public string CreateToken(User user, List<Claim> claims)
    {
      // Adding some Claims


      // Creating some credentials
      var creds = new SigningCredentials(_key, SecurityAlgorithms.HmacSha512Signature);

      // Describe how the token will look like
      var tokenDescriptor = new SecurityTokenDescriptor
      {
        Subject = new ClaimsIdentity(claims),
        Expires = DateTime.Now.AddDays(7),
        SigningCredentials = creds
      };

      // Need to create the token by the following Handler.
      var tokeHandler = new JwtSecurityTokenHandler();
      var token = tokeHandler.CreateToken(tokenDescriptor);

      // return the created Token
      return tokeHandler.WriteToken(token);
    }

    public string CreateToken(User user)
    {
      throw new NotImplementedException();
    }
  }
}
