﻿using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Core.Entities
{
    public partial class Approles
    {
        public Approles()
        {
            Apppermissions = new HashSet<Apppermissions>();
            Appuserroles = new HashSet<Appuserroles>();
        }

        public int Id { get; set; }
        public string Rolename { get; set; }
        public string Roledescription { get; set; }
        public bool Isdeleted { get; set; }
        public string Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }

        public virtual ICollection<Apppermissions> Apppermissions { get; set; }
        public virtual ICollection<Appuserroles> Appuserroles { get; set; }
    }
}
