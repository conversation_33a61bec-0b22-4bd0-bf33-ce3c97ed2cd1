﻿using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Core.Entities
{
    public partial class Appareas
    {
        public Appareas()
        {
            Apppermissions = new HashSet<Apppermissions>();
        }

        public int Id { get; set; }
        public string Areaname { get; set; }
        public string Description { get; set; }
        public bool Isdeleted { get; set; }
        public string Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }

        public virtual ICollection<Apppermissions> Apppermissions { get; set; }
    }
}
