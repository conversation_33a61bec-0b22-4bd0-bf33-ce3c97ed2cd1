﻿using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;


namespace TMT.Mobile.Core.Entities
{
    public partial class Appvariables : IEntity
    {
        public Appvariables()
        {
            ProjectsIdprojecttypeNavigation = new HashSet<Projects>();
            ProjectsIdstatusNavigation = new HashSet<Projects>();
            Projecttasks = new HashSet<Projecttasks>();
            BusinessUnitTeam = new HashSet<Appusers>();
            UsersTites = new HashSet<Appusers>();
            UsersOffices = new HashSet<Appusers>();
            BusinessUnitProjects = new HashSet<Projects>();

            Timesheetbusnessunits = new HashSet<Timesheets>();
            ProjectsIdEngagementTypeNavigation = new HashSet<Projects>();
        }

        public int Id { get; set; }
        public string Category { get; set; }
        public string Name { get; set; }
        public double? Value { get; set; }
        public string Other { get; set; }
        public string Other2 { get; set; }
        public bool? Active { get; set; }
        public bool? IsDelitable { get; set; }
        public bool? IsEditable { get; set; }
        public int? Idparent { get; set; }
        public bool? IsDefault { get; set; }
        [JsonIgnore]
        public virtual Appvariables ParentVariable { get; set; }

        public virtual ICollection<Projects> BusinessUnitProjects { get; set; }
        public virtual ICollection<Appusers> BusinessUnitTeam { get; set; }
        public virtual ICollection<Appusers> UsersOffices { get; set; }
        public virtual ICollection<Appusers> UsersTites { get; set; }
        public virtual ICollection<Appvariables> Childs { get; set; }
        public virtual ICollection<Projectlots> Projectlots { get; set; }

        public virtual ICollection<Projects> ProjectsIdprojecttypeNavigation { get; set; }
        public virtual ICollection<Projects> ProjectsIdstatusNavigation { get; set; }
        public virtual ICollection<Projects> ProjectsIdEngagementTypeNavigation { get; set; }

        public virtual ICollection<Projecttasks> Projecttasks { get; set; }
        public virtual ICollection<Projects> Scopes { get; set; }
        public virtual ICollection<Timesheets> Timesheetbusnessunits { get; set; }
    }
}
