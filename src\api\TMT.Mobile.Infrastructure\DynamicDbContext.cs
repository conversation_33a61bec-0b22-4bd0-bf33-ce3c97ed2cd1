﻿using Microsoft.EntityFrameworkCore;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities;
namespace TMT.Mobile.Infrastructure
{
    public class DynamicDbContext :DbContext
    {
    
        public DynamicDbContext(DbContextOptions<DynamicDbContext> options) : base(options)
        {
        }


        public virtual DbSet<Appvariables>         Appvariables { get; set; }
        public virtual DbSet<Approles>       Approles { get; set; }
        public virtual  DbSet<Apppermissions> Apppermissions { get; set; }
        public virtual  DbSet<Appuserroles> Appuserroles { get; set; }
        public virtual DbSet<Appusers> Appusers { get; set; }
        public virtual DbSet<Projectlots>           Projectlots { get; set; }
        public virtual DbSet<Projecttasks> Projecttasks { get; set; }
        public virtual  DbSet <Projects> Project { get; set; }
        public virtual  DbSet <Timesheets> Timesheets { get; set; }
        public virtual DbSet<RemoteWorkRequest> RemoteWorkRequests { get; set; }
        public virtual DbSet<LeaveRequest> LeaveRequests { get; set; }
        public virtual DbSet<Tag> Tags { get; set; }
        public virtual DbSet<UserTags> UserTags { get; set; }
        public DbSet<UserMood> UserMoods { get; set; }
        public DbSet<EmailsLog> EmailLogs { get; set; }
        public virtual DbSet<ExpenseRequest> ExpenseRequests { get; set; }
        public virtual DbSet<Counterparties> counterparties { get; set; }
        public virtual DbSet<Currency> Currencies { get; set; }
        public virtual DbSet<Projectfees> ProjectFees { get; set; }
        public virtual DbSet<Bill> Bills { get; set; }


            protected override void OnModelCreating(ModelBuilder modelBuilder)
        {

 modelBuilder.Entity<Projectfees>(entity =>
{
    entity.ToTable("projectfees");
    
    entity.HasKey(e => e.Id);
    entity.Property(e => e.Id)
        .HasColumnName("id")
        .ValueGeneratedOnAdd();

    entity.Property(e => e.Idproject)
        .HasColumnName("idproject");
    
    entity.Property(e => e.Feesdate)
        .HasColumnName("feesdate")
        .HasColumnType("date");
    
    entity.Property(e => e.Feesamount)
        .HasColumnName("feesamount");
    
    entity.Property(e => e.IdFeestype)
        .HasColumnName("idfeestype");
    
    entity.Property(e => e.IdAssignedto)
        .HasColumnName("idassignedto");
    
    entity.Property(e => e.Assignedtoexternal)
        .HasColumnName("assignedtoexternal")
        .HasMaxLength(255);
    
    entity.Property(e => e.Comment)
        .HasColumnName("comment")
        .HasMaxLength(1000);
    
    entity.Property(e => e.Reference)
        .HasColumnName("reference")
        .HasMaxLength(255);
    
    entity.Property(e => e.Isbillable)
        .HasColumnName("isbillable")
        .HasDefaultValue(false);
    
    entity.Property(e => e.Resaleamount)
        .HasColumnName("resaleamount");
    
    entity.Property(e => e.Isdeleted)
        .HasColumnName("isdeleted")
        .HasDefaultValue(false);
    
    entity.Property(e => e.Createdby)
        .HasColumnName("createdby")
        .HasMaxLength(255);
    
    entity.Property(e => e.Createddate)
        .HasColumnName("createddate")
        .HasColumnType("timestamp");
    
    entity.Property(e => e.Updatedby)
        .HasColumnName("updatedby")
        .HasMaxLength(255);
    
    entity.Property(e => e.Updateddate)
        .HasColumnName("updateddate")
        .HasColumnType("timestamp");
    
    entity.Property(e => e.IdStatus)
        .HasColumnName("idstatus");
    
    entity.Property(e => e.IdRebillingStatus)
        .HasColumnName("idrebillingstatus");
    
    entity.Property(e => e.Feesamounttaxincl)
        .HasColumnName("feesamounttaxincl");
    
    entity.Property(e => e.Idbill)
        .HasColumnName("idbill");
    
    entity.Property(e => e.Idprojectbills)
        .HasColumnName("idprojectbills");
    
    entity.Property(e => e.IdSupplier)
        .HasColumnName("idsupplier");
    
    entity.Property(e => e.IdClient)
        .HasColumnName("idclient");
    
    entity.Property(e => e.IdCurrency)
        .HasColumnName("idcurrency");
    
    entity.Property(e => e.ExchangeRate)
        .HasColumnName("exchangerate");
    
    entity.Property(e => e.IdOrder)
        .HasColumnName("idorder");
    
    entity.Property(e => e.IdEntity)
        .HasColumnName("identity");

     entity.HasOne(d => d.Bill)
        .WithMany()
        .HasForeignKey(d => d.Idbill)  
        .HasConstraintName("projectfees_idbill_fkey")
        .OnDelete(DeleteBehavior.Restrict);
     entity.HasOne(d => d.Client)
             .WithMany()
             .HasForeignKey(d => d.IdClient)
             .HasConstraintName("projectfees_idclient_fkey");
     entity.HasOne(d => d.AssignedTo)
                .WithMany()
                .HasForeignKey(d => d.IdAssignedto)
                .HasConstraintName("projectfees_idassignedto_fkey");
     entity.HasOne(d => d.Status)
        .WithMany()
        .HasForeignKey(d => d.IdStatus)
        .HasConstraintName("projectfees_idstatus_fkey");
    entity.HasMany(d => d.ExpenseRequests)
        .WithOne(e => e.ProjectFee)
        .HasForeignKey(e => e.ProjectfeesId)
        .HasConstraintName("expenserequests_idprojectfees_fkey")
        .OnDelete(DeleteBehavior.Cascade);
    entity.HasOne(d => d.Currency)
            .WithMany()
            .HasForeignKey(d => d.IdCurrency)
            .HasConstraintName("projectfees_idcurrency_fkey");
    entity.HasOne(d => d.Project)
                .WithMany()
                .HasForeignKey(d => d.Idproject)
                .HasConstraintName("projectfees_idproject_fkey");

    entity.HasOne(d => d.Type)
                .WithMany()
                .HasForeignKey(d => d.IdFeestype)
                .HasConstraintName("projectfees_idfeestype_fkey");
    entity.HasOne(d => d.RebillingStatus)
        .WithMany()
        .HasForeignKey(d => d.IdRebillingStatus)
        .HasConstraintName("projectfees_idrebillingstatus_fkey");
    entity.HasOne(d => d.Supplier)
             .WithMany()
             .HasForeignKey(d => d.IdSupplier)
             .HasConstraintName("projectfees_idsupplier_fkey");
    
});
 modelBuilder.Entity<Bill>(entity =>
    {
      entity.ToTable("bills");
      entity.Property(e => e.Id).HasColumnName("id");
      entity.Property(e => e.BillDate).HasColumnName("billdate");
      entity.Property(e => e.BillNumber).HasColumnName("billnumber");
      entity.Property(e => e.BillTypeId).HasColumnName("billtypeid");
      entity.Property(e => e.ClientId).HasColumnName("clientid");
      entity.Property(e => e.PublicNote).HasColumnName("publicnote");
      entity.Property(e => e.PrivateNote).HasColumnName("privatenote");
      entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");
      entity.Property(e => e.Createdby).HasColumnName("createdby");
      entity.Property(e => e.Createddate).HasColumnName("createddate");
      entity.Property(e => e.Updatedby).HasColumnName("updatedby");
      entity.Property(e => e.Updateddate).HasColumnName("updateddate");

      entity.Property(e => e.BillingAddress).HasColumnName("billingaddress");
      entity.Property(e => e.ClientContactDetails).HasColumnName("clientcontactdetails");
      entity.Property(e => e.ClientVATNumber).HasColumnName("clientvatnumber");
      entity.Property(e => e.IsValidated).HasColumnName("isvalidated");
      entity.Property(e => e.Counter).HasColumnName("counter");
      entity.Property(e => e.IdProject).HasColumnName("idproject");
      entity.Property(e => e.IdBankAccount).HasColumnName("idbankaccount");
      entity.Property(e => e.IdParentBill).HasColumnName("idparentbill");
      entity.Property(e => e.IdCurrency).HasColumnName("idcurrency");
      entity.Property(e => e.ExchangeRate).HasColumnName("exchangerate");

      entity.Property(e => e.IdStatus).HasColumnName("idstatus");
      entity.Property(e => e.PaymentDate).HasColumnName("paymentdate");


      entity.Property(e => e.AmountTaxIncl).HasColumnName("amounttaxincl");
      entity.Property(e => e.AmountTaxExcl).HasColumnName("amounttaxexcl");
      entity.Property(e => e.AmountVat).HasColumnName("amountvat");
      entity.Property(e => e.IsTaxExcluded).HasColumnName("istaxexcluded");

      entity.HasOne(e => e.Type)
            .WithMany()
            .HasForeignKey(d => d.BillTypeId)
            .HasConstraintName("bills_billtypeid_fkey");

      entity.HasOne(e => e.Client)
            .WithMany()
            .HasForeignKey(d => d.ClientId)
            .HasConstraintName("bills_clientid_fkey");

    //   entity.HasMany()
    //         .WithOne(e => e.Bill);

      entity.HasOne(e => e.Project)
            .WithMany()
            .HasForeignKey(d => d.IdProject)
            .HasConstraintName("bills_idproject_fkey");

      entity.HasOne(e => e.ParentBill)
            .WithMany(e => e.ChildBills)
            .HasForeignKey(d => d.IdParentBill)
            .HasConstraintName("bills_idparentbill_fkey");

      entity.HasOne(e => e.Status)
            .WithMany()
            .HasForeignKey(d => d.IdStatus)
            .HasConstraintName("bills_idstatus_fkey");

      entity.HasMany(e => e.Projectfees)
            .WithOne(e => e.Bill);

      entity.HasOne(e => e.Currency)
            .WithMany()
            .HasForeignKey(e => e.IdCurrency)
            .HasConstraintName("bills_idcurrency_fkey");


     // entity.HasMany(x => x.LetteringDetails).WithOne(x => x.Bill);

      entity.Property(e => e.IdEntity).HasColumnName("identity");
    //   entity.HasOne(x => x.Entity)
    //         .WithMany(x => x.Bills)
    //         .HasForeignKey(e => e.IdEntity)
    //         .HasConstraintName("bills_identity_fkey");

    //   entity.HasOne(x => x.Bank)
    //         .WithMany(x => x.Bills)
    //         .HasForeignKey(e => e.IdBankAccount)
    //         .HasConstraintName("bills_idbankaccount_fkey");

    });
            modelBuilder.Entity<Currency>(entity =>
    {
      entity.ToTable("currencies");

      entity.Property(e => e.Id).HasColumnName("id");
      entity.Property(e => e.Symbol).HasColumnName("symbol");
      entity.Property(e => e.Name).HasColumnName("name");
      entity.Property(e => e.IsoCode).HasColumnName("isocode");

    });
    modelBuilder.Entity<Counterparties>(entity =>
    {
      entity.ToTable("counterparties");

      entity.Property(e => e.Id).HasColumnName("id");

      entity.Property(e => e.Billingaddress).HasColumnName("billingaddress");

      entity.Property(e => e.Counterpartyname).HasColumnName("counterpartyname");

      entity.Property(e => e.Counterpartyreference).HasColumnName("counterpartyreference");

      entity.Property(e => e.Createdby).HasColumnName("createdby");
      entity.Property(e => e.ClientMainContactInfo).HasColumnName("clientmaincontactinfo");
      entity.Property(e => e.ClientMainContactName).HasColumnName("clientmaincontactname");

      entity.Property(e => e.Createddate)
                .HasColumnName("createddate")
                .HasDefaultValueSql("CURRENT_DATE");

      entity.Property(e => e.Idcounterpartytype).HasColumnName("idcounterpartytype");

      entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");
      entity.Property(e => e.Isinternal).HasColumnName("isinternal");
      entity.Property(e => e.Note).HasColumnName("note");

      entity.Property(e => e.Paymentterms).HasColumnName("paymentterms");

      entity.Property(e => e.Phonenumber).HasColumnName("phonenumber");

      entity.Property(e => e.Updatedby).HasColumnName("updatedby");

      entity.Property(e => e.Updateddate).HasColumnName("updateddate");

      entity.Property(e => e.Vatcode).HasColumnName("vatcode");
      entity.Property(e => e.ExcludeTaxes).HasColumnName("excludetaxes");

      entity.HasOne(d => d.Counterpartytype)
                .WithMany()
                .HasForeignKey(d => d.Idcounterpartytype)
                .HasConstraintName("counterparties_idcounterpartytype_fkey");

    //   entity.HasMany(d => d.Payments).WithOne(e => e.Counterparty);
    //   entity.HasMany(d => d.SupplierPayments).WithOne(e => e.Supplier);
    //   entity.HasMany(d => d.SupplierLetterings).WithOne(e => e.Supplier);
       entity.Property(e => e.IsEntity).HasColumnName("isentity");
    //   entity.HasMany(d => d.Fees).WithOne(e => e.Supplier);
    //   entity.HasMany(d => d.FeesClients).WithOne(e => e.Client);

      entity.HasMany(d => d.ExpenseRequests).WithOne(d => d.Supplier);
    });

           modelBuilder.Entity<ExpenseRequest>(entity =>
    {
        entity.ToTable("expenserequests");

        entity.HasKey(e => e.Id);
        entity.Property(e => e.Id).HasColumnName("id");
        entity.Property(e => e.DateExpense).HasColumnName("dateexpense");
        entity.Property(e => e.CurrencyId ).HasColumnName("idcurrency");
        entity.Property(e => e.ExpenseTypeId).HasColumnName("idexpensetype");
        entity.Property(e => e.StatusId).HasColumnName("idstatus");
        entity.Property(e => e.AssignedToId).HasColumnName("idassignedto");

        entity.Property(e => e.ProjectfeesId).HasColumnName("idprojectfees");
        entity.Property(e => e.AmountTaxIncl).HasColumnName("amounttaxincl");
        entity.Property(e => e.Amount).HasColumnName("amount");
        entity.Property(e => e.SupplierId).HasColumnName("idsupplier");
        entity.Property(e => e.IsDeleted).HasColumnName("isdeleted");
        entity.Property(e => e.CreatedDate).HasColumnName("createddate");
        entity.Property(e => e.UpdatedDate).HasColumnName("updateddate");
        entity.Property(e => e.UpdatedBy).HasColumnName("updatedby");
        entity.Property(e => e.BillReference).HasColumnName("billreference");
        entity.Property(e => e.Notes).HasColumnName("notes");
        entity.Property(e => e.CreatedBy).HasColumnName("createdby");
    });
            modelBuilder.Entity<EmailsLog>(entity =>
            {
            entity.ToTable("emailslogs");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Sender).HasColumnName("sender");
            entity.Property(e => e.SenderEmail).HasColumnName("senderemail");
            entity.Property(e => e.Recipients).HasColumnName("recipients");
            entity.Property(e => e.CarbonCopy).HasColumnName("carboncopy");
            entity.Property(e => e.Subject).HasColumnName("subject");
            entity.Property(e => e.Body).HasColumnName("body");
            entity.Property(e => e.DateSent).HasColumnName("datesent");
            entity.Property(e => e.Status).HasColumnName("status");
            
            entity.Ignore(e => e.Attachments);
        });

            modelBuilder.Entity<UserMood>().ToTable("usermood");
            modelBuilder.Entity<Tag>(entity =>
        {
            entity.ToTable("tags");

            entity.HasKey(e => e.Id);

            entity.Property(e => e.Id)
                .HasColumnName("id")
                .ValueGeneratedOnAdd();

            entity.Property(e => e.Synonyms)
                .HasColumnName("synonyms")
                .HasMaxLength(255);

            entity.Property(e => e.Derivative)
                .HasColumnName("derivative")
                .HasMaxLength(255);

            entity.Property(e => e.Description)
                .HasColumnName("description")
                .HasMaxLength(1000);

            entity.HasMany(e => e.UserTags)
                .WithOne(ut => ut.Tag)
                .HasForeignKey(ut => ut.IdTag)
                .OnDelete(DeleteBehavior.Cascade); 
        });

        modelBuilder.Entity<UserTags>(entity =>
        {
            entity.ToTable("tagsuser");

            entity.HasKey(e => e.Id);

            entity.Property(e => e.Id)
                .HasColumnName("id")
                .ValueGeneratedOnAdd();

            entity.Property(e => e.UserGuid)
                .HasColumnName("userguid")
                .IsRequired();

            entity.Property(e => e.IdTag)
                .HasColumnName("idtag")
                .IsRequired();

            entity.Property(e => e.TagScore)
                .HasColumnName("tagscore")
                .HasDefaultValue(0);

            entity.Property(e => e.IsLatest)
                .HasColumnName("islatest")
                .HasDefaultValue(false);

            entity.Property(e => e.CreatedBy)
                .HasColumnName("createdby")
                .HasMaxLength(255);

            entity.Property(e => e.CreatedDate)
                .HasColumnName("createddate")
                .HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.Property(e => e.UpdatedBy)
                .HasColumnName("updatedby")
                .HasMaxLength(255);

            entity.Property(e => e.UpdatedDate)
                .HasColumnName("updateddate");

            entity.Property(e => e.Comment)
                .HasColumnName("comment")
                .HasMaxLength(1000);

            entity.Property(e => e.IdCollaborator)
                .HasColumnName("idcollaborator");

            entity.HasOne(e => e.Tag)
                .WithMany(t => t.UserTags)
                .HasForeignKey(e => e.IdTag)  
                .OnDelete(DeleteBehavior.Cascade);  
            entity.HasOne(e => e.Collaborator)
                .WithMany()
                .HasForeignKey(e => e.IdCollaborator)
                .OnDelete(DeleteBehavior.SetNull); 
        });
           
            modelBuilder.Entity<Appvariables>(entity =>
            {
    entity.ToTable("appvariables");

    entity.Property(e => e.Id).HasColumnName("id");

    entity.Property(e => e.Category)
          .HasColumnName("category")
          .HasMaxLength(255);

    entity.Property(e => e.Name)
          .HasColumnName("name")
          .HasMaxLength(255);

    entity.Property(e => e.Value)
          .HasColumnName("value")
          .HasColumnType("double precision");

    entity.Property(e => e.Other)
          .HasColumnName("other")
          .HasMaxLength(255)
          .IsRequired(false);

    entity.Property(e => e.Other2)
          .HasColumnName("other2")
          .HasMaxLength(255)
          .IsRequired(false);

    entity.Property(e => e.Active)
          .HasColumnName("active")
          .HasDefaultValue(true);

    entity.Property(e => e.IsDelitable)
          .HasColumnName("isdelitable")
          .HasDefaultValue(true);

    entity.Property(e => e.IsEditable)
          .HasColumnName("iseditable")
          .HasDefaultValue(true);

    entity.Property(e => e.Idparent)
          .HasColumnName("idparent");

    entity.Property(e => e.IsDefault)
          .HasColumnName("isdefault")
          .HasDefaultValue(false);

    entity.HasOne(e => e.ParentVariable)
          .WithMany(e => e.Childs)
          .HasForeignKey(e => e.Idparent)
          .HasConstraintName("appvariables_idparent_fkey")
          .OnDelete(DeleteBehavior.Restrict); 

    entity.HasMany(e => e.BusinessUnitProjects)
          .WithOne() 
          .HasForeignKey("idbusinessunit") 
          .HasConstraintName("appvariables_idbusinessunit_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.BusinessUnitTeam)
          .WithOne() 
          .HasForeignKey("idbusinessunit") 
          .HasConstraintName("appvariables_idbusinessunit_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.UsersOffices)
          .WithOne() 
          .HasForeignKey("idoffice") 
          .HasConstraintName("appvariables_idoffice_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.UsersTites)
          .WithOne() 
          .HasForeignKey("idtitle") 
          .HasConstraintName("appvariables_idtitle_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.ProjectsIdprojecttypeNavigation)
          .WithOne() 
          .HasForeignKey("idprojecttype") 
          .HasConstraintName("appvariables_idprojecttype_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.ProjectsIdstatusNavigation)
          .WithOne() 
          .HasForeignKey("idstatus") 
          .HasConstraintName("appvariables_idstatus_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.ProjectsIdEngagementTypeNavigation)
          .WithOne()
          .HasForeignKey("idengagementtype") 
          .HasConstraintName("appvariables_idengagementtype_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.Projecttasks)
          .WithOne() 
          .HasForeignKey("idstatus") 
          .HasConstraintName("appvariables_idstatus_fkey")
          .OnDelete(DeleteBehavior.Restrict);

    entity.HasMany(e => e.Timesheetbusnessunits)
          .WithOne() 
          .HasForeignKey("idbusinessunit") 
          .HasConstraintName("appvariables_idbusinessunit_fkey")
          .OnDelete(DeleteBehavior.Restrict);
});

    modelBuilder.Entity<RemoteWorkRequest>(entity =>
            {
                entity.ToTable("remoteworkrequest");
                entity.Property(e => e.Id)
                 .HasColumnName("id")
                 .ValueGeneratedOnAdd();
                entity.Property(e => e.RequestorId).HasColumnName("resquestorid");
                entity.Property(e => e.AssignedToManagerId).HasColumnName("assignedtomanagerid");
                entity.Property(e => e.Quantity).HasColumnName("quantity");
                entity.Property(e => e.DateStart).HasColumnName("datestart");
                entity.Property(e => e.Comment).HasColumnName("comment");
                entity.Property(e => e.StatusId).HasColumnName("statusid");
                entity.Property(e => e.ApprovedBy).HasColumnName("approvedby");
                entity.Property(e => e.ApprovedDate).HasColumnName("approveddate");
                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");
                entity.Property(e => e.Createdby).HasColumnName("createdby");
                entity.Property(e => e.Createddate).HasColumnName("createddate");
                entity.Property(e => e.Updatedby).HasColumnName("updatedby");
                entity.Property(e => e.Updateddate).HasColumnName("updateddate");
                    entity.HasOne(e => e.Status)
                    .WithMany()  
                    .HasForeignKey(e => e.StatusId)
                    .OnDelete(DeleteBehavior.Restrict);  
                                        
            });

            modelBuilder.Entity<LeaveRequest>(entity =>
            {
                entity.ToTable("leaverequest");
                entity.Property(e => e.Id)
                 .HasColumnName("id")
                 .ValueGeneratedOnAdd();
                entity.Property(e => e.Idtype).HasColumnName("idtype");
                entity.Property(e => e.Title).HasColumnName("title");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.Iduser).HasColumnName("iduser");
                entity.Property(e => e.Idassignedtomanager).HasColumnName("idassignedtomanager");
                entity.Property(e => e.Datestart).HasColumnName("datestart");
                entity.Property(e => e.Dateend).HasColumnName("dateend");
                entity.Property(e => e.Quantity).HasColumnName("quantity");
                entity.Property(e => e.Idmanagervalidationstatus).HasColumnName("idmanagervalidationstatus");
                entity.Property(e => e.Managercomment).HasColumnName("managercomment");


                entity.Property(e => e.Idhrvalidationstatus).HasColumnName("idhrvalidationstatus");
                entity.Property(e => e.Idassignedtohr).HasColumnName("idassignedtohr");
                entity.Property(e => e.Hrcomment).HasColumnName("hrcomment");
                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");
                entity.Property(e => e.Createdby).HasColumnName("createdby");
                entity.Property(e => e.Createddate).HasColumnName("createddate");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");
                entity.Property(e => e.Updateddate).HasColumnName("updateddate");
                entity.Property(e => e.Managerfullname).HasColumnName("managerfullname");
                entity.Property(e => e.HRfullname).HasColumnName("hrfullname");
                entity.Property(e => e.Validatedbymanger).HasColumnName("validatedbymanger");
                entity.Property(e => e.Validatedbymangerdate).HasColumnName("validatedbymangerdate");

                entity.Property(e => e.Validatedbyhr).HasColumnName("validatedbyhr");
                entity.Property(e => e.Validatedbyhrdate).HasColumnName("validatedbyhrdate");

            });

            modelBuilder.Entity<Projectlots>(entity =>
            {
                entity.ToTable("projectlots");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");

                entity.Property(e => e.Dateend)
                          .HasColumnName("dateend")
                          .HasColumnType("date");

                entity.Property(e => e.Datestart)
                          .HasColumnName("datestart")
                          .HasColumnType("date");

                entity.Property(e => e.Idproject).HasColumnName("idproject");

                entity.Property(e => e.Idstatus).HasColumnName("idstatus");

                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");

                entity.Property(e => e.Lotdescription).HasColumnName("lotdescription");

                entity.Property(e => e.Lotname).HasColumnName("lotname");

                entity.Property(e => e.Originalworkloadestimates).HasColumnName("originalworkloadestimates");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");

                entity.Property(e => e.Workload).HasColumnName("workload");

                entity.HasOne(d => d.Project)
                          .WithMany(p => p.Projectlots)
                          .HasForeignKey(d => d.Idproject)
                          .HasConstraintName("projectlots_idproject_fkey");

                entity.HasOne(d => d.Status)
                          .WithMany(p => p.Projectlots)
                          .HasForeignKey(d => d.Idstatus)
                          .HasConstraintName("projectlots_idstatus_fkey");
            });

            modelBuilder.Entity<Projects>(entity =>
            {
                entity.ToTable("projects");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");

                entity.Property(e => e.Dateend)
                          .HasColumnName("dateend")
                          .HasColumnType("date");

                entity.Property(e => e.Datestart)
                          .HasColumnName("datestart")
                          .HasColumnType("date");

                entity.Property(e => e.DurationEstimated).HasColumnName("durationestimated");

                entity.Property(e => e.FTEEstimated).HasColumnName("fteestimated");

                entity.Property(e => e.IsIdleTime).HasColumnName("isidletime");
                entity.Property(e => e.IsAbsence).HasColumnName("isabsence");

                entity.Property(e => e.IsOffshore).HasColumnName("isoffshore");
                entity.Property(e => e.IsInternalActivities).HasColumnName("isinternalactivities");

                entity.Property(e => e.IdBusinessUnit).HasColumnName("idbusinessunit");
                entity.Property(e => e.IDProjectBilling).HasColumnName("idprojectbilling");

                entity.Property(e => e.Idcustomer).HasColumnName("idcustomer");

                entity.Property(e => e.IdEngagementType).HasColumnName("idengagementtype");

                entity.Property(e => e.IDProjectDirector).HasColumnName("idprojectdirector");

                entity.Property(e => e.Idprojecttype).HasColumnName("idprojecttype");

                entity.Property(e => e.Idstatus).HasColumnName("idstatus");

                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");

                entity.Property(e => e.Isinternalproject).HasColumnName("isinternalproject");

                entity.Property(e => e.IsNotBillableproject).HasColumnName("isnotbillableproject");

                entity.Property(e => e.IsValidated).HasColumnName("isvalidated");

                entity.Property(e => e.Projectdescription).HasColumnName("projectdescription");

                entity.Property(e => e.Projectname).HasColumnName("projectname");

                entity.Property(e => e.TurnOverEstimated).HasColumnName("turnoverestimated");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");

                entity.Property(e => e.ValidatedBy).HasColumnName("validatedby");

                entity.Property(e => e.AverageDailyRate).HasColumnName("averagedailyrate");

                entity.Property(e => e.DaysSold).HasColumnName("dayssold");
                entity.Property(e => e.IsAdmin).HasColumnName("isadministration");

                entity.Property(e => e.ValidatedComment).HasColumnName("validatedcomment");
                entity.Property(e => e.ProjectDocumentInit).HasColumnName("projectdocumentinit");
                entity.Property(e => e.ValidatedDate)
                          .HasColumnName("validateddate")
                          .HasColumnType("date");


                entity.HasOne(d => d.EngagementType)
                           .WithMany(p => p.ProjectsIdEngagementTypeNavigation)
                           .HasForeignKey(d => d.IdEngagementType)
                           .HasConstraintName("projects_idengagementtype_fkey");

                entity.HasOne(d => d.BusinessUnit)
                          .WithMany(p => p.BusinessUnitProjects)
                          .HasForeignKey(d => d.IdBusinessUnit)
                          .HasConstraintName("projects_idbusinessunit_fkey");


                entity.HasOne(d => d.ProjectDirector)
                          .WithMany(p => p.ProjectsAsDirector)
                          .HasForeignKey(d => d.IDProjectDirector)
                          .HasConstraintName("projects_idprojectdirector_fkey");


                entity.HasOne(d => d.ProjectType)
                          .WithMany(p => p.ProjectsIdprojecttypeNavigation)
                          .HasForeignKey(d => d.Idprojecttype)
                          .HasConstraintName("projects_idprojecttype_fkey");

                entity.HasOne(d => d.Status)
                          .WithMany(p => p.ProjectsIdstatusNavigation)
                          .HasForeignKey(d => d.Idstatus)
                          .HasConstraintName("projects_idstatus_fkey");
                entity.HasOne(d => d.ProjectBilling)
                        .WithMany(p => p.Scopes)
                        .HasForeignKey(d => d.IDProjectBilling)
                        .HasConstraintName("projects_idprojectbilling_fkey");
            });


            modelBuilder.Entity<Projecttasks>(entity =>
            {
                entity.ToTable("projecttasks");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.IsOverTime).HasColumnName("isovertime");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");

                entity.Property(e => e.Dateend)
                          .HasColumnName("dateend")
                          .HasColumnType("date");

                entity.Property(e => e.Datestart)
                          .HasColumnName("datestart")
                          .HasColumnType("date");

                entity.Property(e => e.IdAssignedTo).HasColumnName("idassignedto");

                entity.Property(e => e.Idprojectlot).HasColumnName("idprojectlot");

                entity.Property(e => e.Idstatus).HasColumnName("idstatus");

                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");

                entity.Property(e => e.Originalworkloadestimates).HasColumnName("originalworkloadestimates");

                entity.Property(e => e.Remainingwork).HasColumnName("remainingwork");

                entity.Property(e => e.Taskdescription).HasColumnName("taskdescription");

                entity.Property(e => e.Taskname).HasColumnName("taskname");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");

                entity.Property(e => e.Workload).HasColumnName("workload");


                entity.HasOne(d => d.AssignedTo)
                          .WithMany(p => p.TasksUsers)
                          .HasForeignKey(d => d.IdAssignedTo)
                          .HasConstraintName("projecttasks_idassignedto_fkey");

                entity.HasOne(d => d.Projectlot)
                          .WithMany(p => p.Projecttasks)
                          .HasForeignKey(d => d.Idprojectlot)
                          .HasConstraintName("projecttasks_idprojectlot_fkey");

                entity.HasOne(d => d.Status)
                          .WithMany(p => p.Projecttasks)
                          .HasForeignKey(d => d.Idstatus)
                          .HasConstraintName("projecttasks_idstatus_fkey");



            });
            modelBuilder.Entity<Timesheets>(entity =>
            {
                entity.ToTable("timesheets");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");


                entity.Property(e => e.TsScore)
                          .HasColumnName("tsscore")
                          .HasDefaultValue(1);

                entity.Property(e => e.Datevalidation)
                          .HasColumnName("datevalidation")
                          .HasColumnType("date");

                entity.Property(e => e.Idproject).HasColumnName("idproject");

                entity.Property(e => e.Idprojectlot).HasColumnName("idprojectlot");

                entity.Property(e => e.Idtask).HasColumnName("idtask");

                entity.Property(e => e.Iduser).HasColumnName("iduser");

                entity.Property(e => e.Isbillable).HasColumnName("isbillable");

                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");

                entity.Property(e => e.Isvalidated).HasColumnName("isvalidated");
                entity.Property(e => e.Validatedby).HasColumnName("validatedby");
                entity.Property(e => e.IdUserBusinessUnit).HasColumnName("iduserbusinessunit");

                entity.Property(e => e.Tsday)
                          .HasColumnName("tsday")
                          .HasColumnType("date");

                entity.Property(e => e.Tsvalue).HasColumnName("tsvalue");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");

                entity.HasOne(d => d.Project)
                          .WithMany(p => p.Timesheets)
                          .HasForeignKey(d => d.Idproject)
                          .HasConstraintName("timesheets_idproject_fkey");

                entity.HasOne(d => d.UserBusinessUnit)
                     .WithMany(p => p.Timesheetbusnessunits)
                     .HasForeignKey(d => d.IdUserBusinessUnit)
                     .HasConstraintName("timesheets_iduserbusinessunit_fkey");

                entity.HasOne(d => d.ProjectLot)
                          .WithMany(p => p.Timesheets)
                          .HasForeignKey(d => d.Idprojectlot)
                          .HasConstraintName("timesheets_idprojectlot_fkey");

                entity.HasOne(d => d.Task)
                          .WithMany(p => p.Timesheets)
                          .HasForeignKey(d => d.Idtask)
                          .HasConstraintName("timesheets_idtask_fkey");

                entity.HasOne(d => d.User)
                          .WithMany(p => p.UserTimesheet)
                          .HasForeignKey(d => d.Iduser)
                          .HasConstraintName("timesheets_iduser_fkey");

            });
            modelBuilder.Entity<Appareas>(entity =>
            {
                entity.ToTable("appareas");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Areaname).HasColumnName("areaname");

                entity.Property(e => e.Description).HasColumnName("description");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");

                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");
            });
            modelBuilder.Entity<Apppermissions>(entity =>
            {
                entity.ToTable("apppermissions");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Accesslevel)
                          .IsRequired()
                          .HasColumnName("accesslevel")
                          .HasDefaultValueSql("true");

                entity.Property(e => e.Candelete)
                          .IsRequired()
                          .HasColumnName("candelete")
                          .HasDefaultValueSql("true");

                entity.Property(e => e.Canedit)
                          .IsRequired()
                          .HasColumnName("canedit")
                          .HasDefaultValueSql("true");

                entity.Property(e => e.Idarea).HasColumnName("idarea");

                entity.Property(e => e.Idrole).HasColumnName("idrole");

                entity.Property(e => e.Readonly).HasColumnName("readonly");

                entity.HasOne(d => d.IdareaNavigation)
                          .WithMany(p => p.Apppermissions)
                          .HasForeignKey(d => d.Idarea)
                          .HasConstraintName("apppermissions_idarea_fkey");

                entity.HasOne(d => d.IdroleNavigation)
                          .WithMany(p => p.Apppermissions)
                          .HasForeignKey(d => d.Idrole)
                          .HasConstraintName("apppermissions_idrole_fkey");
            });
            modelBuilder.Entity<Approles>(entity =>
            {
                entity.ToTable("approles");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");

                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");

                entity.Property(e => e.Roledescription).HasColumnName("roledescription");

                entity.Property(e => e.Rolename).HasColumnName("rolename");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");
            });
            modelBuilder.Entity<Appuserroles>(entity =>
            {
                entity.ToTable("appuserroles");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");

                entity.Property(e => e.Idroles).HasColumnName("idroles");

                entity.Property(e => e.Iduser).HasColumnName("iduser");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");

                entity.HasOne(d => d.Role)
                          .WithMany(p => p.Appuserroles)
                          .HasForeignKey(d => d.Idroles)
                          .HasConstraintName("appuserroles_idroles_fkey");

                entity.HasOne(d => d.User)
                          .WithMany(p => p.Appuserroles)
                          .HasForeignKey(d => d.Iduser)
                          .HasConstraintName("appuserroles_iduser_fkey");
            });
            modelBuilder.Entity<Appusers>(entity =>
            {
                entity.ToTable("appusers");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.Authentificationfailsnumbers).HasColumnName("authentificationfailsnumbers");

                entity.Property(e => e.Createdby).HasColumnName("createdby");

                entity.Property(e => e.Createddate)
                          .HasColumnName("createddate")
                          .HasDefaultValueSql("CURRENT_DATE");

                entity.Property(e => e.DateExpiry)
                          .HasColumnType("date")
                          .HasColumnName("dateexpiry");

                entity.Property(e => e.DefaultLanguage).HasColumnName("defaultlanguage");

                entity.Property(e => e.Email).HasColumnName("email");

                entity.Property(e => e.Firstname).HasColumnName("firstname");

                entity.Property(e => e.IdBusinessUnit).HasColumnName("idbusinessunit");

                entity.Property(e => e.IdManager).HasColumnName("idmanager");

                entity.Property(e => e.IdOffice).HasColumnName("idoffice");

                entity.Property(e => e.IdTitle).HasColumnName("idtitle");

                entity.Property(e => e.Isactive)
                          .IsRequired()
                          .HasColumnName("isactive")
                          .HasDefaultValueSql("true");

                entity.Property(e => e.Isblocked).HasColumnName("isblocked");

                entity.Property(e => e.Isdeleted).HasColumnName("isdeleted");

                entity.Property(e => e.Isexternaluser).HasColumnName("isexternaluser");

                entity.Property(e => e.Isfirstuse)
                          .IsRequired()
                          .HasColumnName("isfirstuse")
                          .HasDefaultValueSql("true");

                entity.Property(e => e.Isinitpassword)
                          .IsRequired()
                          .HasColumnName("isinitpassword")
                          .HasDefaultValueSql("true");

                entity.Property(e => e.Lastname).HasColumnName("lastname");

                entity.Property(e => e.StartDate).HasColumnName("startdate");

                entity.Property(e => e.Login).HasColumnName("login");

                entity.Property(e => e.Note).HasColumnName("note");

                entity.Property(e => e.Phonenumber).HasColumnName("phonenumber");

                entity.Property(e => e.Updatedby).HasColumnName("updatedby");

                entity.Property(e => e.Updateddate).HasColumnName("updateddate");

                entity.Property(e => e.Userposition).HasColumnName("userposition");

                entity.Property(e => e.Userpwd).HasColumnName("userpwd");

                entity.HasOne(d => d.BusinessUnit)
                          .WithMany(p => p.BusinessUnitTeam)
                          .HasForeignKey(d => d.IdBusinessUnit)
                          .HasConstraintName("fk_businessunit");

                entity.HasOne(d => d.Manager)
                          .WithMany(p => p.ManagerTeam)
                          .HasForeignKey(d => d.IdManager)
                          .HasConstraintName("appusers_idmanager_fkey");
                entity.HasOne(d => d.Title)
                    .WithMany(p => p.UsersTites)

                    .HasForeignKey(d => d.IdTitle)
                    .HasConstraintName("appusers_idtitle_fkey");
                entity.HasOne(d => d.Office)
                  .WithMany(p => p.UsersOffices)
                  .HasForeignKey(d => d.IdOffice)
                  .HasConstraintName("appusers_idoffice_fkey");



            });

        }






    }
   
}