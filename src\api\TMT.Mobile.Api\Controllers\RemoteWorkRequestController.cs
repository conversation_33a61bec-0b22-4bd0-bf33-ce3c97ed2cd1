using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Core.Consts;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Api.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;

namespace TMT.Mobile.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RemoteWorkRequestController : ControllerBase
    {
        private readonly DynamicDbContext _context;
        private readonly RemoteWorkRequestRepository _remoteWorkRequestRepository;
        private readonly AppvariableRepository _appvariableRepository;
        private readonly AppUserRepository _appUserRepository;
        private readonly ILogger<RemoteWorkRequestController> _logger;
        private readonly IEmailService _emailService;

        public RemoteWorkRequestController(
            ILogger<RemoteWorkRequestController> logger,
            DynamicDbContext context,
            RemoteWorkRequestRepository remoteWorkRequestRepository,
            AppvariableRepository appvariableRepository,
            AppUserRepository appUserRepository,
            IEmailService emailService)
        {
            _context = context;
            _remoteWorkRequestRepository = remoteWorkRequestRepository;
            _appvariableRepository = appvariableRepository;
            _appUserRepository = appUserRepository;
            _logger = logger;
            _emailService = emailService;
        }

        private string GetUserEmail()
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email);
            if (emailClaim == null)
                throw new UnauthorizedAccessException("Email not found in token.");

            return emailClaim.Value;
        }

        [HttpPost]
        public async Task<IActionResult> CreateRemoteWorkRequest([FromBody] RemoteWorkRequestDto requestDto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var email = GetUserEmail();
                var submittedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Submitted");
                if (!submittedStatusId.HasValue)
                    return BadRequest("Status 'Submitted' not found.");

                var employee = await _context.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new {
                        u.Id,
                        u.Firstname,
                        u.Lastname,
                        u.Email
                    })
                    .FirstOrDefaultAsync();

                if (employee == null)
                {
                    _logger.LogWarning("Employee with email {Email} not found.", email);
                    return BadRequest("Employee not found.");
                }

                dynamic manager = null;
                
                if (requestDto.AssignedToManagerId.HasValue)
                {
                    manager = await _context.Appusers
                        .Where(u => u.Id == requestDto.AssignedToManagerId)
                        .Select(u => new {
                            u.Id,
                            u.Firstname,
                            u.Lastname,
                            u.Email,
                            u.DefaultLanguage
                        })
                        .FirstOrDefaultAsync();
                }
                else
                {
                    var managerDto = await _appUserRepository.GetManagerByUserEmail(email);
                    if (managerDto != null)
                    {
                        manager = new {
                            Id = managerDto.Id,
                            Firstname = managerDto.FirstName,
                            Lastname = managerDto.LastName,
                            Email = managerDto.Email,
                            DefaultLanguage = managerDto.DefaultLanguage
                        };
                    }
                }

                var remoteWorkRequest = new RemoteWorkRequest
                {
                    RequestorId = employee.Id,
                    AssignedToManagerId = manager?.Id ?? requestDto.AssignedToManagerId,
                    Quantity = requestDto.Quantity,
                    DateStart = requestDto.DateStart,
                    StatusId = submittedStatusId.Value,
                    Createddate = DateTime.UtcNow,
                    Updateddate = DateTime.UtcNow
                };

                await _remoteWorkRequestRepository.AddAsync(remoteWorkRequest);

                if (manager != null && !string.IsNullOrEmpty(manager.Email))
                {
                    await SendNewRemoteWorkRequestEmail(employee, manager, remoteWorkRequest, manager.DefaultLanguage);
                }

                return Ok(new
                {
                    Message = "Remote work request created successfully.",
                    RequestId = remoteWorkRequest.Id
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating remote work request.");
                return StatusCode(500, "An unexpected error occurred while creating the request.");
            }
        }

private async Task SendNewRemoteWorkRequestEmail(dynamic employee, dynamic manager, RemoteWorkRequest remoteWorkRequest, string? defaultLanguage)
{
    try
    {
        string language = (defaultLanguage?.ToLower()) switch
        {
            "en" => "en",
            "fr" => "fr",
            _ => "fr"
        };

        string emailSubject;
        string emailBody;

        if (language == "en")
        {
            emailSubject = $"New Remote Work Request - {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Remote Work Request Notification</h2>
                    <p>Dear {manager.Firstname},</p>

                    <p>You have received a new remote work request from {employee.Firstname} {employee.Lastname}:</p>

                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 15px 0;'>
                        <p><strong>Request Details:</strong></p>
                        <ul style='list-style-type: none; padding-left: 0;'>
                            <li><strong>Start Date:</strong> {remoteWorkRequest.DateStart:dd/MM/yyyy}</li>
                            <li><strong>Duration:</strong> {remoteWorkRequest.Quantity} day(s)</li>
                            <li><strong>Submission Date:</strong> {DateTime.UtcNow:dd/MM/yyyy HH:mm}</li>
                        </ul>
                    </div>

                    <p>Please review this request at your earliest convenience in the HR Management System.</p>

                    <p>Thank you,</p>
                    <p><strong>Human Resources Department</strong><br/></p>
                </div>";
        }
        else
        {
            emailSubject = $"Nouvelle demande de télétravail - {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Notification de demande de télétravail</h2>
                    <p>Cher/Chère {manager.Firstname},</p>

                    <p>Vous avez reçu une nouvelle demande de télétravail de la part de {employee.Firstname} {employee.Lastname} :</p>

                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 15px 0;'>
                        <p><strong>Détails de la demande :</strong></p>
                        <ul style='list-style-type: none; padding-left: 0;'>
                            <li><strong>Date de début :</strong> {remoteWorkRequest.DateStart:dd/MM/yyyy}</li>
                            <li><strong>Durée :</strong> {remoteWorkRequest.Quantity} jour(s)</li>
                            <li><strong>Date de soumission :</strong> {DateTime.UtcNow:dd/MM/yyyy HH:mm}</li>
                        </ul>
                    </div>

                    <p>Merci de bien vouloir examiner cette demande dans les plus brefs délais via le système de gestion RH.</p>

                    <p>Merci,</p>
                    <p><strong>Département des Ressources Humaines</strong><br/></p>
                </div>";
        }

        await _emailService.SendEmailAsync(manager.Email, emailSubject, emailBody);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to send remote work request email");
    }
}


        [HttpGet("assigned-to-me")]
        [Authorize]
        public async Task<IActionResult> GetAssignedRemoteWorks()
        {
            try
            {
                var email = GetUserEmail();
                
                var user = await _context.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new { u.Id })
                    .FirstOrDefaultAsync();
                    
                if (user == null)
                    return NotFound("Utilisateur non trouvé");
                    
                var userId = user.Id;
                var requests = await _remoteWorkRequestRepository.GetAssignedToUserAsync(userId);
                
                return Ok(requests);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "Échec de la récupération des demandes de travail à distance assignées",
                    Error = ex.Message
                });
            }
        }

        [HttpGet("my-remote-work")]
        [Authorize]
        public async Task<IActionResult> GetMyRemoteWorks()
        {
            try
            {
                var email = GetUserEmail();
                var user = await _context.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new { u.Id })
                    .FirstOrDefaultAsync();
                    
                if (user == null)
                    return NotFound("Utilisateur non trouvé");
                    
                var userId = user.Id;
                var myRequests = await _remoteWorkRequestRepository.GetByUserIdAsync(userId);
                
                return Ok(myRequests);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "Échec de la récupération de vos demandes de travail à distance",
                    Error = ex.Message
                });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAllRemoteWorkRequest()
        {
            try
            {
                var requests = await _remoteWorkRequestRepository.GetAllAsync();
                return Ok(requests.Select(r => new
                {
                    r.Id,
                    r.RequestorId,
                    r.AssignedToManagerId,
                    r.Quantity,
                    r.DateStart,
                    r.StatusId,
                    StatusName = r.Status?.Name,
                    r.ApprovedBy,
                    r.ApprovedDate,
                    r.Createddate,
                    r.Updateddate
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving requests");
                return StatusCode(500, "Error retrieving data");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetRemoteWorkRequestById(int id)
        {
            try
            {
                var request = await _remoteWorkRequestRepository.GetByIdAsync(id);
                if (request == null)
                    return NotFound("Request not found");

                return Ok(new
                {
                    request.Id,
                    request.RequestorId,
                    request.AssignedToManagerId,
                    request.Quantity,
                    request.DateStart,
                    request.StatusId,
                    StatusName = request.Status?.Name,
                    request.ApprovedBy,
                    request.ApprovedDate,
                    request.Createddate,
                    request.Updateddate
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving request");
                return StatusCode(500, "Error retrieving request");
            }
        }

       [HttpPut("{id}")]
[Authorize]
public async Task<IActionResult> UpdateRemoteWorkRequest(int id, [FromBody] RemoteWorkRequestDto requestDto)
{
    if (!ModelState.IsValid)
        return BadRequest(ModelState);

    try
    {
        var email = GetUserEmail();
        
        var employee = await _context.Appusers
            .Where(u => u.Email == email)
            .Select(u => new { 
                u.Id,
                u.Firstname,
                u.Lastname,
                u.Email
            })
            .FirstOrDefaultAsync();
                
        if (employee == null)
            return NotFound("Utilisateur non trouvé");
                
        var userId = employee.Id;
        var existingRequest = await _remoteWorkRequestRepository.GetByIdAsync(id);

        if (existingRequest == null)
            return NotFound("Request not found");

        if (existingRequest.RequestorId != userId)
            return Forbid("You can only modify your own requests");

        if (existingRequest.Status?.Name == "Approved" || existingRequest.Status?.Name == "Rejected")
            return BadRequest("Cannot modify an already processed request");

        var submittedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Submitted");
        if (submittedStatusId.HasValue)
        {
            existingRequest.StatusId = submittedStatusId.Value;
        }

        dynamic manager = null;
        if (requestDto.AssignedToManagerId.HasValue)
        {
            manager = await _context.Appusers
                .Where(u => u.Id == requestDto.AssignedToManagerId)
                .Select(u => new {
                    u.Id,
                    u.Firstname,
                    u.Lastname,
                    u.Email,
                    u.DefaultLanguage
                })
                .FirstOrDefaultAsync();
        }
        else
        {
            var managerDto = await _appUserRepository.GetManagerByUserEmail(email);
            if (managerDto != null)
            {
                manager = new {
                    Id = managerDto.Id,
                    Firstname = managerDto.FirstName,
                    Lastname = managerDto.LastName,
                    Email = managerDto.Email,
                    DefaultLanguage = managerDto.DefaultLanguage
                };
            }
        }

        existingRequest.AssignedToManagerId = requestDto.AssignedToManagerId;
        existingRequest.Quantity = requestDto.Quantity;
        existingRequest.DateStart = requestDto.DateStart;
        existingRequest.Updateddate = DateTime.UtcNow;

        await _remoteWorkRequestRepository.UpdateAsync(existingRequest);

        if (manager != null && !string.IsNullOrEmpty(manager.Email))
        {
            await SendRemoteWorkUpdateEmail(employee, manager, existingRequest, manager.DefaultLanguage);
        }

        return Ok(new { 
            Message = "Request updated successfully", 
            RequestId = existingRequest.Id 
        });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error updating request");
        return StatusCode(500, "Error updating request: " + ex.Message);
    }
}

private async Task SendRemoteWorkUpdateEmail(dynamic employee, dynamic manager, RemoteWorkRequest request, string? defaultLanguage)
{
    try
    {
        string language = (defaultLanguage?.ToLower()) switch
        {
            "en" => "en",
            "fr" => "fr",
            _ => "fr" 
        };

        string emailSubject;
        string emailBody;

        if (language == "en")
        {
            emailSubject = $"Updated Remote Work Request - {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Remote Work Request Update Notification</h2>
                    <p>Dear {manager.Firstname} {manager.Lastname},</p>
                    <p>A remote work request from your team member has been updated:</p>
                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #e74c3c; margin: 15px 0;'>
                        <p><strong>Employee:</strong> {employee.Firstname} {employee.Lastname}</p>
                        <p><strong>Start Date:</strong> {request.DateStart:dd/MM/yyyy}</p>
                        <p><strong>Duration:</strong> {request.Quantity} day(s)</p>
                    </div>
                    <p>Please review this updated remote work request in the HR Management System.</p>
                    <p>Best regards,<br><strong>Human Resources Department</strong></p>
                </div>";
        }
        else
        {
            emailSubject = $"Demande de Télétravail Modifiée - {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Notification de Modification de Demande de Télétravail</h2>
                    <p>Bonjour {manager.Firstname} {manager.Lastname},</p>
                    <p>Une demande de télétravail de votre collaborateur a été modifiée :</p>
                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #e74c3c; margin: 15px 0;'>
                        <p><strong>Employé :</strong> {employee.Firstname} {employee.Lastname}</p>
                        <p><strong>Date de début :</strong> {request.DateStart:dd/MM/yyyy}</p>
                        <p><strong>Durée :</strong> {request.Quantity} jour(s)</p>
                    </div>
                    <p>Merci de consulter cette demande de télétravail mise à jour dans le système de gestion RH.</p>
                    <p>Cordialement,<br><strong>Service RH</strong></p>
                </div>";
        }

        await _emailService.SendEmailAsync(manager.Email, emailSubject, emailBody);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to send remote work update email");
    }
}

        [HttpDelete("{id}")]
        [Authorize]
        public async Task<IActionResult> DeleteRemoteWorkRequest(int id)
        {
            try
            {
                var email = GetUserEmail();
                var user = await _context.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new { u.Id })
                    .FirstOrDefaultAsync();
                    
                if (user == null)
                    return NotFound("Utilisateur non trouvé");
                    
                var userId = user.Id;
                var existingRequest = await _remoteWorkRequestRepository.GetByIdAsync(id);

                if (existingRequest == null)
                    return NotFound("Request not found");

                if (existingRequest.RequestorId != userId)
                    return Forbid("You can only delete your own requests");

                existingRequest.Isdeleted = true;
                existingRequest.Updateddate = DateTime.UtcNow;

                await _remoteWorkRequestRepository.UpdateAsync(existingRequest);

                return Ok(new { Message = "Request deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting request");
                return StatusCode(500, "Error deleting request: " + ex.Message);
            }
        }

        [HttpPut("{id}/accept")]
        [Authorize]
        public async Task<IActionResult> AcceptRemoteWorkRequest(int id, [FromBody] MangerAssigmentRemoteDto dto)
        {
            try
            {
                var request = await _remoteWorkRequestRepository.GetByIdAsync(id);

                if (request == null)
                    return NotFound("Request not found");

                var approvedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Approved");
                if (!approvedStatusId.HasValue)
                    return BadRequest("Approved status not found");

                var rejectedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Rejected");
                var canceledStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Canceled");

                if (request.StatusId == approvedStatusId.Value)
                    return BadRequest("Request already approved");

                if (request.StatusId == rejectedStatusId || request.StatusId == canceledStatusId)
                    return BadRequest("Cannot approve a rejected or canceled request");

                request.StatusId = approvedStatusId.Value;
                request.ApprovedDate = DateTime.UtcNow;
                request.Updateddate = DateTime.UtcNow;
                request.Comment = dto.Comment;

                await _remoteWorkRequestRepository.UpdateAsync(request);

                await SendRemoteWorkStatusUpdateEmail(request.RequestorId, request, "Approved", "Manager");

                return Ok(new {
                    Message = "Request approved successfully",
                    Status = "Approved",
                    request
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving request");
                return StatusCode(500, "Error approving request");
            }
        }

        [HttpPut("{id}/reject")]
        [Authorize]
        public async Task<IActionResult> RejectRemoteWorkRequest(int id, [FromBody] MangerAssigmentRemoteDto dto)
        {
            try
            {
                var request = await _remoteWorkRequestRepository.GetByIdAsync(id);

                if (request == null)
                    return NotFound("Request not found");

                var rejectedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Rejected");
                if (!rejectedStatusId.HasValue)
                    return BadRequest("Rejected status not found");

                var approvedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Approved");
                var canceledStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Canceled");

                if (request.StatusId == approvedStatusId || request.StatusId == canceledStatusId)
                    return BadRequest("Cannot reject an approved or canceled request");

                request.StatusId = rejectedStatusId.Value;
                request.Updateddate = DateTime.UtcNow;
                request.Comment = dto.Comment;

                await _remoteWorkRequestRepository.UpdateAsync(request);

                await SendRemoteWorkStatusUpdateEmail(request.RequestorId, request, "Rejected", "Manager");

                return Ok(new {
                    Message = "Request rejected successfully",
                    Status = "Rejected",
                    request
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting request");
                return StatusCode(500, "Error rejecting request");
            }
        }

        private async Task SendRemoteWorkStatusUpdateEmail(int requestorId, RemoteWorkRequest request, string statusLabel, string actorRole)
{
    try
    {
        var user = await GetUserBasicInfo(requestorId);
        if (user?.Email == null) return;

        string lang = user.DefaultLanguage?.ToLower() ?? "fr";

        var statusColor = statusLabel == "Approved" ? "#27ae60" : "#e74c3c";

        string emailSubject;
        string emailBody;

        var managerCommentSection = string.IsNullOrWhiteSpace(request.Comment)
            ? ""
            : (lang == "en"
                ? $"<p><strong>Manager's Comment:</strong> {request.Comment}</p>"
                : $"<p><strong>Commentaire du manager :</strong> {request.Comment}</p>");

        if (lang == "en")
        {
            emailSubject = $"Remote Work Request Update: {statusLabel}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Remote Work Request Status Update</h2>
                    <p>Dear {user.Firstname} {user.Lastname},</p>
                    
                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid {statusColor}; margin: 15px 0;'>
                        <p><strong>Status:</strong> <span style='color: {statusColor};'>{statusLabel}</span></p>
                        <p><strong>Request Date:</strong> {request.DateStart:dd/MM/yyyy}</p>
                        <p><strong>Duration:</strong> {request.Quantity} day(s)</p>
                        {managerCommentSection}
                    </div>
                    
                    <p>You can view the details of this request in the HR Management System.</p>
                    
                    <p>Best regards,</p>
                    <p><strong>Human Resources Department</strong><br/></p>
                </div>";
        }
        else
        {
            emailSubject = $"Mise à jour de la demande de télétravail : {statusLabel}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Mise à jour du statut de la demande de télétravail</h2>
                    <p>Cher/Chère {user.Firstname} {user.Lastname},</p>
                    
                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid {statusColor}; margin: 15px 0;'>
                        <p><strong>Statut :</strong> <span style='color: {statusColor};'>{statusLabel}</span></p>
                        <p><strong>Date de la demande :</strong> {request.DateStart:dd/MM/yyyy}</p>
                        <p><strong>Durée :</strong> {request.Quantity} jour(s)</p>
                        {managerCommentSection}
                    </div>

                    <p>Vous pouvez consulter les détails de cette demande dans le système de gestion RH.</p>

                    <p>Cordialement,</p>
                    <p><strong>Département des Ressources Humaines</strong><br/></p>
                </div>";
        }

        await _emailService.SendEmailAsync(user.Email, emailSubject, emailBody);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to send status update email");
    }
}


       [HttpPut("{id}/reassign-manager")]
[Authorize]
public async Task<IActionResult> ReassignRemoteWorkManager(int id, [FromBody] MangerAssigmentRemoteDto dto)
{
    try
    {
        var request = await _remoteWorkRequestRepository.GetByIdAsync(id);
        if (request == null)
            return NotFound("Remote work request not found.");

        int newManagerId = dto.AssignedToManagerId ?? request.AssignedToManagerId ?? throw new Exception("No manager ID provided.");

        var newManager = await _context.Appusers
            .Where(u => u.Id == newManagerId)
            .Select(u => new { u.Id, u.Firstname, u.Lastname, u.Email, Language = u.DefaultLanguage })
            .FirstOrDefaultAsync();

        if (newManager == null)
            return BadRequest("Assigned manager not found.");

        request.AssignedToManagerId = newManager.Id;
        request.Comment = dto.Comment;
        request.Updateddate = DateTime.UtcNow;

        await _remoteWorkRequestRepository.UpdateAsync(request);

        var employee = await _context.Appusers
            .Where(u => u.Id == request.RequestorId)
            .Select(u => new { u.Firstname, u.Lastname, u.Email, Language = u.DefaultLanguage })
            .FirstOrDefaultAsync();

        if (employee == null)
            return BadRequest("Employee not found.");

        Func<string, string> formatComment = lang =>
            string.IsNullOrWhiteSpace(request.Comment)
                ? ""
                : $"<p><strong>{(lang == "en" ? "Note" : "Note du manager")}:</strong> {request.Comment}</p>";

        string langManager = newManager.Language?.ToLower() == "en" ? "en" : "fr";

        string managerSubject = langManager == "en"
            ? $"New Remote Work Request Assignment - {employee.Firstname} {employee.Lastname}"
            : $"Nouvelle assignation de demande - {employee.Firstname} {employee.Lastname}";

        string commentManagerSection = formatComment(langManager);

        string managerBody = langManager == "en"
            ? $@"
                <div style='font-family:Arial,sans-serif;color:#333;'>
                <h2 style='color:#2c3e50;'>New Request Assignment</h2>
                <p>Dear {newManager.Firstname} {newManager.Lastname},</p>
                <p>You have been assigned a new remote work request to review:</p>
                <div style='background:#f8f9fa;padding:15px;border-left:4px solid #3498db;margin:15px 0;'>
                    <p><strong>Employee:</strong> {employee.Firstname} {employee.Lastname}</p>
                    <p><strong>Request Date:</strong> {request.DateStart:dd/MM/yyyy}</p>
                    <p><strong>Duration:</strong> {request.Quantity} day(s)</p>
                    {commentManagerSection}
                </div>
                <p>Please log in to the HR Management System to review this request.</p>
                <p>Thank you.</p>
                <p><strong>Human Resources Department</strong></p>
                </div>"
            : $@"
                <div style='font-family:Arial,sans-serif;color:#333;'>
                <h2 style='color:#2c3e50;'>Nouvelle assignation de demande</h2>
                <p>Bonjour {newManager.Firstname} {newManager.Lastname},</p>
                <p>Une nouvelle demande de télétravail vous a été assignée :</p>
                <div style='background:#f8f9fa;padding:15px;border-left:4px solid #3498db;margin:15px 0;'>
                    <p><strong>Employé :</strong> {employee.Firstname} {employee.Lastname}</p>
                    <p><strong>Date de début :</strong> {request.DateStart:dd/MM/yyyy}</p>
                    <p><strong>Durée :</strong> {request.Quantity} jour(s)</p>
                    <p><strong>Date de soumission :</strong> {DateTime.UtcNow:dd/MM/yyyy HH:mm}</p>
                    {commentManagerSection}
                </div>
                <p>Veuillez vous connecter au système RH pour consulter cette demande.</p>
                <p>Merci.</p>
                <p><strong>Service RH</strong></p>
                </div>";

        await _emailService.SendEmailAsync(newManager.Email, managerSubject, managerBody);

        var employeeSubject = "Your Remote Work Request Has Been Reassigned";
        var employeeBody = $@"
            <div style='font-family: Arial, sans-serif; color: #333;'>
                <h2 style='color: #2c3e50;'>Request Reassignment Notification</h2>
                <p>Dear {employee.Firstname} {employee.Lastname},</p>
                <p>Your remote work request has been reassigned to a new manager:</p>
                <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 15px 0;'>
                    <p><strong>New Manager:</strong> {newManager.Firstname} {newManager.Lastname}</p>
                    <p><strong>Request Date:</strong> {request.DateStart:dd/MM/yyyy}</p>
                    <p><strong>Duration:</strong> {request.Quantity} day(s)</p>
                    {(string.IsNullOrWhiteSpace(request.Comment) ? "" : $"<p><strong>Note:</strong> {request.Comment}</p>")}
                </div>
                <p>You will be notified once your request has been reviewed.</p>
                <p>Best regards,</p>
                <p><strong>Human Resources Department</strong><br/></p>
            </div>";

        await _emailService.SendEmailAsync(employee.Email, employeeSubject, employeeBody);

        return Ok(new
        {
            Message = "Remote work request updated. Notifications sent to manager and employee.",
            RemoteWorkDetails = request
        });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error while reassigning manager.");
        return StatusCode(500, "An error occurred while processing your request.");
    }
}

        [HttpPut("{id}/cancel")]
        [Authorize]
        public async Task<IActionResult> CancelRemoteWorkRequest(int id, [FromBody] MangerAssigmentRemoteDto dto)
        {
            try
            {
                var request = await _remoteWorkRequestRepository.GetByIdAsync(id);
                
                if (request == null)
                    return NotFound("Remote work request not found.");

                var canceledStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Canceled");
                if (!canceledStatusId.HasValue)
                    return BadRequest("Canceled status not found.");

                request.StatusId = canceledStatusId.Value;
                request.Comment = dto.Comment;
                request.Updateddate = DateTime.UtcNow;

                await _remoteWorkRequestRepository.UpdateAsync(request);

                await SendRemoteWorkStatusUpdateEmail(request.RequestorId, request, "Canceled", "Manager");

                return Ok(new {
                    Message = "Remote work request canceled successfully",
                    request
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling request");
                return StatusCode(500, "An error occurred while canceling the request.");
            }
        }

        private async Task<dynamic> GetUserBasicInfo(int userId)
        {
            return await _context.Appusers
                .Where(u => u.Id == userId)
                .Select(u => new {
                    u.Id,
                    u.Firstname,
                    u.Lastname,
                    u.Email,
                    u.DefaultLanguage
                })
                .FirstOrDefaultAsync();
        }
    }
}
