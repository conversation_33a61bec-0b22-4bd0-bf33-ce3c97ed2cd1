import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/user_mood_controller.dart';
import 'package:tmt_mobile/utils/myColors.dart';

class UserMoodScreen extends StatelessWidget {
  final UserMoodController controller = Get.put(UserMoodController());
  final TextEditingController commentController = TextEditingController();
  final GlobalController global = Get.find<GlobalController>();

  UserMoodScreen({super.key});

  String getTranslation(String key) {
    if (global.lang.value == "fr") {
      switch (key) {
        case 'submit anonymously':
          return 'Soumettre anonymement';
        case 'comment':
          return 'Commentaire (Optionnel)';
        case 'confirm':
          return 'Soumettre';
        case 'reset':
          return 'Réinitialiser';
        case 'mood_title':
          return 'Comment vous sentez-vous aujourd\'hui ?';
        case 'sad':
          return 'Triste';
        case 'neutral':
          return 'Neutre';
        case 'happy':
          return 'Heureux';
        default:
          return key;
      }
    } else {
      switch (key) {
        case 'mood_title':
          return 'How are you feeling today?';
        case 'sad':
          return 'Sad';
        case 'neutral':
          return 'Neutral';
        case 'happy':
          return 'Happy';
        case 'submit anonymously':
          return 'Submit anonymously';
        case 'comment':
          return 'Comment (Optional)';
        case 'confirm':
          return 'Submit';
        case 'reset':
          return 'Reset';
        default:
          return key;
      }
    }
  }

  Widget _buildFormCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedSecond.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    icon,
                    color: MyColors.MainRedSecond,
                    size: 14,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildMoodOption({
    required String emoji,
    required String label,
    required int score,
    required Color selectedColor,
  }) {
    return Obx(() => GestureDetector(
          onTap: () {
            controller.userMood.update((mood) {
              mood?.score = score;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: controller.userMood.value.score == score
                  ? selectedColor.withOpacity(0.1)
                  : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: controller.userMood.value.score == score
                    ? selectedColor
                    : Colors.grey[300]!,
                width: controller.userMood.value.score == score ? 2 : 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  emoji,
                  style: TextStyle(fontSize: 32),
                ),
                SizedBox(height: 4),
                Obx(() => Text(
                      label,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: controller.userMood.value.score == score
                            ? selectedColor
                            : Colors.black87,
                      ),
                    )),
              ],
            ),
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    void resetForm() {
      controller.userMood.update((mood) {
        mood?.isAnonymous = true;
        mood?.comment = '';
        mood?.score = null;
      });
      commentController.clear();
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section

            // Form Section
            Padding(
              padding: EdgeInsets.all(12),
              child: Column(
                children: [
                  // Mood Selection Card
                  Obx(() => _buildFormCard(
                        title: global.lang.value == "fr"
                            ? 'Sélectionnez votre humeur'
                            : 'Select your mood',
                        icon: Icons.sentiment_satisfied,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: _buildMoodOption(
                                emoji: '😢',
                                label: getTranslation('sad'),
                                score: 1,
                                selectedColor: Colors.red,
                              ),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: _buildMoodOption(
                                emoji: '😐',
                                label: getTranslation('neutral'),
                                score: 3,
                                selectedColor: MyColors.yellow,
                              ),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: _buildMoodOption(
                                emoji: '😊',
                                label: getTranslation('happy'),
                                score: 5,
                                selectedColor: Colors.green,
                              ),
                            ),
                          ],
                        ),
                      )),

                  // Anonymous Toggle Card
                  Obx(() => _buildFormCard(
                        title: global.lang.value == "fr"
                            ? 'Options de confidentialité'
                            : 'Privacy Options',
                        icon: Icons.privacy_tip,
                        child: Obx(() => Container(
                              padding: EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      getTranslation('submit anonymously'),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ),
                                  Switch(
                                    value:
                                        controller.userMood.value.isAnonymous,
                                    onChanged: (value) {
                                      controller.userMood.update((mood) {
                                        mood?.isAnonymous = value;
                                      });
                                    },
                                    activeColor: MyColors.MainRedSecond,
                                  ),
                                ],
                              ),
                            )),
                      )),

                  // Comment Card
                  Obx(() => _buildFormCard(
                        title: getTranslation('comment'),
                        icon: Icons.comment,
                        child: TextFormField(
                          controller: commentController,
                          maxLines: 3,
                          decoration: InputDecoration(
                            hintText: global.lang.value == "fr"
                                ? 'Partagez vos pensées...'
                                : 'Share your thoughts...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                  color: MyColors.MainRedSecond, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 10),
                          ),
                          onChanged: (value) {
                            controller.userMood.update((mood) {
                              mood?.comment = value;
                            });
                          },
                        ),
                      )),

                  // Action Buttons
                  SizedBox(height: 12),
                  Obx(() => controller.isLoading.value
                      ? Container(
                          height: 44,
                          child: Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  MyColors.MainRedSecond),
                            ),
                          ),
                        )
                      : Row(
                          children: [
                            // Reset Button
                            Expanded(
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(8),
                                    onTap: resetForm,
                                    child: Center(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.refresh,
                                            color: Colors.black54,
                                            size: 16,
                                          ),
                                          SizedBox(width: 6),
                                          Obx(() => Text(
                                                getTranslation('reset'),
                                                style: TextStyle(
                                                  color: Colors.black54,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              )),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 12),
                            // Submit Button
                            Expanded(
                              flex: 2,
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                    colors: [
                                      MyColors.MainRedSecond,
                                      MyColors.MainRedSecond.withOpacity(0.8),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                  boxShadow: [
                                    BoxShadow(
                                      color: MyColors.MainRedSecond.withOpacity(
                                          0.2),
                                      blurRadius: 6,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(8),
                                    onTap: () {
                                      controller.submitUserMood(
                                          controller.userMood.value);
                                    },
                                    child: Center(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.send,
                                            color: Colors.white,
                                            size: 16,
                                          ),
                                          SizedBox(width: 6),
                                          Obx(() => Text(
                                                getTranslation('confirm'),
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              )),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )),
                  SizedBox(height: 12),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
