import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';

class OrgContainer extends StatelessWidget {
  final String name;
  final bool? clicked;
  final void Function()? clickme;

  const OrgContainer({
    super.key,
    required this.name,
    this.clicked,
    this.clickme,
  });

  // Translation helper method
  String _translate(String key, GlobalController global) {
    if (global.lang.value == "fr") {
      switch (key) {
        case 'selected':
          return 'Sélectionné';
        case 'tap_to_select':
          return 'Appuyez pour sélectionner';
        default:
          return key;
      }
    } else {
      switch (key) {
        case 'selected':
          return 'Selected';
        case 'tap_to_select':
          return 'Tap to select';
        default:
          return key;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final GlobalController global = Get.find<GlobalController>();

    return GestureDetector(
      onTap: clickme,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: clicked == true
                ? MyColors.MainRedBig
                : Colors.grey.withOpacity(0.2),
            width: clicked == true ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: clicked == true
                  ? MyColors.MainRedBig.withOpacity(0.15)
                  : Colors.black.withOpacity(0.08),
              blurRadius: clicked == true ? 12 : 8,
              offset: Offset(0, clicked == true ? 6 : 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Row(
            children: [
              // Modern Icon Container
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: clicked == true
                      ? MyColors.MainRedBig.withOpacity(0.1)
                      : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.business_outlined,
                  size: 24,
                  color:
                      clicked == true ? MyColors.MainRedBig : Colors.grey[600],
                ),
              ),
              SizedBox(width: 16),

              // Organization Name
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: clicked == true
                            ? MyColors.MainRedBig
                            : Colors.black87,
                      ),
                    ),
                    SizedBox(height: 4),
                    Obx(() => Text(
                          clicked == true
                              ? _translate('selected', global)
                              : _translate('tap_to_select', global),
                          style: TextStyle(
                            fontSize: 12,
                            color: clicked == true
                                ? MyColors.MainRedBig.withOpacity(0.7)
                                : Colors.grey[500],
                            fontWeight: FontWeight.w500,
                          ),
                        )),
                  ],
                ),
              ),

              // Selection Indicator
              if (clicked == true)
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedBig,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.check,
                    size: 16,
                    color: Colors.white,
                  ),
                )
              else
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.radio_button_unchecked,
                    size: 16,
                    color: Colors.grey[400],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
