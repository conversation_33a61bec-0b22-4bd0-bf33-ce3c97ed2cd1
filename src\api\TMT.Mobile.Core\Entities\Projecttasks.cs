﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Core.Entities
{
    public partial class Projecttasks :IEntity
    {
        public Projecttasks()
        {
            Timesheets = new HashSet<Timesheets>();
          
        }

        public int Id { get; set; }
        public string Taskname { get; set; }
        public string? Taskdescription { get; set; }
        public int? Idprojectlot { get; set; }
        public int? Idstatus { get; set; }
        public double? Originalworkloadestimates { get; set; }
        public double? Workload { get; set; }
        public double? Remainingwork { get; set; }
        public DateTime? Datestart { get; set; }
        public DateTime? Dateend { get; set; }
        public bool Isdeleted { get; set; }
        public string? Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string? Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }
        public int? IdAssignedTo { get; set; }


        public virtual Projectlots Projectlot { get; set; }
        public virtual Appvariables Status { get; set; }
        public virtual Appusers AssignedTo { get; set; }
        public virtual ICollection<Timesheets> Timesheets { get; set; }

        public bool IsOverTime { get; set; }

     
        [NotMapped]
        public string TagsToString { get; set; }
    }
}
