import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/menucontroller.dart';
import 'package:tmt_mobile/controllers/remote_work_request_controller.dart';
import 'package:tmt_mobile/models/remote_work_request_model.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/widgets/big_text.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class RemoteWorkRequestScreen extends StatefulWidget {
  const RemoteWorkRequestScreen({super.key});

  @override
  _RemoteWorkRequestScreenState createState() =>
      _RemoteWorkRequestScreenState();
}

class _RemoteWorkRequestScreenState extends State<RemoteWorkRequestScreen> {
  final RemoteWorkRequestController controller =
      Get.put(RemoteWorkRequestController());
  final Menucontroller menuController = Get.find<Menucontroller>();
  final GlobalController global = Get.find<GlobalController>();
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _dateStartController = TextEditingController();
  DateTime _startDate = DateTime.now();
  int? _selectedManager;
  int? _currentUserId;

  @override
  void initState() {
    super.initState();
    _dateStartController.text = _formatDate(_startDate);
    _fetchCurrentUserId();
  }

  Future<void> _fetchCurrentUserId() async {
    final storage = FlutterSecureStorage();
    var userId = await storage.read(key: "userId");
    setState(() {
      _currentUserId = int.tryParse(userId ?? '0');
    });
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildFormCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedSecond.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    icon,
                    color: MyColors.MainRedSecond,
                    size: 14,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            child,
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Form Section
            Padding(
              padding: EdgeInsets.all(12),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Manager Card
                    Obx(() => _buildFormCard(
                          title: global.lang.value == "fr"
                              ? 'Manager assigné'
                              : 'Assigned Manager',
                          icon: Icons.person,
                          child: Obx(() {
                            if (controller.isLoading.value) {
                              return Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      MyColors.MainRedSecond),
                                ),
                              );
                            }
                            return DropdownButtonFormField<int>(
                              value: _selectedManager,
                              items: controller.managers.map((manager) {
                                return DropdownMenuItem<int>(
                                  value: manager.id,
                                  child: Text(
                                      '${manager.firstName} ${manager.lastName}'),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedManager = value;
                                });
                              },
                              decoration: InputDecoration(
                                labelText: global.lang.value == "fr"
                                    ? 'Sélectionner manager'
                                    : 'Select manager',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                      color: MyColors.MainRedSecond, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 10),
                              ),
                            );
                          }),
                        )),
                    // Duration Card
                    Obx(() => _buildFormCard(
                          title:
                              global.lang.value == "fr" ? 'Durée' : 'Duration',
                          icon: Icons.access_time,
                          child: TextFormField(
                            controller: _quantityController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText:
                                  global.lang.value == "fr" ? 'Jours' : 'Days',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                    color: MyColors.MainRedSecond, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return global.lang.value == "fr"
                                    ? 'Veuillez entrer la quantité'
                                    : 'Please enter the quantity';
                              }
                              return null;
                            },
                          ),
                        )),
                    // Date Card
                    Obx(() => _buildFormCard(
                          title: global.lang.value == "fr"
                              ? 'Date de début'
                              : 'Start Date',
                          icon: Icons.calendar_today,
                          child: TextFormField(
                            controller: _dateStartController,
                            readOnly: true,
                            onTap: () async {
                              DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: _startDate,
                                firstDate: DateTime(2000),
                                lastDate: DateTime(2101),
                                builder: (context, child) {
                                  return Theme(
                                    data: Theme.of(context).copyWith(
                                      colorScheme: ColorScheme.light(
                                        primary: MyColors.MainRedSecond,
                                        onPrimary: Colors.white,
                                        surface: Colors.white,
                                        onSurface: Colors.black,
                                      ),
                                    ),
                                    child: child!,
                                  );
                                },
                              );
                              if (picked != null && picked != _startDate) {
                                setState(() {
                                  _startDate = picked;
                                  _dateStartController.text =
                                      _formatDate(_startDate);
                                });
                              }
                            },
                            decoration: InputDecoration(
                              hintText: global.lang.value == "fr"
                                  ? 'Sélectionner date'
                                  : 'Select date',
                              suffixIcon: Container(
                                margin: EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color:
                                      MyColors.MainRedSecond.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Icon(
                                  Icons.calendar_today,
                                  color: MyColors.MainRedSecond,
                                  size: 16,
                                ),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                    color: MyColors.MainRedSecond, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return global.lang.value == "fr"
                                    ? 'Veuillez sélectionner une date'
                                    : 'Please select a date';
                              }
                              return null;
                            },
                          ),
                        )),
                    // Submit Button
                    SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      height: 44,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            MyColors.MainRedSecond,
                            MyColors.MainRedSecond.withOpacity(0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: MyColors.MainRedSecond.withOpacity(0.2),
                            blurRadius: 6,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            if (_formKey.currentState!.validate()) {
                              double quantity =
                                  double.parse(_quantityController.text);
                              RemoteWorkRequest request = RemoteWorkRequest(
                                requestorId: _currentUserId ?? 0,
                                quantity: quantity,
                                dateStart: _startDate,
                                assignedToManagerId: _selectedManager,
                                statusId: 0,
                                isDeleted: false,
                              );
                              controller
                                  .createRemoteWorkRequest(request.toJson())
                                  .then((response) {
                                if (response.statusCode == 201) {
                                  Get.snackbar(
                                    '',
                                    '',
                                    titleText: BigText(
                                      text: global.lang.value == "fr"
                                          ? "Succès"
                                          : "Success",
                                      size: 18,
                                      color: MyColors.blackbackground2,
                                    ),
                                    messageText: Text(
                                      global.lang.value == "fr"
                                          ? "Demande créée avec succès"
                                          : "Request created successfully",
                                      style: TextStyle(fontSize: 17),
                                    ),
                                    snackPosition: SnackPosition.BOTTOM,
                                    backgroundColor:
                                        MyColors.BordersGrey.withOpacity(0.4),
                                    overlayBlur: 1.5,
                                  );
                                  menuController.screenindex.value = 5;
                                } else {
                                  Get.snackbar(
                                    '',
                                    '',
                                    titleText: BigText(
                                      text: global.lang.value == "fr"
                                          ? "Succès"
                                          : "Success",
                                      size: 18,
                                      color: Colors.green,
                                    ),
                                    messageText: Text(
                                      global.lang.value == "fr"
                                          ? "La demande a été créée avec succès"
                                          : "Request created successfully",
                                      style: TextStyle(fontSize: 17),
                                    ),
                                    snackPosition: SnackPosition.BOTTOM,
                                    backgroundColor:
                                        MyColors.BordersGrey.withOpacity(0.4),
                                    overlayBlur: 1.5,
                                  );
                                  menuController.screenindex.value = 6;
                                }
                              }).catchError((error) {
                                Get.snackbar(
                                  '',
                                  '',
                                  titleText: BigText(
                                    text: global.lang.value == "fr"
                                        ? "Erreur"
                                        : "Error",
                                    size: 18,
                                    color: Colors.red,
                                  ),
                                  messageText: Text(
                                    global.lang.value == "fr"
                                        ? "Échec de la création de la demande: $error"
                                        : "Failed to create request: $error",
                                    style: TextStyle(fontSize: 17),
                                  ),
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor:
                                      MyColors.BordersGrey.withOpacity(0.4),
                                  overlayBlur: 1.5,
                                );
                              });
                            }
                          },
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.send,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                SizedBox(width: 6),
                                Obx(() => Text(
                                      global.lang.value == "fr"
                                          ? 'Soumettre'
                                          : 'Submit',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 12),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
