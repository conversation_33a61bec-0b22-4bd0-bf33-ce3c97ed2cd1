using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using TMT.Mobile.Core;

namespace TMT.Mobile.Core.Entities
{
    [Table("usermood")]  
    public class UserMood : IEntity
    {
        [Key]
        [Column("id")] 
        public int Id { get; set; }

        [Column("iduser")]
        public int IdUser { get; set; }

        [Column("isanonymous")]
        public bool IsAnonymous { get; set; } = true;

        [Column("comment")]
        public string? Comment { get; set; }

        [Column("score")]
        public int? Score { get; set; }

        [Column("datemood")]
        public DateTime DateMood { get; set; } = DateTime.UtcNow;
    }
}
