import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/models/ManagerDto.dart';
import 'package:tmt_mobile/models/remote_work_request_model.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/utils/userServices.dart';
import 'package:http/http.dart' as http;
import 'package:tmt_mobile/widgets/big_text.dart';

class RemoteWorkRequestController extends GetxController {
  var remoteWorkRequests = <RemoteWorkRequest>[].obs;
  var managers = <ManagerDto>[].obs;
  var isLoading = false.obs;
  final UserServices userServices = UserServices();
  final GlobalController global = Get.find<GlobalController>();
  final Map<int, String> statusIdToName = {
    131: 'Submitted',
    132: 'Approved',
    133: 'Rejected',
    // Add other status mappings here
  };

  String getStatusName(int? statusId) {
    return statusIdToName[statusId] ?? 'Unknown';
  }

  @override
  void onInit() {
    super.onInit();
    fetchManagers();
    fetchRemoteWorkRequests();
    fetchAssignedRemoteWorkRequests();
  }

  Future<void> fetchManagers() async {
    isLoading(true);
    try {
      var response = await userServices.getManagers();
      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        var managerList = <ManagerDto>[];
        if (data['manager'] != null) {
          managerList.add(ManagerDto.fromJson(data['manager']));
        }
        if (data['secondManager'] != null) {
          managerList.add(ManagerDto.fromJson(data['secondManager']));
        }
        managers.value = managerList;
      } else {
        print('Failed to fetch managers: ${response.statusCode}');
      }
    } catch (e) {
      print('An error occurred while fetching managers: $e');
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchRemoteWorkRequests() async {
    isLoading(true);
    try {
      var response = await userServices.getRemoteWorkRequests();
      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List;
        remoteWorkRequests.value =
            data.map((json) => RemoteWorkRequest.fromJson(json)).toList();
      } else {
        // Handle error
      }
    } catch (e) {
      // Handle error
    } finally {
      isLoading(false);
    }
  }

  Future<http.Response> createRemoteWorkRequest(
      Map<String, dynamic> requestDto) async {
    isLoading(true);
    try {
      var response = await userServices.createRemoteWorkRequest(requestDto);
      print('Create Request Response Status Code: ${response.statusCode}');
      print('Create Request Response Body: ${response.body}');
      if (response.statusCode == 201) {
        fetchRemoteWorkRequests();
      }
      return response;
    } catch (e) {
      print('An error occurred while creating remote work request: $e');
      rethrow;
    } finally {
      isLoading(false);
    }
  }

  Future<void> deleteRemoteWorkRequest(int requestId) async {
    isLoading(true);
    try {
      var response = await userServices.deleteRemoteWorkRequest(requestId);
      print('Delete response: ${response.statusCode} - ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 204) {
        await fetchRemoteWorkRequests(); // Ensure this method updates the list
        update(); // Force UI update
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Demande de télétravail supprimée avec succès"
                : "Remote work request deleted successfully",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      } else {
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de la suppression de la demande de télétravail: ${response.statusCode}"
                : "Failed to delete remote work request: ${response.statusCode}",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      print('Delete error: $e');
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la suppression de la demande de télétravail"
              : "An error occurred while deleting remote work request",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateRemoteWorkRequest(RemoteWorkRequest request) async {
    isLoading(true);
    try {
      var requestBody = {
        'id': request.id,
        'requestorId': request.requestorId,
        'assignedToManagerId': request.assignedToManagerId,
        'quantity': request.quantity,
        'dateStart': request.dateStart.toIso8601String(),
        'comment': request.comment,
        'statusId': request.statusId,
      };
      var response = await userServices.updateRemoteWorkRequest(requestBody);
      print('Update response: ${response.statusCode} - ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 204) {
        await fetchRemoteWorkRequests(); // Ensure this method updates the list
        update(); // Force UI update
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Demande de télétravail mise à jour avec succès"
                : "Remote work request updated successfully",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      } else {
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de la mise à jour de la demande de télétravail: ${response.statusCode}"
                : "Failed to update remote work request: ${response.statusCode}",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      print('Update error: $e');
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la mise à jour de la demande de télétravail"
              : "An error occurred while updating remote work request",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  String getManagerName(int id) {
    var manager = managers.firstWhere(
      (manager) => manager.id == id,
      orElse: () => ManagerDto(
          id: 0, email: '', firstName: 'Non', lastName: 'Sélectionné'),
    );
    return '${manager.firstName} ${manager.lastName}';
  }

  Future<void> approveRemoteWorkRequest(int requestId, String comment) async {
    isLoading(true);
    try {
      var response =
          await userServices.approveRemoteWorkRequest(requestId, comment);
      if (response.statusCode == 200) {
        fetchRemoteWorkRequests();
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Demande de télétravail approuvée avec succès"
                : "Remote work request approved successfully",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      } else {
        print(
            'Failed to approve remote work request: ${response.statusCode} - ${response.body}');
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de l'approbation de la demande de télétravail"
                : "Failed to approve remote work request",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      print('Approve error: $e');
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: Colors.red,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de l'approbation de la demande de télétravail"
              : "An error occurred while approving remote work request",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> rejectRemoteWorkRequest(int requestId, String comment) async {
    isLoading(true);
    try {
      var response =
          await userServices.rejectRemoteWorkRequest(requestId, comment);
      if (response.statusCode == 200) {
        fetchRemoteWorkRequests();
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Demande de télétravail rejetée avec succès"
                : "Remote work request rejected successfully",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      } else {
        print(
            'Failed to reject remote work request: ${response.statusCode} - ${response.body}');
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec du rejet de la demande de télétravail"
                : "Failed to reject remote work request",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      print('Reject error: $e');
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: Colors.red,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors du rejet de la demande de télétravail"
              : "An error occurred while rejecting remote work request",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> reassignManagerR(
      int requestId, int assignedToManagerId, String comment) async {
    isLoading(true);
    try {
      var response = await userServices.reassignManagerR(
          requestId, assignedToManagerId, comment);
      if (response.statusCode == 200) {
        fetchRemoteWorkRequests();
        Get.snackbar('Success', 'Manager reassigned successfully');
      } else {
        print(
            'Failed to reassign manager: ${response.statusCode} - ${response.body}');
        Get.snackbar('Error', 'Failed to reassign manager');
      }
    } catch (e) {
      print('Error reassigning manager: $e');
      Get.snackbar('Error', 'An error occurred while reassigning manager');
    } finally {
      isLoading(false);
    }
  }

  int getSubmittedRemoteWorkRequestsCount() {
    return remoteWorkRequests
        .where((request) => request.statusId == 131)
        .length;
  }

  Future<void> fetchAssignedRemoteWorkRequests() async {
    isLoading(true);
    try {
      var response = await userServices.getAssignedRemoteWorkRequests();
      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List;
        remoteWorkRequests.value =
            data.map((json) => RemoteWorkRequest.fromJson(json)).toList();
      } else {
        Get.snackbar('Error', 'Failed to fetch assigned remote work requests');
      }
    } catch (e) {
      Get.snackbar(
          'Error', 'An error occurred while fetching assigned requests');
    } finally {
      isLoading(false);
    }
  }
}
