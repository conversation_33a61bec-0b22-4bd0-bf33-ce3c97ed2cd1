﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

using TMT.Mobile.Core;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Core.Entities
{
    public partial class Appusers : IEntity
    {
        public Appusers()
        {
            Appuserroles = new HashSet<Appuserroles>();

            UserTimesheet = new HashSet<Timesheets>();
          
            TasksUsers = new HashSet<Projecttasks>();
            ManagerTeam = new HashSet<Appusers>();
            ProjectsAsDirector = new HashSet<Projects>();
        


        
  

     

         
        }

        public int Id { get; set; }
        public string Firstname { get; set; }
        public string Lastname { get; set; }
        public string? Login { get; set; }
        public string? Userpwd { get; set; }
        public string Email { get; set; }
        public string? Userposition { get; set; }
        public bool Isactive { get; set; }
        public bool Isblocked { get; set; }
        public bool Isfirstuse { get; set; }
        public bool Isinitpassword { get; set; }
        public int Authentificationfailsnumbers { get; set; }
        public bool Isexternaluser { get; set; }
        public string? Phonenumber { get; set; }
        public string? Note { get; set; }
        public bool Isdeleted { get; set; }
        public string? Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string? Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }
        public DateTime? DateExpiry { get; set; }
        public DateTime StartDate { get; set; }
        public int? IdBusinessUnit { get; set; }
        public string? DefaultLanguage { get; set; }
        public int? IdOffice { get; set; }
        public int? IdTitle { get; set; }
        public int? IdManager { get; set; }

        public string FullName => $"{Firstname} {Lastname}";
        public virtual ICollection<Appuserroles> Appuserroles { get; set; }
    
        public virtual ICollection<Timesheets> UserTimesheet { get; set; }
        [Column("guid")]
        public Guid Guid { get; set; }

       
        public virtual ICollection<Projecttasks> TasksUsers { get; set; }
        public virtual ICollection<Projects> ProjectsAsDirector { get; set; }

        public Appvariables BusinessUnit { get; set; }
        public Appvariables Office { get; set; }
        public Appvariables Title { get; set; }
        public Appusers Manager { get; set; }

        public virtual ICollection<Appusers> ManagerTeam { get; set; }
 



      
     

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}
