# Documentation du Contrôleur LeaveRequest

## Aperçu

Le contrôleur `LeaveRequestController` est un contrôleur API RESTful qui gère les demandes de congés des employés dans l'application TMT Mobile. Il fournit des points d'accès pour créer, ré<PERSON><PERSON><PERSON>, mettre à jour et traiter les demandes de congés, incluant les flux de validation par les managers et le service RH.

## Dépendances

Le contrôleur s'appuie sur les services et référentiels suivants :

- `DynamicDbContext` : Contexte de base de données pour les opérations sur les entités  
- `ITokenService` : Service pour la gestion et la validation des tokens  
- `LeaveRequestRepository` : Référentiel pour les opérations sur les demandes de congés  
- `AppUserRepository` : Référentiel pour les opérations sur les utilisateurs  
- `AppvariableRepository` : Référentiel pour les variables d'application  
- `ILogger` : Service de journalisation  
- `IEmailService` : Service de notification par email

## Authentification et Autorisation

Le contrôleur utilise l'authentification JWT. L'identité de l'utilisateur est extraite des revendications du token, en particulier en utilisant la revendication `TMTClaimsTypes.UserId` pour identifier l'utilisateur actuel.

## Points d'Accès (Endpoints)

### GET

#### Obtenir Toutes les Demandes de Congés
### GET /LeaveRequest

Renvoie toutes les demandes de congés dans le système.

#### Obtenir une Demande de Congé par ID
## GET /LeaveRequest/{id}

Renvoie une demande de congé spécifique par son ID.

#### Obtenir les Demandes de Congés Assignées à l'Utilisateur Actuel
## GET /LeaveRequest/assigned-to-me

Renvoie toutes les demandes de congés assignées à l'utilisateur actuel (généralement un manager).

- Nécessite une authentification  
- Filtre les demandes en fonction de l'ID de l'utilisateur authentifié

#### Obtenir les Demandes de Congés de l'Utilisateur Actuel
## GET /LeaveRequest/my-leave-requests

Renvoie toutes les demandes de congés créées par l'utilisateur actuel.

- Nécessite une authentification  
- Filtre les demandes en fonction de l'ID de l'utilisateur authentifié

### POST

#### Créer une Demande de Congé

## POST /LeaveRequest

Crée une nouvelle demande de congé dans le système.

**Corps de la Requête :**
```json
{
  "Idtype": 115,
  "Idassignedtomanager": null,
  "Title": "Congé Annuel",
  "Description": "Vacances en famille",
  "Datestart": "2025-05-01T00:00:00",
  "Dateend": "2025-05-10T00:00:00",
  "Quantity": 10
}
```
 Processus :

1. Valide le modèle de la requête

2. Obtient l'ID de l'utilisateur actuel à partir du token d'authentification

3. Définit le statut initial comme "Soumis"

4. Vérifie que l'employé existe dans le système

5. Si aucun manager n'est spécifié (Idassignedtomanager est null ou 0), le système tente automatiquement de récupérer le manager assigné à l'employé

6. Crée et enregistre la demande de congé

7. Envoie un email de notification au manager assigné ou automatiquement récupéré

## PUT
### Mettre à Jour une Demande de Congé

## PUT /LeaveRequest/{id}

Met à jour une demande de congé existante.

+ Corps de la Requête : Identique à la création

+ Restrictions :

* Seul le propriétaire de la demande peut la mettre à jour

* Les mises à jour ne sont pas autorisées si la demande a déjà été validée ou rejetée

## Approbation par le Manager

## PUT /LeaveRequest/accept/manager/{id}

Approuve une demande de congé par un manager.

* Change le statut de validation du manager à "Approuvé"

* Envoie un email de notification à l'employé

## Approbation par les RH

## PUT /LeaveRequest/accept/hr/{id}

Approuve une demande de congé par les RH.

* Change le statut de validation RH à "Approuvé"

* Envoie un email de notification à l'employé

## Rejet par le Manager

## PUT /LeaveRequest/reject/manager/{id}


Rejette une demande de congé par un manager.

* Change le statut de validation du manager à "Rejeté"

* Envoie un email de notification à l'employé

## Rejet par les RH

## PUT /LeaveRequest/reject/hr/{id}

Rejette une demande de congé par les RH.

* Change le statut de validation RH à "Rejeté"

* Envoie un email de notification à l'employé

## DELETE
### Supprimer une Demande de Congé
## DELETE /LeaveRequest/{id}

Supprime une demande de congé.

* Seul le propriétaire de la demande peut la supprimer

## Notifications par Email
Le système envoie des notifications par email à différentes étapes du cycle de vie de la demande de congé :

+ Nouvelle Demande de Congé : Notification au manager assigné ou automatiquement détecté

+ Mises à Jour de Statut : Notification à l'employé lors d'une approbation ou d'un rejet