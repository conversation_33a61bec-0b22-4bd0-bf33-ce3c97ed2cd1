# Documentation du Contrôleur RemoteWorkRequest

## Aperçu

Le contrôleur `RemoteWorkRequestController` est un contrôleur API RESTful qui gère les demandes de télétravail des employés dans l'application TMT Mobile. Il permet aux employés de soumettre, consulter, modifier et supprimer leurs demandes de télétravail. Il permet aussi aux managers d'approuver ou rejeter ces demandes. Des notifications par email sont envoyées aux parties concernées lors des changements de statut.

## Dépendances

Le contrôleur s'appuie sur les services et référentiels suivants :

- `DynamicDbContext` : Contexte de base de données pour les opérations sur les entités  
- `RemoteWorkRequestRepository` : Référentiel pour les opérations sur les demandes de télétravail  
- `AppvariableRepository` : Référentiel pour les variables d'application (statuts, etc.)  
- `ILogger` : Service de journalisation  
- `IEmailService` : Service de notification par email

## Authentification et Autorisation

Le contrôleur utilise l'authentification JWT. L'identité de l'utilisateur est extraite des revendications du token, en particulier en utilisant la revendication `TMTClaimsTypes.UserId` pour identifier l'utilisateur actuel.  

- Seul le créateur d'une demande peut la modifier ou la supprimer.  
- Seul le manager assigné peut approuver ou rejeter une demande.

## Points d'Accès (Endpoints)

### GET

#### Obtenir Toutes les Demandes de Télétravail
### GET /RemoteWorkRequest

Renvoie toutes les demandes de télétravail dans le système.

#### Obtenir une Demande de Télétravail par ID
### GET /RemoteWorkRequest/{id}

Renvoie une demande de télétravail spécifique par son ID.

#### Obtenir les Demandes de Télétravail Assignées à l'Utilisateur Actuel
### GET /RemoteWorkRequest/assigned-to-me

Renvoie toutes les demandes de télétravail assignées à l'utilisateur actuel (généralement un manager).

- Nécessite une authentification  
- Filtre les demandes en fonction de l'ID de l'utilisateur authentifié

#### Obtenir les Demandes de Télétravail de l'Utilisateur Actuel
### GET /RemoteWorkRequest/my-remote-work

Renvoie toutes les demandes de télétravail créées par l'utilisateur actuel.

- Nécessite une authentification  
- Filtre les demandes en fonction de l'ID de l'utilisateur authentifié

### POST

#### Créer une Demande de Télétravail

### POST /RemoteWorkRequest

Crée une nouvelle demande de télétravail dans le système.

**Corps de la Requête :**
```json
{
  "assignedToManagerId": 7,
  "quantity": 2,
  "dateStart": "2025-05-15T00:00:00"
}
```
Processus :

1. Valide le modèle de la requête

2. Obtient l'ID de l'utilisateur actuel à partir du token

3. Définit le statut initial comme "Soumis"

4. Crée et enregistre la demande de télétravail

5. Envoie un email de notification au manager assigné

## PUT

### Mettre à Jour une Demande de Télétravail

### PUT /RemoteWorkRequest/{id}

Met à jour une demande de télétravail existante.

**Corps de la Requête :**  
Identique à celui utilisé lors de la création de la demande.

**Restrictions :**

- Seul le créateur de la demande peut la mettre à jour
- Les mises à jour ne sont pas autorisées si la demande est déjà **acceptée** ou **rejetée**

---

### Approbation par le Manager

### PUT /RemoteWorkRequest/{id}/accept

Approuve une demande de télétravail par le manager assigné.

- Change le statut de la demande à **"Approuvé"**
- Envoie un email de notification à l'employé concerné

---

### Rejet par le Manager

### PUT /RemoteWorkRequest/{id}/reject

Rejette une demande de télétravail par le manager assigné.

- Change le statut de la demande à **"Rejeté"**
- Envoie un email de notification à l'employé concerné

---

## DELETE

### Supprimer une Demande de Télétravail

### DELETE /RemoteWorkRequest/{id}

Supprime une demande de télétravail.

- Seul le créateur de la demande peut la supprimer

---

## Notifications par Email

Le système envoie des notifications par email à différentes étapes du cycle de vie de la demande de télétravail :

- **Nouvelle Demande de Télétravail** : Notification au manager assigné  
- **Approbation ou Rejet** : Notification à l'employé créateur de la demande

