{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "TokenKey": "eT5u7zQ9hB3KfMwJ6vX2NqL8pYsR0dZG54Ao1VCJHbmFxOktWgYcDUE93TNaQvBL", "ConnectionStrings": {"TmtMobileApiContextConnection": "Host=**************;Database=tmt_db_core_dev;Username=postgres;Password=*********"}, "AzureCommunicationService": {"EmailServiceConnectionString": "endpoint=https://tmtrackcommunication01.europe.communication.azure.com/;accesskey=****************************************************/jgIwXROAUhlzkAiLQ5WeGuRoGAysz3log==", "EmailServiceSender": "<EMAIL>"}}