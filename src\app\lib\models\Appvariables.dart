class Appvariables {
  int id;
  String category;
  String name;
  num? value; // Changed from double? to num?
  String? other;
  String? other2;
  bool? active;
  bool? isDelitable;
  bool? isEditable;
  int? idparent;
  bool? isDefault;

  Appvariables({
    required this.id,
    required this.category,
    required this.name,
    this.value,
    this.other,
    this.other2,
    this.active,
    this.isDelitable,
    this.isEditable,
    this.idparent,
    this.isDefault,
  });

  factory Appvariables.fromJson(Map<String, dynamic> json) {
    return Appvariables(
      id: json['id'],
      category: json['category'],
      name: json['name'],
      value: json['value'], // No need to convert explicitly
      other: json['other'],
      other2: json['other2'],
      active: json['active'],
      isDelitable: json['isDelitable'],
      isEditable: json['isEditable'],
      idparent: json['idparent'],
      isDefault: json['isDefault'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'name': name,
      'value': value,
      'other': other,
      'other2': other2,
      'active': active,
      'isDelitable': isDelitable,
      'isEditable': isEditable,
      'idparent': idparent,
      'isDefault': isDefault,
    };
  }
}
