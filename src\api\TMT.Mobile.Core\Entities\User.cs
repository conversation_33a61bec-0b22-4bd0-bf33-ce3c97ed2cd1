﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;

namespace TMT.Mobile.Core.Entities
{
    public class User : IEntity
    {

        public int Id { get; set; }
        public Guid GUID { get; set; }
        public string Email { get; set; }
        public string Login { get; set; }   
        public byte[] PasswordHash { get; set; }
        public byte[] PasswordSalt { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? FullName { get; set; }
        public string AccountValidationNumber { get; set; }
        public DateTime? AccountValidationRequestedDate { get; set; }
        public bool AccountValidated { get; set; }
        public DateTime? AccountValidationDate { get; set; }
        public string? ResetPasswordNumber { get; set; }
        public DateTime? ResetPasswordRequestedDate { get; set; }
        public bool ResetPasswordValidated { get; set; }
        public DateTime? ResetPasswordValidationDate { get; set; }
        public int? IdFavoriteOrganization { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public ICollection<UserOrganizations> UserOrganizations { get; set; }
        public ICollection<Organization> OrganizationsOwned { get; set; }
        public Organization FavoriteOrganization { get; set; }
        public TMT.Mobile.Core.Consts.IdentityProviders IdentityProvider { get; set; }
        public bool IsInvitation { get; set; }
        public int MaxOrganizations { get; set; }
        public User()
        {
            UserOrganizations = new HashSet<UserOrganizations>();
            OrganizationsOwned = new HashSet<Organization>();
          
        }


        public override string ToString()
        {
            if (!string.IsNullOrEmpty(this.FullName))
                return this.FullName;
            return this.FirstName + " " + this.LastName;
        }
        public override bool Equals(object obj)
        {
            if (obj is User)
                return this.Email == (obj as User).Email;
            return false;
        }
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

    }
}
