﻿using TMT.Mobile.Api.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;
using TMT.Mobile.Infrastructure.Services;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities.DTOs.Authentification;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Infrastructure.Services.Interfaces;
using TMT.Mobile.Infrastructure.Repositories;
using System.Security.Claims;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Api.Controllers.Authentification
{


    public class AuthentificationController : ControllerBase
    {
        private readonly TmtMobileContext _context;
        private readonly ITokenService _tokenService;
        private readonly IBPEmailService _emailService;
        private readonly IConfiguration _config;

        private readonly UserRepository _userRepository;
        private static Random random = new Random();

        public AuthentificationController(TmtMobileContext context, ITokenService tokenService, IBPEmailService emailService, UserRepository userRepository, IConfiguration config)
        {
            _context = context;
            _userRepository = userRepository;
            _tokenService = tokenService;
            _emailService = emailService;
            _config = config;
        }


        [HttpGet("authentified")]
        public IActionResult isAuthentified()
        {
            // Retrieve the JWT token from the request headers
            string token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

            // Replace with your actual secret key

            // Validate the token using the IsTokenValid function
            if (_tokenService.IsTokenValid(token))
            {
                // The token is valid, proceed with your GET request logic here
                return Ok("yes");
            }
            else
            {
                // The token is not valid or has expired
                return Unauthorized();
            }
        }

        [HttpPost("register")]
        public async Task<ActionResult<AppUserDto>> Register([FromBody] RegisterDto registerDto)

        {
            if (await UserExists(registerDto.Email!)) return BadRequest("Email existe déjà");

            using var hmac = new HMACSHA512();
            var user = new User
            {
                FirstName = registerDto.Firstname!.ToLower(),
                LastName = registerDto.Lastname!.ToLower(),
                Login = registerDto.Email!.ToLower(),
                PasswordHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(registerDto.Password!)),
                PasswordSalt = hmac.Key,
                Email = registerDto.Email!.ToLower(),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                IsDeleted = false,
                FullName = registerDto.Firstname!.ToLower() + registerDto.Lastname!.ToLower(),
                GUID = Guid.NewGuid()
            };
            var claims = new List<Claim>
                    {
                    new Claim(ClaimTypes.Name, user.ToString()),
                    new Claim(ClaimTypes.Email, user.Email),
                    new Claim(TMTClaimsTypes.UserLogin, user.Login),
                    new Claim(TMTClaimsTypes.UserGuid, user.GUID.ToString()),
                    new Claim(TMTClaimsTypes.UserId, user.Id.ToString())
                    };
            // Add Registration Informations
            var _token = _tokenService.CreateToken(user, claims);

            user.AccountValidated = false;
            user.AccountValidationNumber = GenerateRandomRegistrationCode().ToString();
            user.AccountValidationRequestedDate = DateTime.Now;

            _context.Users.Add(user);
            if (await _context.SaveChangesAsync() == 0)
                return null!;
            else
                // Add Registration Email Validation
                EmailService.SendEmailConfirmationRegistry(user, _emailService, user.AccountValidationNumber);

            return new AppUserDto
            {
                Email = user.Email,
                GUID = user.GUID,
                Token = _token
            };





        }

      [HttpPost("login")]
public async Task<ActionResult<AppUserDto>> Login([FromBody] LoginDto loginDto)
{
    var user = await _userRepository.GetUserByEmail(loginDto.Email.ToLower());

    if (user == null) 
        return Unauthorized("Email non valide");

    using var hmac = new HMACSHA512(user.PasswordSalt!);
    var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(loginDto.Password!));

    if (!user.AccountValidated)
        return StatusCode(402, "Ce compte n'est pas encore validé");

    for (int i = 0; i < computedHash.Length; i++)
    {
        if (computedHash[i] != user.PasswordHash![i]) 
            return Unauthorized("Mot de passe non valide");
    }

    var userOrganization = _context.UserOrganizations
        .Include(uo => uo.Organization)
        .ThenInclude(o => o.Owner) // S'assurer d'inclure Owner
        .FirstOrDefault(uo => uo.IdUser == user.Id && !uo.IsDeleted);

    string? ownerId = userOrganization?.Organization?.Owner?.Id.ToString();
    string? orgGuid = userOrganization?.Organization?.GUID.ToString(); // Récupérer l'OrgGuid

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
        new Claim(ClaimTypes.Name, user.Login),
        new Claim(ClaimTypes.Email, user.Email),
        new Claim(TMTClaimsTypes.UserLogin, user.Login),
        new Claim(TMTClaimsTypes.UserGuid, user.GUID.ToString()),
        new Claim(TMTClaimsTypes.UserId, user.Id.ToString())
    };

    if (!string.IsNullOrEmpty(ownerId))
    {
        claims.Add(new Claim(TMTClaimsTypes.Owner, ownerId));
    }

    if (!string.IsNullOrEmpty(orgGuid))
    {
        claims.Add(new Claim(TMTClaimsTypes.OrgGuid, orgGuid)); // Ajouter l'OrgGuid aux claims
    }

    return Ok(new AppUserDto
    {
        Email = user.Email,
        GUID = user.GUID,
        OwnerId = ownerId,
        Token = _tokenService.CreateToken(user, claims)
    });
}



        [HttpPost("registryconfirmation")]

        public async Task<ActionResult<AppUserDto>> RegistryConfirmation(string email, string registrationCode)
        {
            var user = await _context.Users
            .SingleOrDefaultAsync(x => x.Email == email);


            if (user == null)
            {
                return BadRequest("Adresse email non valide!");
            }

            if (user.AccountValidationNumber != registrationCode)
                return BadRequest("Code de confirmation non valide!");

            if (user.AccountValidationNumber == registrationCode)
            {

                if (user.AccountValidationRequestedDate!.Value.AddDays(2) < DateTime.Now)
                {
                    registrationCode = GenerateRandomRegistrationCode().ToString();
                    user = _userRepository.UpdateRegistrationCode(email, registrationCode);
                    EmailService.SendEmailConfirmationRegistry(user, _emailService, registrationCode);
                    return BadRequest("La demande d'inscription a expirée. Un nouvel email de confirmation vous a été envoyé.");
                }
                else
                {
                    _userRepository.ValidateRegistry(email);
                }

                var claims = new List<Claim>
                    {
                    new Claim(ClaimTypes.Name, user.ToString()),
                    new Claim(ClaimTypes.Email, user.Email),
                    new Claim(TMTClaimsTypes.UserLogin, user.Login),
                    new Claim(TMTClaimsTypes.UserGuid, user.GUID.ToString()),
                    new Claim(TMTClaimsTypes.UserId, user.Id.ToString())
                    };
                return Ok(new AppUserDto
                {
                    Email = user.Email,
                    GUID = user.GUID,
                    Token = _tokenService.CreateToken(user!, claims)
                });
            }
            else
            {
                return BadRequest("Adresse email déjà validé");
            }
        }
        [HttpPost("ResetPassowrd")]

        public async Task<ActionResult<AppUserDto>> ResetPassword(string email, string password, string resetCode)
        {
            var user = await _context.Users
            .SingleOrDefaultAsync(x => x.Email == email);


            if (user == null)
            {
                return BadRequest("Adresse email non valide!");
            }

            if (user.ResetPasswordNumber != resetCode)
                return BadRequest("Code de récupération non valide!");

            if (user.ResetPasswordNumber == resetCode)
            {

                if (user.ResetPasswordRequestedDate!.Value.AddDays(2) < DateTime.Now)
                {
                    resetCode = GenerateRandomRegistrationCode().ToString();
                    user = _userRepository.UpdateRegistrationCode(email, resetCode);
                    EmailService.SendEmailResetPassword(user, _emailService);
                    return BadRequest("La demande de récupération a expirée. Un nouvel email de confirmation vous a été envoyé.");
                }
                else
                {
                    _context.Users.Attach(user!);
                    using var hmac = new HMACSHA512();
                    user.PasswordHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));
                    user.PasswordSalt = hmac.Key;
                    user.ResetPasswordValidated = true;
                    user.ResetPasswordValidationDate = DateTime.Now;
                    _context.Entry<User>(user).State = EntityState.Modified;
                    _context.SaveChanges();

                }


            }


            return Ok();

        }





        [HttpPost("SendEmailValidation")]

        public async Task<ActionResult<AppUserDto>> SendEmailValidation(string email)
        {
            var user = await _context.Users
            .SingleOrDefaultAsync(x => x.Email == email);
            if (user == null)
            {
                return BadRequest("Adresse email dont exist!");
            }
            _context.Users.Attach(user!);
            user.AccountValidationNumber = GenerateRandomRegistrationCode().ToString();
            user.AccountValidationRequestedDate = DateTime.Now;


            _context.Entry<User>(user).State = EntityState.Modified;
            _context.SaveChanges();
            EmailService.SendEmailConfirmationRegistry(user, _emailService, user.AccountValidationNumber);

            return Ok("validation code Sent to email");


        }
        [HttpPost("SendEmailReset")]

        public async Task<ActionResult<AppUserDto>> SendResetCode(string email)
        {
            var user = await _context.Users
            .SingleOrDefaultAsync(x => x.Email == email);
            if (user == null)
            {
                return BadRequest("Adresse email dont exist!");
            }
            _context.Users.Attach(user!);
            user.ResetPasswordNumber = GenerateRandomRegistrationCode().ToString();
            user.ResetPasswordRequestedDate = DateTime.UtcNow;
            user.ResetPasswordValidated = false;
            _context.Entry<User>(user).State = EntityState.Modified;
            _context.SaveChanges();
            EmailService.SendEmailResetPassword(user, _emailService);

            return Ok("ResetPassword Sent to email");


        }
        private async Task<bool> UserExists(string email)
        {
            return await _context.Users.AnyAsync(x => x.Email.ToLower() == email.ToLower());
        }


        public int GenerateRandomRegistrationCode()
        {
            int min = 100000;
            int max = 999999;

            return random.Next(min, max + 1);
        }

    }

}
