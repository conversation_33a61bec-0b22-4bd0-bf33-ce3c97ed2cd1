using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities;


namespace TMT.Mobile.Core.Entities
{
    public class ExpenseRequest : IEntity
   {
    public int Id { get; set; }
    public DateTime DateExpense { get; set; }
    public int CurrencyId { get; set; }
    public int ExpenseTypeId { get; set; }
    public int StatusId { get; set; }
    public int? AssignedToId { get; set; }
    public int ProjectfeesId { get; set; }
    public decimal AmountTaxIncl { get; set; }
    public decimal Amount { get; set; }
    public int SupplierId { get; set; }

    public bool IsDeleted { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime UpdatedDate { get; set; }
    public string UpdatedBy { get; set; }
    public string BillReference { get; set; }
    public string Notes { get; set; }
    public string CreatedBy { get; set; }

        public Counterparties? Supplier { get; set; }
        public Currency? Currency { get; set; }
        public Appvariables? ExpenseType { get; set; }
        public Appvariables? Status { get; set; }
        public Appusers? AssignedTo { get; set; }
        public Projectfees? ProjectFee { get; set; }
    }
}
