import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/menucontroller.dart';
import 'package:tmt_mobile/controllers/vacation_request_controller.dart';
import 'package:tmt_mobile/models/vacation_request_model.dart';
import 'package:tmt_mobile/utils/myColors.dart';

class VacationRequestScreen extends StatefulWidget {
  const VacationRequestScreen({super.key});

  @override
  _VacationRequestScreenState createState() => _VacationRequestScreenState();
}

class _VacationRequestScreenState extends State<VacationRequestScreen> {
  final VacationRequestController controller =
      Get.put(VacationRequestController());
  final Menucontroller menuController = Get.find<Menucontroller>();
  final GlobalController global = Get.find<GlobalController>();
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _startDateController = TextEditingController();
  DateTime _startDate = DateTime.now();
  int? _selectedLeaveType;
  int? _selectedManager;

  @override
  void initState() {
    super.initState();
    _startDateController.text = _formatDate(_startDate);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String translateLeaveType(String leaveType) {
    switch (leaveType) {
      case 'PaidLeave':
        return global.lang.value == "fr" ? 'Congé payé' : 'Paid Leave';
      case 'ExitPermit':
        return global.lang.value == "fr" ? 'Permis de sortie' : 'Exit Permit';
      case 'SickLeave':
        return global.lang.value == "fr" ? 'Congé maladie' : 'Sick Leave';
      case 'MaternityLeave':
        return global.lang.value == "fr"
            ? 'Congé maternité'
            : 'Maternity Leave';
      case 'PaternityLeave':
        return global.lang.value == "fr"
            ? 'Congé paternité'
            : 'Paternity Leave';
      case 'UnpaidLeave':
        return global.lang.value == "fr" ? 'Congé sans solde' : 'Unpaid Leave';
      case 'ExceptionalLeave':
        return global.lang.value == "fr"
            ? 'Congé exceptionnel'
            : 'Exceptional Leave';
      default:
        return leaveType;
    }
  }

  Widget _buildFormCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedSecond.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    icon,
                    color: MyColors.MainRedSecond,
                    size: 14,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            child,
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Compact Header Section

            // Form Section
            Padding(
              padding: EdgeInsets.all(12),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Leave Type Card
                    Obx(() => _buildFormCard(
                          title: global.lang.value == "fr"
                              ? 'Type de congé'
                              : 'Leave Type',
                          icon: Icons.category,
                          child: Obx(() {
                            if (controller.isLoading.value) {
                              return Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      MyColors.MainRedSecond),
                                ),
                              );
                            }
                            return DropdownButtonFormField<int>(
                              value: _selectedLeaveType,
                              items: controller.leaveRequestTypes.map((type) {
                                return DropdownMenuItem<int>(
                                  value: type.id,
                                  child: Obx(() =>
                                      Text(translateLeaveType(type.name))),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedLeaveType = value;
                                });
                              },
                              decoration: InputDecoration(
                                labelText: global.lang.value == "fr"
                                    ? 'Sélectionner le type'
                                    : 'Select type',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                      color: MyColors.MainRedSecond, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 10),
                              ),
                              validator: (value) {
                                if (value == null) {
                                  return global.lang.value == "fr"
                                      ? 'Veuillez sélectionner un type de congé'
                                      : 'Please select a leave type';
                                }
                                return null;
                              },
                            );
                          }),
                        )),
                    // Title Card
                    Obx(() => _buildFormCard(
                          title: global.lang.value == "fr" ? 'Titre' : 'Title',
                          icon: Icons.title,
                          child: TextFormField(
                            controller: _titleController,
                            decoration: InputDecoration(
                              labelText: global.lang.value == "fr"
                                  ? 'Entrez le titre'
                                  : 'Enter title',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                    color: MyColors.MainRedSecond, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return global.lang.value == "fr"
                                    ? 'Veuillez entrer un titre'
                                    : 'Please enter a title';
                              }
                              return null;
                            },
                          ),
                        )),
                    // Comment Card
                    Obx(() => _buildFormCard(
                          title: global.lang.value == "fr"
                              ? 'Commentaire (Optionnel)'
                              : 'Comment (Optional)',
                          icon: Icons.comment,
                          child: TextFormField(
                            controller: _descriptionController,
                            maxLines: 2,
                            decoration: InputDecoration(
                              labelText: global.lang.value == "fr"
                                  ? 'Commentaire...'
                                  : 'Comment...',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                    color: MyColors.MainRedSecond, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                            ),
                          ),
                        )),
                    // Duration Card
                    Obx(() => _buildFormCard(
                          title:
                              global.lang.value == "fr" ? 'Durée' : 'Duration',
                          icon: Icons.access_time,
                          child: TextFormField(
                            controller: _quantityController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText:
                                  global.lang.value == "fr" ? 'Jours' : 'Days',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                    color: MyColors.MainRedSecond, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return global.lang.value == "fr"
                                    ? 'Veuillez entrer la quantité'
                                    : 'Please enter the quantity';
                              }
                              return null;
                            },
                          ),
                        )),
                    // Manager Card
                    Obx(() => _buildFormCard(
                          title: global.lang.value == "fr"
                              ? 'Manager assigné'
                              : 'Assigned Manager',
                          icon: Icons.person,
                          child: Obx(() {
                            if (controller.isLoading.value) {
                              return Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      MyColors.MainRedSecond),
                                ),
                              );
                            }
                            return DropdownButtonFormField<int>(
                              value: _selectedManager,
                              items: controller.managers.map((manager) {
                                return DropdownMenuItem<int>(
                                  value: manager.id,
                                  child: Text(
                                      '${manager.firstName} ${manager.lastName}'),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedManager = value;
                                });
                              },
                              decoration: InputDecoration(
                                labelText: global.lang.value == "fr"
                                    ? 'Sélectionner manager'
                                    : 'Select manager',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                      color: MyColors.MainRedSecond, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 10),
                              ),
                            );
                          }),
                        )),
                    // Date Card
                    Obx(() => _buildFormCard(
                          title: global.lang.value == "fr"
                              ? 'Date de début'
                              : 'Start Date',
                          icon: Icons.calendar_today,
                          child: TextFormField(
                            controller: _startDateController,
                            readOnly: true,
                            onTap: () async {
                              DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: _startDate,
                                firstDate: DateTime(2000),
                                lastDate: DateTime(2101),
                                builder: (context, child) {
                                  return Theme(
                                    data: Theme.of(context).copyWith(
                                      colorScheme: ColorScheme.light(
                                        primary: MyColors.MainRedSecond,
                                        onPrimary: Colors.white,
                                        surface: Colors.white,
                                        onSurface: Colors.black,
                                      ),
                                    ),
                                    child: child!,
                                  );
                                },
                              );
                              if (picked != null && picked != _startDate) {
                                setState(() {
                                  _startDate = picked;
                                  _startDateController.text =
                                      _formatDate(_startDate);
                                });
                              }
                            },
                            decoration: InputDecoration(
                              hintText: global.lang.value == "fr"
                                  ? 'Sélectionner date'
                                  : 'Select date',
                              suffixIcon: Container(
                                margin: EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color:
                                      MyColors.MainRedSecond.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Icon(
                                  Icons.calendar_today,
                                  color: MyColors.MainRedSecond,
                                  size: 16,
                                ),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                    color: MyColors.MainRedSecond, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 10),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return global.lang.value == "fr"
                                    ? 'Veuillez sélectionner une date'
                                    : 'Please select a date';
                              }
                              return null;
                            },
                          ),
                        )),
                    // Submit Button
                    SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      height: 44,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            MyColors.MainRedSecond,
                            MyColors.MainRedSecond.withOpacity(0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: MyColors.MainRedSecond.withOpacity(0.2),
                            blurRadius: 6,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            if (_formKey.currentState!.validate()) {
                              int quantity =
                                  int.parse(_quantityController.text);
                              DateTime endDate =
                                  _startDate.add(Duration(days: quantity - 1));
                              VacationRequest request = VacationRequest(
                                idtype: _selectedLeaveType!,
                                title: _titleController.text,
                                description: _descriptionController.text,
                                datestart: _startDate,
                                quantity: quantity,
                                idassignedtomanager: _selectedManager,
                              );
                              controller.createVacationRequest(
                                  request.toJson(), endDate);
                              menuController.screenindex.value = 5;
                            }
                          },
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.send,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                SizedBox(width: 6),
                                Obx(() => Text(
                                      global.lang.value == "fr"
                                          ? 'Soumettre'
                                          : 'Submit',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 12),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
