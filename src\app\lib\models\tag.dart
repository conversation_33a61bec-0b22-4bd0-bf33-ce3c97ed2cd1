class Tag {
  int id;
  String? synonyms;
  String? derivative;
  String? description;

  Tag({
    required this.id,
    this.synonyms,
    this.derivative,
    this.description,
  });

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json['id'],
      synonyms: json['synonyms'],
      derivative: json['derivative'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'synonyms': synonyms,
      'derivative': derivative,
      'description': description,
    };
  }
}
