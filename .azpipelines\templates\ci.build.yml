parameters:
- name: deploy
  type: boolean
  default: false


steps:
- task: DotNetCoreCLI@2
  displayName: Restore
  inputs:
    command: 'restore'
    projects: 'src/api/TMT.Mobile.Api/TMT.Mobile.Api.csproj'
    feedsToUse: 'select'
- task: DotNetCoreCLI@2
  displayName: Build
  inputs:
    projects: 'src/api/TMT.Mobile.Api/TMT.Mobile.Api.csproj'
    arguments: '--configuration Release'

- task: DotNetCoreCLI@2
  displayName: Publish
  inputs:
    command: 'publish'
    publishWebProjects: true
    arguments: '--configuration Release --output $(build.artifactstagingdirectory)'
    zipAfterPublish: true
  
- task: PublishBuildArtifacts@1
  displayName: 'Publish Package'
  inputs:
    PathtoPublish: '$(build.artifactstagingdirectory)'
    ArtifactName: Packages