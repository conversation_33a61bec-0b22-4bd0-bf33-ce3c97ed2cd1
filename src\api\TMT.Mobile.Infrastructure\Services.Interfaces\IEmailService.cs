﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace TMT.Mobile.Infrastructure.Services.Interfaces
{
    public interface IBPEmailService
    {
        public string _smtpServer { get; set; } 
        public int _smtpPort { get; set; } 
        public string _sender { get; set; } 
        public string _senderEmail { get; set; } 
        public string _senderPwd { get; set; } 
        void SendEmail(string sender,
                              string senderEmail,
                              string senderPwd,
                              string receiver,
                              string receiverEmail,
                              string subject,
                              string Body,
                              string smtpServer,
                              int smtpPort,
                              bool useSSL);
        void SendEmailToMultipleReceivers(string sender,
                                     string senderEmail,
                                     string senderPwd,
                                     IEnumerable<string> receivers,
                                     string subject,
                                     string Body,
                                     string smtpServer,
                                     int smtpPort,
                                     bool useSSL);
    }
}
