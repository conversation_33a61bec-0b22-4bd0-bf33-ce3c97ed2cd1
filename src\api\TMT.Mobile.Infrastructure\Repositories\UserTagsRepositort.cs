using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Repositories;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class UserTagsRepository
    {
        private readonly DynamicDbContext _context;

        public UserTagsRepository(DynamicDbContext context)
        {
            _context = context;
        }

        public async Task<List<UserTags>> GetAllAsync()
        {
            return await _context.UserTags
                .Include(ut => ut.Collaborator)
                .Include(ut => ut.Tag)
                .ToListAsync();
        }
        public async Task<UserTags?> GetLatestUserTagAsync(Guid userGuid, int idCollaborator, int idTag)
        {
            return await _context.UserTags
                .Where(ut => ut.UserGuid == userGuid && ut.IdCollaborator == idCollaborator && ut.IdTag == idTag)
                .OrderByDescending(ut => ut.CreatedDate)
                .FirstOrDefaultAsync();
        }

        public async Task<UserTags> GetByIdAsync(int id)
        {
            return await _context.UserTags
                .Include(ut => ut.Collaborator)
                .Include(ut => ut.Tag)
                .FirstOrDefaultAsync(ut => ut.Id == id);
        }

         public async Task<UserTags> AddAsync(UserTags userTag)
        {
            var existingUserTag = await _context.UserTags
                .Where(ut => ut.IdCollaborator == userTag.IdCollaborator 
                             && ut.IdTag == userTag.IdTag 
                             && ut.CreatedDate.Date == DateTime.Now.Date) 
                .FirstOrDefaultAsync();

            if (existingUserTag != null)
            {
                throw new InvalidOperationException("Vous avez déjà évalué ce collaborateur avec ce tag aujourd'hui.");
            }

            _context.UserTags.Add(userTag);
            await _context.SaveChangesAsync();
            return userTag;
        }


        public async Task<UserTags> UpdateAsync(UserTags userTag)
        {
            _context.UserTags.Update(userTag);
            await _context.SaveChangesAsync();
            return userTag;
        }

        public async Task DeleteAsync(int id)
        {
            var userTag = await GetByIdAsync(id);
            if (userTag != null)
            {
                _context.UserTags.Remove(userTag);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<UserTags>> GetByUserGuidAsync(Guid userGuid)
        {
            return await _context.UserTags
                .Where(ut => ut.UserGuid == userGuid)
                .ToListAsync();
        }

    }
}
