using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
    public partial class LeaveRequest : IEntity
    {
        public LeaveRequest()
        {
           
        }
        public int Id { get; set; }
        public int? Idtype { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public int Iduser { get; set; }
        public int? Idassignedtomanager { get; set; }
        public DateTime Datestart { get; set; }
        public DateTime Dateend { get; set; }
        public double Quantity { get; set; }
        public int? Idmanagervalidationstatus { get; set; }
        public string? Managercomment { get; set; }
        public int? Idhrvalidationstatus { get; set; }
        public int? Idassignedtohr { get; set; }
        public string? Hrcomment { get; set; }
        public bool Isdeleted { get; set; }
        public string? Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string? Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }
        public string? Managerfullname { get; set; }
        public string? HRfullname { get; set; }
        public string? Validatedbymanger { get; set; }
        public DateTime? Validatedbymangerdate { get; set; }
        public string? Validatedbyhr { get; set; } 
        public DateTime? Validatedbyhrdate { get; set; }

    }
}

