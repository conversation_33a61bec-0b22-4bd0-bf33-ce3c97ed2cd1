class ManagerDto {
  int id;
  String email;
  String firstName;
  String lastName;

  ManagerDto({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
  });

  factory ManagerDto.fromJson(Map<String, dynamic> json) {
    return ManagerDto(
      id: json['id'],
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
    };
  }
}
