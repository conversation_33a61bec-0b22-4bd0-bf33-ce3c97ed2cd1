import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/user_tag_controller.dart';
import 'package:tmt_mobile/models/usertag.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class UserTagScreen extends StatefulWidget {
  const UserTagScreen({super.key});

  @override
  _UserTagScreenState createState() => _UserTagScreenState();
}

class _UserTagScreenState extends State<UserTagScreen> {
  final UserTagController controller = Get.put(UserTagController());
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _commentController = TextEditingController();
  int? _selectedTag;
  int? _selectedManager;
  double _tagScore = 0.0;

  String translateTag(String tag, GlobalController global) {
    switch (tag) {
      case 'Codage, Développment':
        return global.lang.value == "fr"
            ? 'Coding, Développment'
            : 'Coding, Development';
      case 'SGBD,SQL,NoSQL':
        return global.lang.value == "fr" ? 'SGBD,SQL,NoSQL' : 'DBMS,SQL,NoSQL';
      case 'AI, Machine Learning':
        return global.lang.value == "fr"
            ? 'AI, Machine Learning'
            : 'AI, Machine Learning';
      case 'Frontend, Backend, Fullstack':
        return global.lang.value == "fr"
            ? 'Frontend, Backend, Fullstack'
            : 'Frontend, Backend, Fullstack';
      case 'Cybersécurité, Protection des données':
        return global.lang.value == "fr"
            ? 'Cybersécurité, Protection des données'
            : 'Cybersecurity, Data Protection';
      default:
        return tag;
    }
  }

  Widget _buildFormCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedSecond.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    icon,
                    color: MyColors.MainRedSecond,
                    size: 14,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            child,
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    GlobalController global = Get.find<GlobalController>();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Form Section
            Padding(
              padding: EdgeInsets.all(12),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Tag Selection Card
                    Obx(() {
                      return _buildFormCard(
                        title: global.lang.value == "fr" ? 'Tag' : 'Tag',
                        icon: Icons.label,
                        child: controller.isLoading.value
                            ? Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      MyColors.MainRedSecond),
                                ),
                              )
                            : DropdownButtonFormField<int>(
                                value: _selectedTag,
                                items: controller.tags.map((tag) {
                                  return DropdownMenuItem<int>(
                                    value: tag.id,
                                    child: Text(
                                      translateTag(
                                          tag.synonyms ?? 'No Tag', global),
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedTag = value;
                                  });
                                },
                                decoration: InputDecoration(
                                  labelText: global.lang.value == "fr"
                                      ? 'Sélectionner un tag'
                                      : 'Select a tag',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide:
                                        BorderSide(color: Colors.grey[300]!),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide:
                                        BorderSide(color: Colors.grey[300]!),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                        color: MyColors.MainRedSecond,
                                        width: 2),
                                  ),
                                  filled: true,
                                  fillColor: Colors.white,
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 10),
                                ),
                                validator: (value) {
                                  if (value == null) {
                                    return global.lang.value == "fr"
                                        ? 'Veuillez sélectionner un tag'
                                        : 'Please select a tag';
                                  }
                                  return null;
                                },
                              ),
                      );
                    }),
                    // Collaborator Card
                    Obx(() {
                      return _buildFormCard(
                        title: global.lang.value == "fr"
                            ? 'Collaborateur'
                            : 'Collaborator',
                        icon: Icons.person,
                        child: controller.isLoading.value
                            ? Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      MyColors.MainRedSecond),
                                ),
                              )
                            : DropdownButtonFormField<int>(
                                value: _selectedManager,
                                items: controller.managers.map((manager) {
                                  return DropdownMenuItem<int>(
                                    value: manager.id,
                                    child: Text(
                                      '${manager.firstName} ${manager.lastName}',
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedManager = value;
                                  });
                                },
                                decoration: InputDecoration(
                                  labelText: global.lang.value == "fr"
                                      ? 'Sélectionner collaborateur'
                                      : 'Select collaborator',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide:
                                        BorderSide(color: Colors.grey[300]!),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide:
                                        BorderSide(color: Colors.grey[300]!),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                        color: MyColors.MainRedSecond,
                                        width: 2),
                                  ),
                                  filled: true,
                                  fillColor: Colors.white,
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 10),
                                ),
                                validator: (value) {
                                  if (value == null) {
                                    return global.lang.value == "fr"
                                        ? 'Veuillez sélectionner un collaborateur'
                                        : 'Please select a collaborator';
                                  }
                                  return null;
                                },
                              ),
                      );
                    }),
                    // Comment Card
                    Obx(() {
                      return _buildFormCard(
                        title: global.lang.value == "fr"
                            ? 'Commentaire (Optionnel)'
                            : 'Comment (Optional)',
                        icon: Icons.comment,
                        child: TextFormField(
                          controller: _commentController,
                          maxLines: 2,
                          decoration: InputDecoration(
                            labelText: global.lang.value == "fr"
                                ? 'Commentaire...'
                                : 'Comment...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                  color: MyColors.MainRedSecond, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 10),
                          ),
                        ),
                      );
                    }),
                    // Score Card
                    Obx(() {
                      return _buildFormCard(
                        title: global.lang.value == "fr" ? 'Score' : 'Score',
                        icon: Icons.star,
                        child: RatingBar.builder(
                          initialRating: _tagScore,
                          minRating: 0,
                          direction: Axis.horizontal,
                          allowHalfRating: true,
                          itemCount: 5,
                          itemSize: 32,
                          itemBuilder: (context, _) => Icon(
                            Icons.star,
                            color: Colors.amber,
                          ),
                          onRatingUpdate: (rating) {
                            setState(() {
                              _tagScore = rating;
                            });
                          },
                        ),
                      );
                    }),
                    // Submit Button
                    SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      height: 44,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            MyColors.MainRedSecond,
                            MyColors.MainRedSecond.withOpacity(0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: MyColors.MainRedSecond.withOpacity(0.2),
                            blurRadius: 6,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            if (_formKey.currentState!.validate()) {
                              UserTag userTag = UserTag(
                                id: 0, // Assuming ID is auto-generated
                                userGuid: '', // Replace with actual user GUID
                                idTag: _selectedTag!,
                                tagScore: _tagScore,
                                comment: _commentController.text,
                                createdDate: DateTime.now(),
                                idCollaborator: _selectedManager!,
                              );
                              controller.addUserTag(userTag);
                            }
                          },
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.send,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                SizedBox(width: 6),
                                Obx(() => Text(
                                      global.lang.value == "fr"
                                          ? 'Confirmer'
                                          : 'Confirm',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 12),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
