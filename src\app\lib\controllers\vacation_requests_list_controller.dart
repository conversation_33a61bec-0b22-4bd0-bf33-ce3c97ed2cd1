import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/vacation_request_controller.dart';
import 'package:tmt_mobile/models/vacation_request_model.dart';

class VacationRequestsListController extends GetxController {
  final VacationRequestController vacationController =
      Get.put(VacationRequestController());
  final RxString selectedStatus = 'All'.obs;

  @override
  void onInit() {
    super.onInit();
    // Fetch vacation requests when controller is initialized
    vacationController.fetchVacationRequests();
  }

  void updateSelectedStatus(String status) {
    selectedStatus.value = status;
  }

  void deleteRequest(int? requestId) {
    if (requestId != null) {
      vacationController.deleteVacationRequest(requestId).then((_) {
        vacationController.update(); // Force UI update
      });
    }
  }

  void updateRequest(VacationRequest request) {
    vacationController.updateVacationRequest(request);
  }

  Future<void> refreshRequests() async {
    await vacationController.fetchVacationRequests();
  }
}
