import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/models/user_mood_model.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/utils/userServices.dart';
import 'package:tmt_mobile/widgets/big_text.dart';

class UserMoodController extends GetxController {
  var userMood = UserMood(isAnonymous: true).obs;
  var userMoods = <UserMood>[].obs;
  var isLoading = false.obs;
  GlobalController global = Get.find<GlobalController>();
  final UserServices userServices = UserServices();

  @override
  void onInit() {
    super.onInit();
    fetchUserMoods(); // Fetch user moods when the controller is initialized
  }

  Future<void> submitUserMood(UserMood mood) async {
    isLoading.value = true;
    var response = await userServices.submitUserMood(mood);
    if (response.statusCode == 201) {
      // Refresh the list after successful submission
      await fetchUserMoods();

      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Succès" : "Success",
          size: 18,
          color: Colors.green,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Humeur soumise avec succès"
              : "Mood submitted successfully",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } else {
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: Colors.red,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Échec de la soumission de l'humeur"
              : "Failed to submit mood",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    }
    isLoading.value = false;
  }

  Future<void> fetchUserMoods() async {
    print('UserMoodController: Starting fetchUserMoods...');
    isLoading(true);
    try {
      print('UserMoodController: Calling userServices.getUserMoods()...');
      var response = await userServices.getUserMoods();
      print('UserMoodController: API Response Status: ${response.statusCode}');
      print('UserMoodController: API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        if (response.body.isNotEmpty) {
          try {
            var data = json.decode(response.body);
            print('UserMoodController: Decoded data type: ${data.runtimeType}');
            print('UserMoodController: Decoded data: $data');

            if (data is List) {
              userMoods.value =
                  data.map((json) => UserMood.fromJson(json)).toList();
              print(
                  'UserMoodController: Successfully parsed ${userMoods.length} user moods');
            } else {
              print(
                  'UserMoodController: Expected List but got ${data.runtimeType}');
              userMoods.value = [];
            }
          } catch (parseError) {
            print('UserMoodController: JSON parsing error: $parseError');
            userMoods.value = [];
          }
        } else {
          print('UserMoodController: Empty response body');
          userMoods.value = [];
        }
      } else {
        print(
            'UserMoodController: Failed to fetch user moods - Status: ${response.statusCode}');
        print('UserMoodController: Error response body: ${response.body}');
        userMoods.value = [];

        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de la récupération des humeurs (${response.statusCode})"
                : "Failed to fetch user moods (${response.statusCode})",
            style: TextStyle(fontSize: 17),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      print('UserMoodController: Exception during fetch: $e');
      userMoods.value = [];

      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: Colors.red,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la récupération des humeurs: $e"
              : "An error occurred while fetching user moods: $e",
          style: TextStyle(fontSize: 17),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }
}
