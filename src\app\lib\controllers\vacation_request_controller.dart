import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/models/ManagerDto.dart';
import 'package:tmt_mobile/models/vacation_request_model.dart';
import 'package:tmt_mobile/models/appvariables.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/utils/userServices.dart';
import 'package:tmt_mobile/widgets/big_text.dart';

class VacationRequestController extends GetxController {
  var vacationRequests = <VacationRequest>[].obs;
  var leaveRequestTypes = <Appvariables>[].obs;
  var managers = <ManagerDto>[].obs;
  var isLoading = false.obs;
  final UserServices userServices = UserServices();
  GlobalController global = Get.find<GlobalController>();
  final Map<int, String> statusIdToName = {
    131: 'Submitted',
    132: 'Approved',
    133: 'Rejected',
    // Add other status mappings here
  };

  String getValidationStatusName(int? statusId) {
    return statusIdToName[statusId] ?? 'Unknown';
  }

  @override
  void onInit() {
    super.onInit();
    fetchLeaveRequestTypes();
    fetchManagers();
    fetchVacationRequests();
    fetchAssignedVacationRequests();
  }

  Future<void> fetchLeaveRequestTypes() async {
    isLoading(true);
    try {
      var response = await userServices.getLeaveRequestTypes();
      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List;
        leaveRequestTypes.value =
            data.map((json) => Appvariables.fromJson(json)).toList();
      } else {
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: MyColors.blackbackground2,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de la récupération des types de demande de congé: ${response.statusCode}"
                : "Failed to fetch leave request types: ${response.statusCode}",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la récupération des types de demande de congé"
              : "An error occurred while fetching leave request types",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchManagers() async {
    isLoading(true);
    try {
      var response = await userServices.getManagers();
      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        var managerList = <ManagerDto>[];
        if (data['manager'] != null) {
          managerList.add(ManagerDto.fromJson(data['manager']));
        }
        if (data['secondManager'] != null) {
          managerList.add(ManagerDto.fromJson(data['secondManager']));
        }
        managers.value = managerList;
      } else {
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: MyColors.blackbackground2,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de la récupération des gestionnaires: ${response.statusCode}"
                : "Failed to fetch managers: ${response.statusCode}",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la récupération des gestionnaires"
              : "An error occurred while fetching managers",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchVacationRequests() async {
    print("VacationRequestController: Starting to fetch vacation requests...");
    isLoading(true);
    try {
      var response = await userServices.getVacationRequests();
      print(
          "VacationRequestController: API response status: ${response.statusCode}");
      print("VacationRequestController: API response body: ${response.body}");

      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List;
        print(
            "VacationRequestController: Parsed ${data.length} vacation requests");
        vacationRequests.value =
            data.map((json) => VacationRequest.fromJson(json)).toList();
        print(
            "VacationRequestController: Successfully loaded ${vacationRequests.length} vacation requests");
      } else {
        print(
            "VacationRequestController: Failed to fetch vacation requests - Status: ${response.statusCode}");
        print("VacationRequestController: Error response: ${response.body}");
      }
    } catch (e) {
      print(
          "VacationRequestController: Exception while fetching vacation requests: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> createVacationRequest(
      Map<String, dynamic> requestDto, DateTime endDate) async {
    isLoading(true);
    try {
      requestDto['dateend'] =
          endDate.toIso8601String(); // Add end date to the request
      var response = await userServices.createVacationRequest(requestDto);
      if (response.statusCode == 201) {
        fetchVacationRequests();
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Demande de congé créée avec succès"
                : "Vacation request created successfully",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      } else {
        Get.snackbar('', '',
            titleText: BigText(
              text: global.lang.value == "fr" ? "Erreur" : "Error",
              size: 18,
              color: Colors.red,
            ),
            messageText: Text(
              global.lang.value == "fr"
                  ? "Échec de la création de la demande de congé: ${response.statusCode}"
                  : "Failed to create vacation request: ${response.statusCode}",
              style: TextStyle(
                fontSize: 17,
              ),
            ),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
            overlayBlur: 1.5);
      }
    } catch (e) {
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la création de la demande de congé"
              : "An error occurred while creating vacation request",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> deleteVacationRequest(int requestId) async {
    isLoading(true);
    try {
      var response = await userServices.deleteVacationRequest(requestId);
      print('Delete response: ${response.statusCode} - ${response.body}');
      if (response.statusCode == 204) {
        await fetchVacationRequests(); // Ensure this method updates the list
        update(); // Force UI update
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Demande de congé supprimée avec succès"
                : "Vacation request deleted successfully",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      } else {
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de la suppression de la demande de congé: ${response.statusCode}"
                : "Failed to delete vacation request: ${response.statusCode}",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      print('Delete error: $e');
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la suppression de la demande de congé"
              : "An error occurred while deleting vacation request",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateVacationRequest(VacationRequest request) async {
    isLoading(true);
    try {
      var requestBody = {
        'id': request.id,
        'idtype': request.idtype,
        'title': request.title,
        'description': request.description,
        'datestart': request.datestart.toIso8601String(),
        'dateend': request.datestart
            .add(Duration(days: request.quantity - 1))
            .toIso8601String(), // Calculate end date
        'quantity': request.quantity,
        'idassignedtomanager': request.idassignedtomanager,
        'idmanagervalidationstatus': request.idmanagervalidationstatus,
        'idhrvalidationstatus': request.idhrvalidationstatus,
      };
      print('Request Body: $requestBody');
      var response = await userServices.updateVacationRequest(requestBody);
      print('Update response: ${response.statusCode} - ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 204) {
        fetchVacationRequests();
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Demande de congé mise à jour avec succès"
                : "Vacation request updated successfully",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      } else {
        print(
            'Failed to update vacation request: ${response.statusCode} - ${response.body}');
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? "Erreur" : "Error",
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? "Échec de la mise à jour de la demande de congé: ${response.statusCode}"
                : "Failed to update vacation request: ${response.statusCode}",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
      }
    } catch (e) {
      print('Update error: $e');
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Erreur" : "Error",
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Une erreur s'est produite lors de la mise à jour de la demande de congé"
              : "An error occurred while updating vacation request",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    } finally {
      isLoading(false);
    }
  }

  String getLeaveTypeName(int id) {
    return leaveRequestTypes
        .firstWhere((type) => type.id == id,
            orElse: () =>
                Appvariables(id: 0, category: '', name: 'Non sélectionné'))
        .name;
  }

  String getManagerName(int id) {
    var manager = managers.firstWhere((manager) => manager.id == id,
        orElse: () => ManagerDto(
            id: 0, email: '', firstName: 'Non', lastName: 'Sélectionné'));
    return '${manager.firstName} ${manager.lastName}';
  }

  Future<void> approveVacationRequest(int requestId, String comment) async {
    isLoading(true);
    try {
      var response =
          await userServices.approveVacationRequest(requestId, comment);
      if (response.statusCode == 200) {
        fetchVacationRequests();
        Get.snackbar('Success', 'Vacation request approved successfully');
      } else {
        Get.snackbar('Error', 'Failed to approve vacation request');
      }
    } finally {
      isLoading(false);
    }
  }

  Future<void> rejectVacationRequest(int requestId, String comment) async {
    isLoading(true);
    try {
      var response =
          await userServices.rejectVacationRequest(requestId, comment);
      if (response.statusCode == 200) {
        fetchVacationRequests();
        Get.snackbar('Success', 'Vacation request rejected successfully');
      } else {
        Get.snackbar('Error', 'Failed to reject vacation request');
      }
    } finally {
      isLoading(false);
    }
  }

  int getSubmittedVacationRequestsCount() {
    return vacationRequests
        .where((request) => request.idmanagervalidationstatus == 131)
        .length;
  }

  // Add this method to VacationRequestController
  Future<void> reassignManager(
      int requestId, int managerId, String comment) async {
    isLoading(true);
    try {
      var response =
          await userServices.reassignManager(requestId, managerId, comment);
      if (response.statusCode == 200) {
        fetchVacationRequests();
        Get.snackbar('Success', 'Manager reassigned successfully');
      } else {
        print(
            'Failed to reassign manager: ${response.statusCode} - ${response.body}');
        Get.snackbar('Error', 'Failed to reassign manager');
      }
    } catch (e) {
      print('Error reassigning manager: $e');
      Get.snackbar('Error', 'An error occurred while reassigning manager');
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchAssignedVacationRequests() async {
    isLoading(true);
    try {
      var response = await userServices.getAssignedVacationRequests();
      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List;
        vacationRequests.value =
            data.map((json) => VacationRequest.fromJson(json)).toList();
      } else {
        Get.snackbar('Error', 'Failed to fetch assigned vacation requests');
      }
    } catch (e) {
      Get.snackbar(
          'Error', 'An error occurred while fetching assigned requests');
    } finally {
      isLoading(false);
    }
  }
}
