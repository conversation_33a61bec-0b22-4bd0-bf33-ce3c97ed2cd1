﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;  
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Npgsql;
using System.Security.Claims;
using TMT.Mobile.Core.Entities.DTOs;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class AppUserRepository : CoreRepository<User, DynamicDbContext>
    {
        public AppUserRepository(DynamicDbContext context) : base(context)
        {
        }

          public async Task<Appusers> GetByIdAsync(int id)
        {
            return await context.Appusers.FindAsync(id);
        }
        public async Task<Appusers> GetUserByEmailAsync(string email)
            {
                return await context.Appusers
                    .FirstOrDefaultAsync(u => u.Email == email);
            }
        public async Task<List<Appusers>> GetMyManager(int userIdFromToken)
        {
            if (userIdFromToken <= 0)
                return new List<Appusers>();

            var user = await context.Appusers
                .FirstOrDefaultAsync(u => u.Id == userIdFromToken);

            if (user?.IdManager == null)
                return new List<Appusers>();

            return await context.Appusers
                .Where(m => m.Id == user.IdManager)
                .ToListAsync();
        }
        public async Task<ManagerDto?> GetManagerByUserEmail(string userEmail)
        {
            try
            {
                return await context.Appusers
                    .Where(u => u.Email == userEmail)
                    .Select(u => new ManagerDto
                    {
                        Id = u.Manager.Id,
                        Email = u.Manager.Email,
                        FirstName = u.Manager.Firstname,
                        LastName = u.Manager.Lastname
                    })
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur repository: {ex.Message}");
                return null;
            }
        }

        public async Task<ManagerHierarchyDto?> GetManagerHierarchyByUserEmail(string userEmail)
        {
            try
            {
                return await context.Appusers
                    .Include(u => u.Manager)
                    .ThenInclude(m => m.Manager)
                    .Where(u => u.Email == userEmail)
                    .Select(u => new ManagerHierarchyDto
                    {
                        Manager = u.Manager != null ? new ManagerDto 
                        {
                            Id = u.Manager.Id,
                            Email = u.Manager.Email,
                            FirstName = u.Manager.Firstname,
                            LastName = u.Manager.Lastname
                        } : null,
                        SecondManager = u.Manager != null && u.Manager.Manager != null ? new ManagerDto
                        {
                            Id = u.Manager.Manager.Id,
                            Email = u.Manager.Manager.Email,
                            FirstName = u.Manager.Manager.Firstname,
                            LastName = u.Manager.Manager.Lastname
                        } : null
                    })
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Erreur dans GetManagerHierarchyByUserEmail");
                return null;
            }
        }

    }

}
