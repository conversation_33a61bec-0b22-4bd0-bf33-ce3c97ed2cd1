using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace TMT.Mobile.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] 
    public class UserTagsController : ControllerBase
    {        
        private readonly DynamicDbContext _dynamicDb;
        private readonly UserTagsRepository _userTagsRepository;

        public UserTagsController(
        DynamicDbContext dynamicDb,
        UserTagsRepository userTagsRepository)
        {            
            _dynamicDb = dynamicDb ?? throw new ArgumentNullException(nameof(dynamicDb));
            _userTagsRepository = userTagsRepository;
        }

   [HttpPost]
public async Task<IActionResult> AddUserTag([FromBody] UserTagsDto userTagDto)
{
    if (userTagDto == null)
    {
        return BadRequest(new { message = "Données invalides." });
    }

    var userGuidClaim = User.FindFirst("GUID")?.Value; 

    if (string.IsNullOrEmpty(userGuidClaim) || !Guid.TryParse(userGuidClaim, out Guid userGuid))
    {
        return Unauthorized(new { message = "Utilisateur non authentifié." });
    }

    try
    {
        var existingTag = await _userTagsRepository.GetLatestUserTagAsync(userGuid, userTagDto.IdCollaborator, userTagDto.IdTag);
        if (existingTag != null && existingTag.CreatedDate.Date == DateTime.UtcNow.Date)
        {
            return BadRequest(new { message = "Vous ne pouvez pas créer plus d'un avis par jour pour ce collaborateur et ce tag." });
        }

        var newUserTag = new UserTags
        {
            UserGuid = userGuid,
            IdCollaborator = userTagDto.IdCollaborator,
            IdTag = userTagDto.IdTag,
            TagScore = userTagDto.TagScore,
            Comment = userTagDto.Comment,
            CreatedBy = userGuid.ToString(),
            CreatedDate = DateTime.UtcNow
        };

        var addedUserTag = await _userTagsRepository.AddAsync(newUserTag);
        return CreatedAtAction(nameof(GetUserTagById), new { id = addedUserTag.Id }, addedUserTag);
    }
    catch
    {
        return StatusCode(500, new { message = "Une erreur interne est survenue." });
    }
}



        [HttpGet("{id}")]
        public async Task<IActionResult> GetUserTagById(int id)
        {
            var userTag = await _userTagsRepository.GetByIdAsync(id);
            if (userTag == null)
            {
                return NotFound(new { message = "Évaluation non trouvée." });
            }
            return Ok(userTag);
        }
     private string GetUserEmail()
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email);
            if (emailClaim == null)
                throw new UnauthorizedAccessException("Email non trouvé dans le token");

            return emailClaim.Value;
        }


   [HttpGet("my-user-tags")]
[Authorize]
public async Task<IActionResult> GetMyUserTags()
{
    try
    {
        var email = GetUserEmail();

        var user = await _dynamicDb.Appusers
    .Where(u => u.Email == email)
    .Select(u => new { u.Guid })
    .FirstOrDefaultAsync();

        if (user == null)
            return NotFound("Utilisateur non trouvé");

        var userGuid = user.Guid;

        var myTags = await _userTagsRepository.GetByUserGuidAsync(userGuid);

        return Ok(myTags);
    }
    catch (Exception ex)
    {
        return StatusCode(500, new
        {
            Success = false,
            Message = "Échec de la récupération de vos tags",
            Error = ex.Message
        });
    }
}

}
}