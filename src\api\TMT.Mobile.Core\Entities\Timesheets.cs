﻿using System;
using TMT.Mobile.Core.Entities;


namespace TMT.Mobile.Core.Entities
{
  public partial class Timesheets : IEntity
  {
        public int Id { get; set; }
        public int? Idproject { get; set; }
        public int? Idprojectlot { get; set; }
        public int? Idtask { get; set; }
        public int? Iduser { get; set; }
        public DateTime Tsday { get; set; }
        public double Tsvalue { get; set; }
        public bool Isvalidated { get; set; }
        public string? Validatedby { get; set; }

        public DateTime? Datevalidation { get; set; }
        public bool Isbillable { get; set; }
        public bool Isdeleted { get; set; }
        public string Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string? Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }
        public double TsScore { get; set; }
        public int? IdUserBusinessUnit { get; set; }

        public  Projects Project { get; set; }
        public  Projectlots ProjectLot { get; set; }
        public  Projecttasks Task { get; set; }
        public  Appusers User { get; set; }

        public  Appvariables UserBusinessUnit { get; set; }
    }
}
