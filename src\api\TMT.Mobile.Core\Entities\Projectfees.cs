using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
  public partial class Projectfees : IEntity
  {
   
    public int Id { get; set; }
    public int? Idproject { get; set; }
    public DateTime Feesdate { get; set; }
    public double Feesamount { get; set; }
    public int? IdFeestype { get; set; }
    public int? IdAssignedto { get; set; }
    public string? Assignedtoexternal { get; set; }
    public string? Comment { get; set; }
    public string? Reference { get; set; }
    public bool Isbillable { get; set; }
    public double? Resaleamount { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public int? IdStatus { get; set; }
    public int? IdRebillingStatus { get; set; }
    public double? Feesamounttaxincl { get; set; }
    [Column("idbill")]
    public int? Idbill { get; set; }
    public int? Idprojectbills { get; set; }
    public int? IdSupplier { get; set; }
    public int? IdClient { get; set; }
    public virtual Appusers? AssignedTo { get; set; }
    public virtual Appvariables? Type { get; set; }
    public virtual Appvariables? Status { get; set; }
    public virtual Appvariables? RebillingStatus { get; set; }
    public virtual Projects? Project { get; set; }

    public Bill? Bill { get; set; }
  //public Projectbills? Projectbill { get; set; }
    public int? IdCurrency { get; set; }
    public Currency? Currency { get; set; }
    public double? ExchangeRate { get; set; } 
    public int? IdOrder { get; set; }
    //public Projectorders? ProjectOrder { get; set; }
    public Counterparties? Supplier { get; set; }
    public Counterparties? Client { get; set; }

   // public virtual ICollection<SupplierLetteringDetail> SupplierLetteringDetails { get; set; }

    public virtual ICollection<ExpenseRequest> ExpenseRequests { get; set; }
    public int? IdEntity { get; set; }
   // public virtual OrgEntity? Entity { get; set; }

    public Projectfees()
    {
    // SupplierLetteringDetails = new HashSet<SupplierLetteringDetail>();
     ExpenseRequests = new HashSet<ExpenseRequest>();
    }
  }
}