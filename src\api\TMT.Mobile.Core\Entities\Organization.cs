﻿using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;

namespace TMT.Mobile.Core.Entities
{
  public class Organization : IEntity
  {
    public int Id { get; set; }
    public Guid GUID { get; set; }
    public string Reference { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public int IdOwner { get; set; }
    public string ConnectionString { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public bool IsDeleted { get; set; }
    public string? CreationTemplate { get; set; }
    public bool IsActive { get; set; }
    public bool IsSuspended { get; set; }
    public ICollection<UserOrganizations> UserOrganizations { get; set; }
    public ICollection<User> FavoriteOrganizations { get; set; }
  
    public User Owner { get; set; }
    public bool SchemaCreated { get; set; }
    public Organization()
    {
      FavoriteOrganizations = new HashSet<User>();
      UserOrganizations = new HashSet<UserOrganizations>();
      
    }
    public override string ToString()
    {
      return this.Name;
    }
    public override bool Equals(object obj)
    {
      if (obj is Organization)
        return this.Id == (obj as Organization).Id;
      return false;
    }
    public override int GetHashCode()
    {
      return base.GetHashCode();
    }
  }
  public class OrganizationEqualityComparer : IEqualityComparer<Organization>
  {
    public bool Equals(Organization b1, Organization b2)
    {
      if (b2 == null && b1 == null)
        return true;
      else if (b1 == null || b2 == null)
        return false;
      else if (b1.Id == b2.Id)
        return true;
      else
        return false;
    }

    public int GetHashCode(Organization bx)
    {
      int hCode = bx.GUID.GetHashCode() ^ bx.Id;
      return hCode.GetHashCode();
    }
  }
}
