using Microsoft.EntityFrameworkCore;

using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class CounterpartiesRepository : CoreRepository<Counterparties, DynamicDbContext>
    {

        public CounterpartiesRepository(DynamicDbContext context) : base(context)
        {

        }

        public async Task<IEnumerable<Counterparties>> GetAllAsync()
        {
            return await context.counterparties.ToListAsync();
        }

    }
}