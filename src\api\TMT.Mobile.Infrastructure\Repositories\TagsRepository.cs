using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class TagsRepository : CoreRepository<Tag, DynamicDbContext>
    {
        public TagsRepository(DynamicDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Tag>> GetAllAsync()
        {
            return await context.Tags.ToListAsync();
        }

       

    }
}
