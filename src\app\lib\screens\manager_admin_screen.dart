import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/screens/remote_work_request_validation_screen.dart';
import 'package:tmt_mobile/screens/vacation_request_validation_screen.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/vacation_request_controller.dart';
import 'package:tmt_mobile/controllers/remote_work_request_controller.dart';

class ManagerAdminPage extends StatelessWidget {
  final VacationRequestController vacationRequestController =
      Get.put(VacationRequestController());
  final RemoteWorkRequestController remoteWorkRequestController =
      Get.put(RemoteWorkRequestController());

  ManagerAdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: GetBuilder<GlobalController>(
        builder: (global) {
          return RefreshIndicator(
            color: MyColors.MainRedSecond,
            backgroundColor: Colors.white,
            onRefresh: () async {
              await vacationRequestController.fetchAssignedVacationRequests();
              await remoteWorkRequestController
                  .fetchAssignedRemoteWorkRequests();
            },
            child: ListView(
              padding: EdgeInsets.all(20),
              children: [
                Obx(() {
                  final count = vacationRequestController
                      .getSubmittedVacationRequestsCount();
                  return ModernRequestCard(
                    title: global.lang.value == "fr"
                        ? 'Demandes de congé'
                        : 'Vacation Requests',
                    subtitle: global.lang.value == "fr"
                        ? 'En attente de validation'
                        : 'Pending validation',
                    count: count,
                    icon: Icons.beach_access,
                    gradientColors: [
                      Color(0xFF4FC3F7),
                      Color(0xFF29B6F6),
                    ],
                    onTap: () {
                      Get.to(() => VacationRequestValidationPage());
                    },
                  );
                }),
                SizedBox(height: 16),
                Obx(() {
                  final count = remoteWorkRequestController
                      .getSubmittedRemoteWorkRequestsCount();
                  return ModernRequestCard(
                    title: global.lang.value == "fr"
                        ? 'Demandes de télétravail'
                        : 'Remote Work Requests',
                    subtitle: global.lang.value == "fr"
                        ? 'En attente de validation'
                        : 'Pending validation',
                    count: count,
                    icon: Icons.home_work,
                    gradientColors: [
                      Color(0xFF66BB6A),
                      Color(0xFF4CAF50),
                    ],
                    onTap: () {
                      Get.to(() => RemoteWorkRequestValidationPage());
                    },
                  );
                }),
                SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }
}

class ModernRequestCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final int count;
  final IconData icon;
  final List<Color> gradientColors;
  final VoidCallback? onTap;

  const ModernRequestCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.count,
    required this.icon,
    required this.gradientColors,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradientColors,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: gradientColors[0].withOpacity(0.3),
              blurRadius: 15,
              offset: Offset(0, 8),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: onTap,
            child: Container(
              padding: EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          icon,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '$count',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        count > 0
                            ? (count == 1
                                ? (title.contains('congé') ||
                                        title.contains('Vacation')
                                    ? (Get.find<GlobalController>()
                                                .lang
                                                .value ==
                                            "fr"
                                        ? '1 demande'
                                        : '1 request')
                                    : (Get.find<GlobalController>()
                                                .lang
                                                .value ==
                                            "fr"
                                        ? '1 demande'
                                        : '1 request'))
                                : (title.contains('congé') ||
                                        title.contains('Vacation')
                                    ? (Get.find<GlobalController>()
                                                .lang
                                                .value ==
                                            "fr"
                                        ? '$count demandes'
                                        : '$count requests')
                                    : (Get.find<GlobalController>()
                                                .lang
                                                .value ==
                                            "fr"
                                        ? '$count demandes'
                                        : '$count requests')))
                            : (Get.find<GlobalController>().lang.value == "fr"
                                ? 'Aucune demande'
                                : 'No requests'),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white.withOpacity(0.8),
                        size: 16,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
