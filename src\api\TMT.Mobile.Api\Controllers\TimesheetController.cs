﻿using Microsoft.AspNetCore.Authorization;

using Microsoft.AspNetCore.Mvc;

using System.Security.Claims;
using TMT.Mobile.Api.Interfaces;
using TMT.Mobile.Core.Entities.DTOs;

using TMT.Mobile.Core.Consts;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Core.Entities;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.EntityFrameworkCore;
using System.Data.Entity;
using Microsoft.Extensions.Logging;
using TMT.Mobile.Infrastructure.Repositories;

namespace TMT.Mobile.Api.Controllers
{
  public class TimesheetController : ControllerBase
    {

        private readonly ILogger<TimesheetController> _logger;
        private readonly DynamicDbContext _DynamicDb;
        private readonly ITokenService _tokenService;
        private readonly ProjectLotRepository _projectLotRepository;
        private readonly ProjectTasksRepository _projecttasksRepository;
        private readonly ProjectRepository _projectRepository;
        public TimesheetController(DynamicDbContext dynamicDb, ITokenService tokenService, ILogger<TimesheetController> logger, ProjectRepository projectRepository, ProjectLotRepository projectLotRepository, ProjectTasksRepository projectTasksRepository)
        {
            _DynamicDb = dynamicDb;
            _tokenService = tokenService;
            _projectLotRepository = projectLotRepository;
            _projecttasksRepository = projectTasksRepository;
            _projectRepository = projectRepository;
            _logger = logger;

        }

         [HttpGet("getTimeSheets")]
        [Authorize]
        public IActionResult GetTimeSheet(DateTime date)
        {
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            if (string.IsNullOrEmpty(userEmail))
                return Unauthorized("Utilisateur non authentifié");

            var appUserId = _DynamicDb.Appusers
                .Where(u => u.Email == userEmail)
                .Select(u => u.Id)
                .FirstOrDefault();

            if (appUserId == 0)
                return NotFound("Utilisateur non trouvé");

            var timesheets = _DynamicDb.Timesheets
                .Where(ts => ts.Iduser == appUserId && ts.Tsday == date)
                .Select(ts => new
                {
                    ts.Id,
                    ts.Tsday,
                    ts.Tsvalue,
                    ts.Isvalidated,
                    Project = _DynamicDb.Project.FirstOrDefault(p => p.Id == ts.Idproject).Projectname,
                    ProjectLot = _DynamicDb.Projectlots.FirstOrDefault(pl => pl.Id == ts.Idprojectlot).Lotname,
                    Task = _DynamicDb.Projecttasks.FirstOrDefault(t => t.Id == ts.Idtask).Taskname
                })
                .ToList();

            return Ok(timesheets);
        }

        [HttpGet("copyLastTimeSheet")]
        [Authorize]
        public IActionResult CopyLastTimeSheet()
        {
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            if (string.IsNullOrEmpty(userEmail))
                return Unauthorized("Utilisateur non authentifié");

            var appUserId = _DynamicDb.Appusers
                .Where(u => u.Email == userEmail)
                .Select(u => u.Id)
                .FirstOrDefault();

            if (appUserId == 0)
                return NotFound("Utilisateur non trouvé");

            var lastDateWithTimesheet = _DynamicDb.Timesheets
                .Where(ts => ts.Iduser == appUserId)
                .OrderByDescending(ts => ts.Createddate)
                .Select(ts => ts.Createddate)
                .FirstOrDefault();

            if (lastDateWithTimesheet == default)
                return NotFound("Aucune feuille de temps trouvée");

            var lastTimesheet = _DynamicDb.Timesheets
                .Where(ts => ts.Iduser == appUserId && ts.Createddate == lastDateWithTimesheet)
                .Select(ts => new TimesheetInputDto
                {
                    IdProjet = ts.Idproject ?? 0,
                    IdProjetLot = ts.Idprojectlot ?? 0,
                    IdProjetTask = ts.Idtask ?? 0,
                    TsDay = ts.Tsday,
                    Value = ts.Tsvalue 
                })
                .FirstOrDefault();

            if (lastTimesheet == null)
                return NotFound("Aucun timesheet trouvé pour la dernière date");

            return Ok(lastTimesheet);
        }


        [HttpGet("getRecentTimeSheets")]
        [Authorize]
        public IActionResult GetRecentTimeSheets()
        {
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            if (string.IsNullOrEmpty(userEmail))
                return Unauthorized("Utilisateur non authentifié");

            var appUserId = _DynamicDb.Appusers
                .Where(u => u.Email == userEmail)
                .Select(u => u.Id)
                .FirstOrDefault();
            if (appUserId == 0)
                return NotFound("Utilisateur introuvable");

            var lastDate = _DynamicDb.Timesheets
                .Where(ts => ts.Iduser == appUserId)
                .OrderByDescending(ts => ts.Createddate)
                .Select(ts => ts.Tsday)
                .FirstOrDefault();
            if (lastDate == null)
                return NotFound("Aucune feuille de temps trouvée");

            var sheets = _DynamicDb.Timesheets
                .Where(ts => ts.Iduser == appUserId && ts.Tsday == lastDate)
                .Select(ts => new
                {
                    ts.Id,
                    ts.Tsday,
                    ts.Tsvalue,
                    ts.Isvalidated
                })
                .ToList();

            return Ok(sheets);
        }


        [HttpDelete("deleteTimeSheets")]
        [Authorize]
        public IActionResult DeleteTimeSheets(int id)
        {
            try
            {
                if (!User.Identity.IsAuthenticated)
                    return Unauthorized();
                
                var sql = "DELETE FROM timesheets WHERE id = @p0 AND isvalidated = false RETURNING 1";
                
                var deleted = _DynamicDb.Database.ExecuteSqlRaw(sql, id) > 0;

                if (!deleted)
                    return NotFound("Timesheet non trouvé ou déjà validé");

                return Ok("Suppression réussie");
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erreur lors de la suppression");
            }
        }

        private string GetUserEmail()
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email);
            if (emailClaim == null)
                throw new UnauthorizedAccessException("Email non trouvé dans le token");

            return emailClaim.Value;
        }


   [HttpPost("AddTimeSheets")]
[Authorize]
 
public IActionResult AddTimeSheet([FromBody] TimesheetInputDto input)
{
    try
    {
        if (!ModelState.IsValid)
        {
            _logger.LogWarning($"Validation de TimesheetInputDto échouée: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}");
            return BadRequest(ModelState);
        }

        _logger.LogInformation($"Données reçues: Projet={input.IdProjet}, Lot={input.IdProjetLot}, Tâche={input.IdProjetTask}, Date={input.TsDay}, Valeur={input.Value}");

        var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
        if (string.IsNullOrEmpty(userEmail))
            return Unauthorized("Utilisateur non authentifié");

        var appUserId = _DynamicDb.Appusers
            .Where(u => u.Email == userEmail)
            .Select(u => u.Id)
            .FirstOrDefault();

        if (appUserId == 0)
            return NotFound("Utilisateur non trouvé");

        int newId = 0;
        bool isUpdate = false;

        if (_DynamicDb.Database.GetDbConnection().State != System.Data.ConnectionState.Open)
        {
            _DynamicDb.Database.GetDbConnection().Open();
        }

        using (var command = _DynamicDb.Database.GetDbConnection().CreateCommand())
        {
            command.CommandText = @"
                SELECT id FROM timesheets 
                WHERE iduser = @iduser 
                AND idproject = @idproject 
                AND idprojectlot = @idprojectlot 
                AND idtask = @idtask 
                AND DATE(tsday) = DATE(@tsday)
                LIMIT 1";
            
            var userParam = command.CreateParameter();
            userParam.ParameterName = "@iduser";
            userParam.Value = appUserId;
            command.Parameters.Add(userParam);
            
            var projectParam = command.CreateParameter();
            projectParam.ParameterName = "@idproject";
            projectParam.Value = input.IdProjet;
            command.Parameters.Add(projectParam);
            
            var lotParam = command.CreateParameter();
            lotParam.ParameterName = "@idprojectlot";
            lotParam.Value = input.IdProjetLot;
            command.Parameters.Add(lotParam);
            
            var taskParam = command.CreateParameter();
            taskParam.ParameterName = "@idtask";
            taskParam.Value = input.IdProjetTask;
            command.Parameters.Add(taskParam);
            
            var dayParam = command.CreateParameter();
            dayParam.ParameterName = "@tsday";
            dayParam.Value = input.TsDay;
            command.Parameters.Add(dayParam);
            
            var existingId = command.ExecuteScalar();
            
            if (existingId != null && existingId != DBNull.Value)
            {
                isUpdate = true;
                command.Parameters.Clear();
                command.CommandText = @"
                    UPDATE timesheets
                    SET tsvalue = @tsvalue, updateddate = @updateddate
                    WHERE id = @id
                    RETURNING id";
                
                var idParam = command.CreateParameter();
                idParam.ParameterName = "@id";
                idParam.Value = Convert.ToInt32(existingId);
                command.Parameters.Add(idParam);
                
                var valueParam = command.CreateParameter();
                valueParam.ParameterName = "@tsvalue";
                valueParam.Value = input.Value;
                command.Parameters.Add(valueParam);
                
                var dateParam = command.CreateParameter();
                dateParam.ParameterName = "@updateddate";
                dateParam.Value = DateTime.Now;
                command.Parameters.Add(dateParam);
                
                var result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    newId = Convert.ToInt32(result);
                }
            }
            else
            {
                command.Parameters.Clear();
                command.CommandText = @"
                    INSERT INTO timesheets (iduser, idproject, idprojectlot, idtask, tsday, tsvalue, isvalidated, createddate)
                    VALUES (@iduser, @idproject, @idprojectlot, @idtask, @tsday, @tsvalue, @isvalidated, @createddate)
                    RETURNING id";
                
                var userParam2 = command.CreateParameter();
                userParam2.ParameterName = "@iduser";
                userParam2.Value = appUserId;
                command.Parameters.Add(userParam2);
                
                var projectParam2 = command.CreateParameter();
                projectParam2.ParameterName = "@idproject";
                projectParam2.Value = input.IdProjet;
                command.Parameters.Add(projectParam2);
                
                var lotParam2 = command.CreateParameter();
                lotParam2.ParameterName = "@idprojectlot";
                lotParam2.Value = input.IdProjetLot;
                command.Parameters.Add(lotParam2);
                
                var taskParam2 = command.CreateParameter();
                taskParam2.ParameterName = "@idtask";
                taskParam2.Value = input.IdProjetTask;
                command.Parameters.Add(taskParam2);
                
                var dayParam2 = command.CreateParameter();
                dayParam2.ParameterName = "@tsday";
                dayParam2.Value = input.TsDay;
                command.Parameters.Add(dayParam2);
                
                var valueParam2 = command.CreateParameter();
                valueParam2.ParameterName = "@tsvalue";
                valueParam2.Value = input.Value;
                command.Parameters.Add(valueParam2);
                
                var validatedParam = command.CreateParameter();
                validatedParam.ParameterName = "@isvalidated";
                validatedParam.Value = false;
                command.Parameters.Add(validatedParam);
                
                var createdParam = command.CreateParameter();
                createdParam.ParameterName = "@createddate";
                createdParam.Value = DateTime.Now;
                command.Parameters.Add(createdParam);
                
                var result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    newId = Convert.ToInt32(result);
                }
            }
        }

        if (isUpdate)
        {
            _logger.LogInformation($"Timesheet mis à jour pour l'utilisateur {appUserId}, projet {input.IdProjet}, jour {input.TsDay:yyyy-MM-dd}");
            return Ok(new { Id = newId, Message = "Feuille de temps mise à jour avec succès" });
        }
        else
        {
            _logger.LogInformation($"Timesheet créé pour l'utilisateur {appUserId}, projet {input.IdProjet}, jour {input.TsDay:yyyy-MM-dd}");
            return Ok(new { Id = newId, Message = "Feuille de temps ajoutée avec succès" });
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Erreur lors de l'ajout d'un timesheet");
        return StatusCode(500, $"Erreur interne: {ex.Message}");
    }
} 
    }
}
