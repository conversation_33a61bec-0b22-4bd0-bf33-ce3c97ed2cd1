import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/myorganisationcontroller.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:flutter_advanced_drawer/flutter_advanced_drawer.dart';
import 'package:tmt_mobile/widgets/drawer.dart';

import '../controllers/menucontroller.dart';

class MenuScreen extends GetView<Menucontroller> {
  const MenuScreen({super.key});
  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    double screenWidht = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    final advancedDrawerController = AdvancedDrawerController();

    GlobalController globalController = Get.find<GlobalController>();
    Get.put(MyOrganisationController(globalController));

    void handleMenuButtonPressed() {
      advancedDrawerController.showDrawer();
    }

    return AdvancedDrawer(
      openRatio: globalController.devType == "tablet" ? 0.26 : 0.55,
      backdrop: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [MyColors.MainRedBig, MyColors.MainRedBig.withOpacity(0.8)],
          ),
        ),
      ),
      drawer: SideMenu(advancedDrawerController: advancedDrawerController),
      controller: advancedDrawerController,
      animationCurve: Curves.easeInOut,
      animationDuration: const Duration(milliseconds: 300),
      animateChildDecoration: true,
      rtlOpening: false,
      disabledGestures: false,
      childDecoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(16))),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          actions: <Widget>[
            PopupMenuButton<String>(
              icon: Icon(
                Icons.language,
                color: Colors.white,
              ),
              onSelected: (String choice) {},
              itemBuilder: (BuildContext context) {
                return [
                  PopupMenuItem<String>(
                    value: "fr",
                    child: Text(
                      globalController.lang.value == "fr"
                          ? "Francais"
                          : "Frensh",
                      style: globalController.lang.value == "fr"
                          ? TextStyle(
                              fontWeight: FontWeight.bold,
                              color: MyColors.thirdColor)
                          : TextStyle(
                              fontWeight: FontWeight.normal,
                            ),
                    ),
                    onTap: () => globalController.lang.value = "fr",
                  ),
                  PopupMenuItem<String>(
                    value: "en",
                    child: Text(
                      globalController.lang.value == "fr"
                          ? "Anglais"
                          : "English",
                      style: globalController.lang.value == "en"
                          ? TextStyle(
                              fontWeight: FontWeight.bold,
                              color: MyColors.thirdColor)
                          : TextStyle(
                              fontWeight: FontWeight.normal,
                            ),
                    ),
                    onTap: () => globalController.lang.value = "en",
                  ),
                ];
              },
            )
          ],
          centerTitle: true,
          title: Obx(() => Text(controller.screenindex == 1
              ? globalController.lang == "fr"
                  ? "Mes Organisations"
                  : "My Organisations"
              : controller.screenindex == 2
                  ? globalController.lang == "fr"
                      ? "Demande de Congés"
                      : "Vacation Request"
                  : controller.screenindex == 3
                      ? globalController.lang == "fr"
                          ? "Demande de Travail à Distance"
                          : "Remote Work Request"
                      : controller.screenindex == 4
                          ? globalController.lang == "fr"
                              ? "Soumettre l'Humeur"
                              : "Submit Mood"
                          : controller.screenindex == 5
                              ? globalController.lang == "fr"
                                  ? "Historique de Congés"
                                  : "Vacation History"
                              : controller.screenindex == 6
                                  ? globalController.lang == "fr"
                                      ? "Historique de Télétravail"
                                      : "Remote Request History"
                                  : controller.screenindex == 7
                                      ? globalController.lang == "fr"
                                          ? "Validation des Demandes de Congé "
                                          : "Vacation Request Validation"
                                      : controller.screenindex == 8
                                          ? globalController.lang == "fr"
                                              ? "Validation des Demandes de Télétravail"
                                              : "Remote Work Request Validation"
                                          : controller.screenindex == 9
                                              ? globalController.lang == "fr"
                                                  ? "Espace Manger"
                                                  : "Manager Space"
                                              : controller.screenindex == 10
                                                  ? globalController.lang ==
                                                          "fr"
                                                      ? "Partagez Vos Avis"
                                                      : "Share Your Opinion"
                                                  : controller.screenindex == 11
                                                      ? globalController.lang ==
                                                              "fr"
                                                          ? "Historique de l'humeur"
                                                          : "Mood History"
                                                      : globalController.lang ==
                                                              "fr"
                                                          ? "Saisie de temps"
                                                          : "Timesheet")),
          backgroundColor: MyColors.MainRedBig,
          foregroundColor: Colors.white,
          leading: IconButton(
            onPressed: handleMenuButtonPressed,
            icon: ValueListenableBuilder<AdvancedDrawerValue>(
              valueListenable: advancedDrawerController,
              builder: (_, value, __) {
                return AnimatedSwitcher(
                  duration: Duration(milliseconds: 250),
                  child: Icon(
                    value.visible ? Icons.clear : Icons.menu,
                    key: ValueKey<bool>(value.visible),
                  ),
                );
              },
            ),
          ),
        ),
        drawer: SideMenu(advancedDrawerController: advancedDrawerController),
        body: Obx(() => Stack(
              children: [
                controller.Screens[controller.screenindex.value],
              ],
            )),
      ),
    );
  }
}
