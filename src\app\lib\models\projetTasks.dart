class ProjectTasks {
  int id;
  int idprojetlot;
  String name;

  ProjectTasks(
      {required this.id, required this.idprojetlot, required this.name});

  factory ProjectTasks.fromJson(Map<String, dynamic> json) {
    return ProjectTasks(
      id: json['id'],
      idprojetlot: json['idprojetlot'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'idprojetlot': idprojetlot,
      'name': name,
    };
  }
}
