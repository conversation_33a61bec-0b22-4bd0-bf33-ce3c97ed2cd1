﻿using System;
using System.Globalization;

namespace TMT.Mobile.Core.Extensions
{
  public static class DateTimeExtension
  {
    public static DateTime EndOfTheMonth(this DateTime dt)
    {
      return new DateTime(dt.Year, dt.Month, DateTime.DaysInMonth(dt.Year, dt.Month));
    }
    public static DateTime BegginingOfTheMonth(this DateTime dt)
    {
      return new DateTime(dt.Year, dt.Month, 1);
    }
    public static DateTime GetSunday(this DateTime dt)
    {
      if (dt.DayOfWeek == DayOfWeek.Sunday)
        return dt;
      else
      {
        return dt.AddDays(+1).GetMonday();
      }
    }
    public static DateTime GetMonday(this DateTime dt)
    {
      if (dt.DayOfWeek == DayOfWeek.Monday)
        return dt.Date;
      else
      {
        return dt.AddDays(-1).GetMonday();
      }
    }
    public static DateTime? GetMonday(this DateTime? dt)
    {
      if (dt == null)
        return null;
      if (dt.Value.DayOfWeek == DayOfWeek.Monday)
        return dt;
      else
      {
        return dt.Value.AddDays(-1).GetMonday();
      }
    }

    public static string ToPorstgresStringDate(this DateTime? dt)
    {
      if (dt == null)
        return "null";
      return "'" + dt.Value.Year + "-" + dt.Value.Month + "-" + dt.Value.Day + "'";
    }
    public static string ToPorstgresStringDate(this DateTime dt)
    {
      return "'" + dt.Year + "-" + dt.Month + "-" + dt.Day + "'";
    }
    public static int MonthWorkingDays(this DateTime dt)
    {
      int year = dt.Year;
      int month = dt.Month;
      int daysInMonth = 0;
      int days = DateTime.DaysInMonth(year, month);
      for (int i = 1; i <= days; i++)
      {
        DateTime day = new DateTime(year, month, i);
        if (day.DayOfWeek != DayOfWeek.Sunday && day.DayOfWeek != DayOfWeek.Saturday)
        {
          daysInMonth++;
        }
      }
      return daysInMonth;
    }
    public static int WorkingDays(this DateTime dt)
    {
      int year = dt.Year;
      int month = dt.Month;
      int daysInMonth = 0;
      for (int i = 1; i <= dt.Day; i++)
      {
        DateTime day = new DateTime(year, month, i);
        if (day.DayOfWeek != DayOfWeek.Sunday && day.DayOfWeek != DayOfWeek.Saturday)
        {
          daysInMonth++;
        }
      }
      return daysInMonth;
    }

    public static string GetMonthShortName(this DateTime dt, string culture = "fr-fr")
    {
      string strMonthName = dt.ToString("MMM", CultureInfo.CreateSpecificCulture(culture));
      return strMonthName;
    }
    public static string GetMonthFullName(this DateTime dt, string culture = "fr-fr")
    {
      string strMonthName = dt.ToString("MMMM", CultureInfo.CreateSpecificCulture(culture));
      return strMonthName;
    }
    public static string ToHTMLString(this DateTime dt)
    {
      return dt.ToString("yyyy-MM-dd");
    }
    public static string ToHTMLString(this DateTime? dt)
    {
      if (dt.HasValue)
        return dt.Value.ToString("yyyy-MM-dd");
      return string.Empty;
    }
    public static string ToHTMLMonthString(this DateTime dt)
    {
      return dt.ToString("yyyy-MM");
    }
    public static string ToHTMLMonthString(this DateTime? dt)
    {
      if (dt.HasValue)
        return dt.Value.ToString("yyyy-MM");
      return string.Empty;
    }
    public static DateTime AddWorkingDays(this DateTime dateTime, int days)
    {
      DateTime result = dateTime;
      for (int i = 1; i <= days; i++)
      {
        result = result.AddDays(1);
        if (result.DayOfWeek == DayOfWeek.Saturday)
        {
          result = result.AddDays(2);
        }
        if (result.DayOfWeek == DayOfWeek.Sunday)
        {
          result = result.AddDays(1);
        }
      }
      return result;
    }
    public static int GetIso8601WeekOfYear(this DateTime time)
    {

      DayOfWeek day = CultureInfo.InvariantCulture.Calendar.GetDayOfWeek(time);
      if (day >= DayOfWeek.Monday && day <= DayOfWeek.Wednesday)
      {
        time = time.AddDays(3);
      }
      return CultureInfo.InvariantCulture.Calendar.GetWeekOfYear(time, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
    }
    public static DateTime AddBusinessDays(this DateTime startDate,
                                         double businessDays)
    {
      int direction = Math.Sign(businessDays);
      if (direction == 1)
      {
        if (startDate.DayOfWeek == DayOfWeek.Saturday)
        {
          startDate = startDate.AddDays(2);
          businessDays = businessDays - 1;
        }
        else if (startDate.DayOfWeek == DayOfWeek.Sunday)
        {
          startDate = startDate.AddDays(1);
          businessDays = businessDays - 1;
        }
      }
      else
      {
        if (startDate.DayOfWeek == DayOfWeek.Saturday)
        {
          startDate = startDate.AddDays(-1);
          businessDays = businessDays + 1;
        }
        else if (startDate.DayOfWeek == DayOfWeek.Sunday)
        {
          startDate = startDate.AddDays(-2);
          businessDays = businessDays + 1;
        }
      }

      int initialDayOfWeek = (int)startDate.DayOfWeek;

      int weeksBase = Math.Abs((int)businessDays / 5);
      int addDays = Math.Abs((int)businessDays % 5);

      if ((direction == 1 && addDays + initialDayOfWeek > 5) ||
           (direction == -1 && addDays >= initialDayOfWeek))
      {
        addDays += 2;
      }

      int totalDays = (weeksBase * 7) + addDays;
      var result = startDate.AddDays(totalDays * direction);
      if( result.AddMinutes(-1).DayOfWeek == DayOfWeek.Sunday )
      {
        result = result.AddDays(-2);
      }
      if (result.AddMinutes(-1).DayOfWeek == DayOfWeek.Saturday)
      {
        result = result.AddDays(-1);
      }
      return result;
    }
  }
}
