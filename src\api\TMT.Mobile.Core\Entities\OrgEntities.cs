using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
public class OrgEntity : IEntity
  {
    public int Id { get; set; }
    public int IdCounterParty { get; set; }
    public bool Isdeleted { get; set; } = false;
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public virtual Counterparties? CounterParty { get; set; }
   // public virtual ICollection<EntityParameter> Parameters { get; set; }
    public virtual ICollection<Projects> Projects { get; set; }
    public virtual ICollection<Bill> Bills { get; set; }
    public virtual ICollection<Projectfees> Fees { get; set; }
    public virtual ICollection<Payment> Payments { get; set; }
    public virtual ICollection<SupplierPayment> SupplierPayments { get; set; }
    public virtual ICollection<Appusers> Appusers { get; set; }
    public OrgEntity()
    {
    //  Parameters = new HashSet<EntityParameter>();
      Projects = new HashSet<Projects>();
      Fees = new HashSet<Projectfees>();
      Payments = new HashSet<Payment>();
      SupplierPayments = new HashSet<SupplierPayment>();
      Appusers = new HashSet<Appusers>();
    }
  }
}
