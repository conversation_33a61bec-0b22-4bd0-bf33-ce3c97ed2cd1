import 'package:email_validator/email_validator.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/menucontroller.dart';
import 'package:tmt_mobile/controllers/signincontroller.dart';
import 'package:tmt_mobile/models/userdata.dart';
import 'package:tmt_mobile/screens/MenuScreen.dart';
import 'package:tmt_mobile/screens/codeValidationScreen.dart';
import 'package:tmt_mobile/screens/resetPassword.dart';

import 'package:tmt_mobile/screens/signupscreen.dart';
import 'package:tmt_mobile/widgets/buttonwithicon.dart';

import '../utils/myColors.dart';
import '../widgets/big_text.dart';
import '../widgets/inputfield.dart';

class SignInScreen extends GetView<SignInController> {
  const SignInScreen({super.key});
  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    GlobalController globalController =
        Get.put(GlobalController(), permanent: true);
    Get.put(SignInController(globalController));

    Future signin() async {
      bool valid = controller.SignInKey.currentState!.validate();
      if (valid) {
        userLogin user = userLogin(
            email: controller.email.value.text,
            password: controller.password.value.text);
        var checklog = await controller.onSubmit(user);
        if (checklog == 1 &&
            globalController.appuser.value.isvalidated == true) {
          await Get.putAsync<Menucontroller>(() async => Menucontroller(),
              permanent: true);
          Menucontroller menuController = Get.find<Menucontroller>();
          menuController.screenindex.value = 1;
          Get.offAll(MenuScreen());
        } else if (checklog == 2 &&
            globalController.appuser.value.isvalidated == false) {
          Get.offAll(CodeValidationScreen());
        }
      }
    }

    return Scaffold(
        resizeToAvoidBottomInset:
            globalController.devType.value == "tablet" ? true : false,
        backgroundColor: Color(0xffffffff),
        body: globalController.devType.value == "tablet"
            ? signinscreenTablet(
                screenHeight, screenWidth, globalController, signin)
            : signinScreenAndroid(
                screenWidth, screenHeight, globalController, signin));
  }

  Widget signinScreenAndroid(double screenWidth, double screenHeight,
      GlobalController globalController, Future<dynamic> Function() signin) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[50]!,
            Colors.white,
            Colors.grey[50]!,
          ],
          stops: [0.0, 0.5, 1.0],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: screenWidth * .06),
          child: Obx(
            () => SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              child: Form(
                key: controller.SignInKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: screenHeight * .08),

                    // Logo and Welcome Section
                    Container(
                      child: Column(
                        children: [
                          // Logo

                          // Welcome Text
                          Text(
                            globalController.lang.value == "fr"
                                ? "Bon retour !"
                                : "Welcome Back!",
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: MyColors.mainblack,
                              fontFamily: "aileron",
                            ),
                          ),

                          SizedBox(height: 30),

                          Text(
                            globalController.lang.value == "fr"
                                ? "Connectez-vous à votre compte"
                                : "Sign in to your account",
                            style: TextStyle(
                              fontSize: 16,
                              color: MyColors.Strokecolor,
                              fontFamily: "aileron",
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Input Fields Container
                    Container(
                      padding: EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 15,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Email Input
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Myinput(
                              labelText: "Email",
                              controller: controller.email.value,
                              icon: Icons.email_outlined,
                              validate: (v) =>
                                  v != null && !EmailValidator.validate(v)
                                      ? globalController.lang.value == "fr"
                                          ? 'Entrer un email valide'
                                          : 'Enter a valid email'
                                      : null,
                              fontsize: 16,
                            ),
                          ),

                          SizedBox(height: 20),

                          // Password Input
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Myinput(
                              labelText: globalController.lang.value == "fr"
                                  ? "Mot de passe"
                                  : "Password",
                              controller: controller.password.value,
                              obscureText: controller.passwtogg2.value,
                              icon: Icons.lock_outline,
                              Suffixicon: Icons.visibility_outlined,
                              validate: (v) => controller.validateThese(v!),
                              Suffixiconoff: Icons.visibility_off_outlined,
                              suffixiconfun: () {
                                controller.passwtogg2.value =
                                    !controller.passwtogg2.value;
                              },
                              fontsize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Error Message
                    Visibility(
                      visible: controller.showError.value,
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16),
                        margin: EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.red[200]!, width: 1),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: MyColors.MainRedBig,
                              size: 24,
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                controller.errormsg.value,
                                style: TextStyle(
                                  color: MyColors.MainRedBig,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: "aileron",
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Sign In Button
                    controller.loading.value == false
                        ? Container(
                            width: double.infinity,
                            height: 56,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: LinearGradient(
                                colors: [
                                  MyColors.MainRedBig,
                                  MyColors.MainRedSecond
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: MyColors.MainRedBig.withOpacity(0.3),
                                  blurRadius: 12,
                                  offset: Offset(0, 6),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed: () async {
                                await signin();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                              child: Text(
                                globalController.lang.value == "fr"
                                    ? "Se connecter"
                                    : "Sign In",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: "aileron",
                                ),
                              ),
                            ),
                          )
                        : SizedBox(
                            height: 56,
                            child: Center(
                              child: CircularProgressIndicator(
                                color: MyColors.MainRedBig,
                                strokeWidth: 3,
                              ),
                            ),
                          ),

                    SizedBox(height: screenHeight * 0.025),

                    // Forgot Password Link
                    TextButton(
                      onPressed: () {
                        Get.to(ResetPasswordScreen());
                      },
                      style: TextButton.styleFrom(
                        padding:
                            EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        globalController.lang.value == "fr"
                            ? "Mot de passe oublié ?"
                            : "Forgot password?",
                        style: TextStyle(
                          color: MyColors.thirdColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: "aileron",
                        ),
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.03),

                    // Divider with "or"
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 1,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.transparent,
                                  MyColors.Strokecolor.withOpacity(0.3),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            globalController.lang.value == "fr" ? "ou" : "or",
                            style: TextStyle(
                              fontSize: 16,
                              color: MyColors.Strokecolor,
                              fontWeight: FontWeight.w500,
                              fontFamily: "aileron",
                            ),
                          ),
                        ),
                        Expanded(
                          child: Container(
                            height: 1,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  MyColors.Strokecolor.withOpacity(0.3),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: screenHeight * 0.025),

                    // Sign Up Link
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: MyColors.thirdColor.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: MyColors.thirdColor.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            globalController.lang.value == "fr"
                                ? "Vous n'avez pas de compte ?"
                                : "Don't have an account?",
                            style: TextStyle(
                              color: MyColors.Strokecolor,
                              fontSize: 16,
                              fontFamily: "aileron",
                            ),
                          ),
                          SizedBox(height: 8),
                          TextButton(
                            onPressed: () {
                              Get.offAll(SignupScreen());
                            },
                            style: TextButton.styleFrom(
                              backgroundColor:
                                  MyColors.thirdColor.withOpacity(0.1),
                              padding: EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 24),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              globalController.lang.value == "fr"
                                  ? "Créer un compte"
                                  : "Create Account",
                              style: TextStyle(
                                color: MyColors.thirdColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                fontFamily: "aileron",
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.04),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  SafeArea signinscreenTablet(double screenHeight, double screenWidth,
      GlobalController globalController, Future<dynamic> Function() signin) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(30),
        child: Center(
          child: Obx(() => SingleChildScrollView(
                child: Form(
                    key: controller.SignInKey,
                    child: Container(
                        height: screenHeight * 0.6,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  width: screenWidth * 0.4,
                                  decoration: BoxDecoration(
                                      color: Color(0xffffffff),
                                      borderRadius: BorderRadius.circular(10)),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        "assets/logotmt.svg",
                                        fit: BoxFit.cover,
                                        width: screenWidth * 0.30,
                                      ),
                                      SizedBox(
                                        height: screenHeight * 0.03,
                                      ),
                                      BigText(
                                        text: "TM/T Software ",
                                        color: Colors.black,
                                        size: 40,
                                      ),
                                      SizedBox(
                                        height: screenHeight * 0.01,
                                      ),
                                      Center(
                                        child: Text(
                                          "PILOTEZ VOS PROJETS AVEC LES BONS INDICATEURS  ",
                                          style: TextStyle(fontSize: 17),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(width: screenWidth * 0.03),
                                VerticalDivider(
                                  width: 7,
                                  thickness: 2,
                                  color: MyColors.Strokecolor.withOpacity(0.2),
                                  indent: 10, //spacing at the start of divider
                                  endIndent: 10,
                                ),
                                SizedBox(width: screenWidth * 0.08),
                                SizedBox(
                                  width: screenWidth * 0.4,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: screenHeight * .04,
                                      ),
                                      BigText(
                                        text:
                                            globalController.lang.value == "fr"
                                                ? "Accéder au compte"
                                                : "Sign in",
                                        color: Colors.black,
                                        textAlign: TextAlign.left,
                                        size: screenHeight * 0.038,
                                      ),
                                      SizedBox(
                                        height: screenHeight * 0.03,
                                      ),
                                      SizedBox(
                                          width: screenWidth * 0.90,
                                          child: Myinput(
                                            labelText: "email",
                                            controller: controller.email.value,
                                            icon: Icons.mail,
                                            validate: (v) => v != null &&
                                                    !EmailValidator.validate(v)
                                                ? 'entrer un email valide'
                                                : null,
                                          )),
                                      SizedBox(
                                        height: screenHeight * 0.02,
                                      ),
                                      SizedBox(
                                          width: screenWidth * 0.90,
                                          child: Myinput(
                                            labelText:
                                                globalController.lang.value ==
                                                        "fr"
                                                    ? "mot de passe"
                                                    : "password",
                                            controller:
                                                controller.password.value,
                                            obscureText:
                                                controller.passwtogg2.value,
                                            icon: Icons.lock,
                                            Suffixicon: Icons.visibility,
                                            validate: (v) =>
                                                controller.validateThese(v!),
                                            Suffixiconoff: Icons.visibility_off,
                                            suffixiconfun: () {
                                              controller.passwtogg2.value =
                                                  !controller.passwtogg2.value;
                                            },
                                          )),
                                      SizedBox(
                                        height: screenHeight * 0.02,
                                      ),
                                      Visibility(
                                        visible: controller.showError.value,
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Center(
                                              child: Text(
                                            controller.errormsg.value,
                                            style: TextStyle(
                                                color: Colors.red,
                                                fontSize: 16),
                                          )),
                                        ),
                                      ),
                                      controller.loading.value == false
                                          ? ButtonWithIcon(
                                              onPressed: () async {
                                                await signin();
                                              },
                                              text:
                                                  globalController.lang.value ==
                                                          "fr"
                                                      ? "Se connecter"
                                                      : "Connect",
                                              mainColor: MyColors.MainRedBig,
                                              fontSize: 18,
                                              textcolor: Colors.white,
                                              width: screenWidth * 0.90,
                                              height: screenHeight * 0.065,
                                            )
                                          : Center(
                                              child: CircularProgressIndicator(
                                              color: MyColors.thirdColor,
                                            )),
                                      SizedBox(
                                        height: screenHeight * 0.02,
                                      ),
                                      Center(
                                          child: RichText(
                                              textAlign: TextAlign.center,
                                              text: TextSpan(
                                                  style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 16,
                                                  ),
                                                  children: [
                                                    TextSpan(
                                                      text: globalController
                                                                  .lang.value ==
                                                              "fr"
                                                          ? "Mot de passe oublié ?"
                                                          : "Forgot password ?",
                                                      style: TextStyle(
                                                        color: MyColors
                                                            .BordersGrey,
                                                      ),
                                                    ),
                                                    TextSpan(
                                                        recognizer:
                                                            TapGestureRecognizer()
                                                              ..onTap = () =>
                                                                  Get.to(
                                                                      ResetPasswordScreen()),
                                                        text: globalController
                                                                    .lang
                                                                    .value ==
                                                                "fr"
                                                            ? "Réinitialiser le mot de passe"
                                                            : "Reset password",
                                                        style: TextStyle(
                                                            color: MyColors
                                                                .thirdColor,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            decoration:
                                                                TextDecoration
                                                                    .underline)),
                                                  ]))),
                                      SizedBox(
                                        height: screenHeight * 0.02,
                                      ),
                                      Row(children: <Widget>[
                                        Expanded(
                                            child: Divider(
                                          color:
                                              MyColors.Strokecolor.withOpacity(
                                                  0.5), //color of divider
                                          height: 5, //height spacing of divider
                                          thickness:
                                              2, //thickness of divier line
                                          indent:
                                              25, //spacing at the start of divider
                                          endIndent:
                                              25, //spacing at the end of divider
                                        )),
                                        Text(
                                          globalController.lang.value == "fr"
                                              ? "ou"
                                              : "or",
                                          style: TextStyle(fontSize: 18),
                                        ),
                                        Expanded(
                                            child: Divider(
                                          color:
                                              MyColors.Strokecolor.withOpacity(
                                                  0.5), //color of divider
                                          height: 5, //height spacing of divider
                                          thickness:
                                              2, //thickness of divier line
                                          indent:
                                              25, //spacing at the start of divider
                                          endIndent:
                                              25, //spacing at the end of divider
                                        )),
                                      ]),
                                      SizedBox(
                                        height: screenHeight * 0.02,
                                      ),
                                      Center(
                                        child: RichText(
                                            textAlign: TextAlign.center,
                                            text: TextSpan(
                                                style: TextStyle(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                ),
                                                children: [
                                                  TextSpan(
                                                    text: globalController
                                                                .lang.value ==
                                                            "fr"
                                                        ? "Vous n'avez pas de compte?"
                                                        : "Don't have an account?",
                                                    style: TextStyle(
                                                        color: MyColors
                                                            .BordersGrey),
                                                  ),
                                                  TextSpan(
                                                      recognizer:
                                                          TapGestureRecognizer()
                                                            ..onTap = () {
                                                              Get.offAll(
                                                                  SignupScreen());
                                                            },
                                                      text: globalController
                                                                  .lang.value ==
                                                              "fr"
                                                          ? 'Inscrivez-vous.'
                                                          : "Sign up",
                                                      style: TextStyle(
                                                          color: MyColors
                                                              .thirdColor)),
                                                ])),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ))),
              )),
        ),
      ),
    );
  }
}
