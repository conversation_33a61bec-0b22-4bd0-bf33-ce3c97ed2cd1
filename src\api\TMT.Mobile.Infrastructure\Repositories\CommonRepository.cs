using System.Data.Entity;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities.DTOs.Authentification;

using TMT.Mobile.Infrastructure;

namespace TMT.Mobile.Infrastructure.Repositories
{
  public class CommonRepository : CoreRepository<User, TmtMobileContext>
  {
    public CommonRepository(TmtMobileContext context) : base(context)
    {
    }
    public string HealthCheck()
    {
      var usersCount = context.Users.Count();
      var orgCount = context.Organizations.Count();
      var result = $"Users Count: {usersCount} & Org Count: {orgCount}";
      return result;
    }
  }
}
