﻿using TMT.Mobile.Core;
using System.Linq.Expressions;

using TMT.Mobile.Core;
namespace TMT.Mobile.Infrastructure
{
  public interface ICoreRepository<T> where T : class, IEntity
    {
        Task<List<T>> GetAll();
        Task<T> Get(int id);
        Task<T> Add(T entity);
        Task<T> Update(T entity);
        Task<T> Delete(int id);
        IQueryable<T> FindByCondition(Expression<Func<T, bool>> expression);
    }
}
    