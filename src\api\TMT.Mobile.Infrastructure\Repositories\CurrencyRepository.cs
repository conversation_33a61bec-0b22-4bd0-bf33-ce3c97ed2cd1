using Microsoft.EntityFrameworkCore;

using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class CurrencyRepository : CoreRepository<Currency, DynamicDbContext>
    {

        public CurrencyRepository(DynamicDbContext context) : base(context)
        {

        }

        public async Task<IEnumerable<Currency>> GetAllAsync()
        {
            return await context.Currencies.ToListAsync();
        }

    }
}