parameters:
- name: coreDBname
  type: string
- name: coreDBHost
  type: string
- name: coreDBUser
  type: string
- name: coreDBPassword
  type: string
- name: dependsOn
  type: string
  
jobs:
- deployment: OrgSchemaUpdate
  displayName: "Org DB Updates"
  environment: 'TMT-QA'
  dependsOn: ${{parameters.dependsOn}}
  pool:
    vmImage: 'ubuntu-latest'  
  variables:
    PGPASSWORD: ${{parameters.coreDBPassword}}    
  strategy:
    runOnce:
      deploy:
          steps:
          - checkout: self
          - script: echo 'psql -h ${{ parameters.coreDBHost}} -U ${{ parameters.coreDBUser}} --csv -p 5432 ${{ parameters.coreDBname}} -c "SELECT id, name, connectionstring FROM Organizations WHERE isdeleted = false"'
          - task: PowerShell@2
            displayName: Update Org Schema 
            name: connectionStringsTask
            inputs:
              targetType: 'inline'
              script: |

                Function Start-AzDOPipeline
                {
                    param(
                        [string]$pipelineBaseUrl,
                        [string]$branchName,
                        [string]$sourceVersion,
                        [string]$accessToken,
                        [hashtable]$templateParameters
                    )

                    $body = @{}
                    $body["stagesToSkip"] = @{}
                    $body["resources"] = @{"repositories" = @{ "self" = @{ "refname" = "$branchName"; "version" = "$sourceVersion" }}}
                    $body["templateParameters"] = $templateParameters
                    $body["variables"] = @{}

                    $bodyJson = $($body | ConvertTo-Json -Depth 100)

                    Write-Host $bodyJson
                    $bodyJson

                    $url = "$pipelineBaseUrl/runs?api-version=6.0-preview.1"

                    $pipeline = Invoke-RestMethod -Uri $url -Headers @{Authorization = "Bearer $accessToken"; "Content-Type" = "application/json"} -Method Post -Body $bodyJson
                    Write-Host  $pipeline 

                    Write-Host "Pipeline = $($pipeline.id)"

                    return $pipeline
                }
                Function Wait-AzDOPipelineCompleted
                {
                    param(
                        [object]$pipelineBaseUrl,
                        [string]$accessToken,
                        [string]$runId
                    )

                    $url = "$pipelineBaseUrl/runs/$($runId)?api-version=6.0-preview.1"

                    DO
                    {
                        $pipelineStatus = Invoke-RestMethod -Uri $url -Headers @{Authorization = "Bearer $accessToken"}
                        $status = $($pipelineStatus | ConvertTo-Json -Depth 100 | ConvertFrom-Json)
                        Start-Sleep -s 15
                        
                    } While($status.state -ne "completed")

                    return $status
                }
                Write-Host '##########################'
                Write-Host 'Getting Organizations connection strings'
                Write-Host 'coreDBHost => ${{ parameters.coreDBHost}}'
                Write-Host 'coreDBUser => ${{ parameters.coreDBUser}}'
                Write-Host 'coreDBname => ${{ parameters.coreDBname}}'               
                $result = psql -h ${{ parameters.coreDBHost}} -U ${{ parameters.coreDBUser}} --csv -p 5432 ${{ parameters.coreDBname}} -c "SELECT id, guid, name, connectionstring FROM Organizations WHERE isdeleted = false" | ConvertFrom-Csv 
                $env:AZURE_DEVOPS_EXT_PAT = '$(System.AccessToken)'

                $orgUrl = "https://dev.azure.com/TMTSoftware/"
                $projectname = "TMTracker"
                $pipelineId = "34"
                $branchName = "$(Build.SourceBranch)"
                $accessToken = "$(System.AccessToken)"
                Write-Host $orgUrl
                Write-Host $projectname
                Write-Host $pipelineId
                Write-Host $branchName

                $baseUrl = "$orgUrl$projectname/_apis/pipelines/$pipelineId"

                foreach ($org in $result)
                {      
                  Write-Host '##########################'
                  Write-Host $org.guid
                  Write-Host $org.connectionstring

                  $templateParameters = @{}
                  $templateParameters["orgConnectionString"] =  '' +$org.connectionstring
                  $templateParameters["orgName"] = '' + $org.guid

                  $pipelineRun = Start-AzDOPipeline -pipelineBaseUrl $baseUrl -branchName $branchName -sourceVersion $sourceVersion -accessToken $(System.AccessToken) -templateParameters $templateParameters

                  $pipelineUrl = "$orgUrl$projectname/_build/results?buildId=$($pipelineRun.id)"
                  Write-Host ""
                  Write-Host "The pipeline was started. It may require an approval. Please check the url: $pipelineUrl" -ForegroundColor "DarkCyan"
                  Write-Host ""

                }

