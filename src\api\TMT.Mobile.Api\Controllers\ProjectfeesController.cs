using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Api.Services;
using System.Text;
using System.Security.Claims;

namespace TMT.Mobile.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class ProjectfeesController : ControllerBase
    {
        private readonly DynamicDbContext _dynamicDb;
        private readonly ProjectfeesRepository _projectfeesRepository;
        private readonly AppUserRepository _appUserRepository;
        private readonly AppvariableRepository _appvariableRepository;
        private readonly ILogger<ProjectfeesController> _logger;
        private readonly IEmailService _emailService;

        public ProjectfeesController(
            ILogger<ProjectfeesController> logger,
            DynamicDbContext dynamicDb,
            ProjectfeesRepository projectfeesRepository,
            AppUserRepository appUserRepository,
            AppvariableRepository appvariableRepository,
            IEmailService emailService)
        {
            _projectfeesRepository = projectfeesRepository;
            _appUserRepository = appUserRepository;
            _appvariableRepository = appvariableRepository;
            _dynamicDb = dynamicDb ?? throw new ArgumentNullException(nameof(dynamicDb));
            _logger = logger;
            _emailService = emailService;

        }

        private string GetUserEmail()
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email);
            if (emailClaim == null)
                throw new UnauthorizedAccessException("Email not found in token.");

            return emailClaim.Value;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var requests = await _projectfeesRepository.GetAllAsync();
            return Ok(requests);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            var requests = await _projectfeesRepository.GetByIdAsync(id);
            if (requests == null)
                return NotFound("expense request not found.");

            return Ok(requests);
        }

 [HttpPost]
public async Task<IActionResult> CreateProjectfees([FromBody] ProjectfeesDto requestDto)
{
    if (!ModelState.IsValid)
        return BadRequest(ModelState);

    try
    {
        var email = GetUserEmail();
        
        var waitingStatus = await _dynamicDb.Appvariables
            .FirstOrDefaultAsync(v => v.Name == "Waiting" && v.Category == "PROJECT_FEES_STATUS");
        
        if (waitingStatus == null)
            return BadRequest("Status 'Waiting' not found.");

        if (requestDto.IdFeestype.HasValue)
        {
            var feesTypeExists = await _dynamicDb.Appvariables
                .AnyAsync(v => v.Id == requestDto.IdFeestype.Value);
            if (!feesTypeExists)
                return BadRequest($"Fees type with ID {requestDto.IdFeestype} does not exist.");
        }

        if (requestDto.IdCurrency.HasValue)
        {
            var currencyExists = await _dynamicDb.Currencies
                .AnyAsync(c => c.Id == requestDto.IdCurrency.Value);
            if (!currencyExists)
                return BadRequest($"Currency with ID {requestDto.IdCurrency} does not exist.");
        }

        if (requestDto.IdSupplier.HasValue)
        {
            var supplierExists = await _dynamicDb.counterparties
                .AnyAsync(s => s.Id == requestDto.IdSupplier.Value);
            if (!supplierExists)
                return BadRequest($"Supplier with ID {requestDto.IdSupplier} does not exist.");
        }

        var employee = await _dynamicDb.Appusers
            .Where(u => u.Email == email)
            .Select(u => new {
                u.Id,
                u.Firstname,
                u.Lastname,
                u.Email
            })
            .FirstOrDefaultAsync();
            
        if (employee == null)
            return BadRequest("Employee not found.");

               dynamic manager = null;

        if (requestDto.IdAssignedto.HasValue)
        {
            manager = await _dynamicDb.Appusers
                .Where(u => u.Id == requestDto.IdAssignedto)
                .Select(u => new {
                    u.Id,
                    u.Firstname,
                    u.Lastname,
                    u.Email,
                    u.DefaultLanguage
                })
                .FirstOrDefaultAsync();
        }
        else
        {
            var managerDto = await _appUserRepository.GetManagerByUserEmail(email);
            if (managerDto != null)
            {
                manager = new {
                    Id = managerDto.Id,
                    Firstname = managerDto.FirstName,
                    Lastname = managerDto.LastName,
                    Email = managerDto.Email,
                    DefaultLanguage = managerDto.DefaultLanguage
                };
            }
        }

        var now = DateTime.UtcNow;
        
        var projectfees = new Projectfees
        {
            Feesdate = requestDto.Feesdate,
            Feesamount = requestDto.Feesamount,
            IdFeestype = requestDto.IdFeestype,
            IdAssignedto =  manager?.Id ?? requestDto.IdAssignedto,
            Comment = requestDto.Comment,
            Reference = requestDto.Reference,
            Createdby = employee.Id.ToString(),
            Createddate = now,
            Updatedby = employee.Id.ToString(),
            Updateddate = now,
            IdStatus = waitingStatus.Id,
            Feesamounttaxincl = requestDto.Feesamounttaxincl,
            IdSupplier = requestDto.IdSupplier,
            IdCurrency = requestDto.IdCurrency
        };

        await _projectfeesRepository.AddAsync(projectfees);
        
        if (manager != null && !string.IsNullOrEmpty(manager.Email))
        {
            await SendProjectfeesEmail(employee, manager, projectfees, manager.DefaultLanguage);
        }

        return CreatedAtAction(nameof(GetById), new { id = projectfees.Id }, projectfees);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error creating project fees");
        return StatusCode(500, "An error occurred while creating the project fees. " + ex.Message);
    }
}

private async Task SendProjectfeesEmail(dynamic employee, dynamic manager, Projectfees projectfees, string? defaultLanguage)
{
    try
    {
        string language = (defaultLanguage?.ToLower()) switch
        {
            "en" => "en",
            "fr" => "fr",
            _ => "fr"
        };

        string emailSubject;
        string emailBody;

        string projectDetails = $@"<ul>
            <li>Amount: {projectfees.Feesamount:C}</li>
            <li>Date: {projectfees.Feesdate:dd/MM/yyyy}</li>
            <li>Comment: {projectfees.Comment}</li>
            <li>Reference: {projectfees.Reference}</li>
        </ul>";

        if (language == "en")
        {
            emailSubject = $"New Project Fees from {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Project Fees Notification</h2>
                    <p>A new project fee request has been submitted by {employee.Firstname} {employee.Lastname}.</p>
                    {projectDetails}
                    <p>Please review this request in the HR Management System.</p>
                    <p>Best regards,<br><strong>Human Resources Department</strong></p>
                </div>";
        }
        else
        {
            emailSubject = $"Nouvelle demande de frais de {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Notification de demande de frais</h2>
                    <p>Une nouvelle demande de frais de projet a été soumise par {employee.Firstname} {employee.Lastname}.</p>
                    {projectDetails}
                    <p>Merci de consulter cette demande dans le système de gestion RH.</p>
                    <p>Cordialement,<br><strong>Service RH</strong></p>
                </div>";
        }

        await _emailService.SendEmailAsync(manager.Email, emailSubject, emailBody);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to send project fee request email");
    }
}

 [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProjectfees(int id, [FromBody] ProjectfeesDto requestDto)
        {
            var projectfees = await _projectfeesRepository.GetByIdAsync(id);
            if (projectfees == null)
                return NotFound("Project fees not found.");

            projectfees.Feesdate = requestDto.Feesdate;
            projectfees.Feesamount = requestDto.Feesamount;
            projectfees.Comment = requestDto.Comment;
            projectfees.Reference = requestDto.Reference;
            projectfees.IdFeestype = requestDto.IdFeestype;
            projectfees.IdCurrency = requestDto.IdCurrency;
            projectfees.IdSupplier = requestDto.IdSupplier;
            projectfees.Updateddate = DateTime.UtcNow;

            await _projectfeesRepository.UpdateAsync(projectfees);
            return Ok(projectfees);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProjectfees(int id)
        {
            var projectfees = await _projectfeesRepository.GetByIdAsync(id);
            if (projectfees == null)
                return NotFound("Project fees not found.");

            await _projectfeesRepository.DeleteAsync(projectfees);
            return NoContent();
        }

    


 [HttpGet("assigned-to-me")]
        [Authorize]
        public async Task<IActionResult> GetAssignedProjectFees()
        {
            try
            {
                var email = GetUserEmail();
                var user = await _dynamicDb.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new { u.Id })
                    .FirstOrDefaultAsync();
                    
                if (user == null)
                    return NotFound("Utilisateur non trouvé");
                    
                var userId = user.Id;
                var requests = await _projectfeesRepository.GetAssignedToUserAsync(userId);
                
                return Ok(requests);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "Échec de la récupération des demandes",
                    Error = ex.Message
                });
            }
        }

        [HttpGet("my-project-fees")]
        [Authorize]
  
        public async Task<IActionResult> GetMyProjectFees()
        {
            try
                    {
                        var email = GetUserEmail();
                        var user = await _dynamicDb.Appusers
                            .Where(u => u.Email == email)
                            .Select(u => new { u.Id })
                            .FirstOrDefaultAsync();
                            
                        if (user == null)
                            return NotFound("Utilisateur non trouvé");
                            
                        var userId = user.Id.ToString();
                        var requests = await _projectfeesRepository.GetByCreatedByAsync(userId);
                        
                        return Ok(requests);
                    }
                    catch (Exception ex)
                    {
                        return StatusCode(500, new
                        {
                            Success = false,
                            Message = "Échec de la récupération des demandes",
                            Error = ex.Message
                        });
                    }

        }
}
}