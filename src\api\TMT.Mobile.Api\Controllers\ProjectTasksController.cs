﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using TMT.Mobile.Api.Interfaces;
using TMT.Mobile.Core.Consts;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;

namespace TMT.Mobile.Api.Controllers
{
    public class ProjectTasksController : Controller
    {
        private readonly DynamicDbContext _DynamicDb;
        private readonly ITokenService _tokenService;


        public ProjectTasksController(DynamicDbContext dynamicDb, ITokenService tokenService)
        {

            _DynamicDb = dynamicDb;
            _tokenService = tokenService;

        }


        [HttpGet("getProjectTasks")]
        [Authorize]
        public IActionResult GetProjectTasks(int projectlotid)
        {
            if (!User.Identity.IsAuthenticated)
            {
                return Unauthorized("You are not authenticated");
            }

            var userId = User.FindFirst(TMTClaimsTypes.UserId)?.Value;
            var email = User.FindFirst(TMTClaimsTypes.UserLogin)?.Value;

            if (userId == null || email == null)
            {
                return BadRequest("Missing user claims.");
            }

            var projects = _DynamicDb.Projecttasks
                .Where(u => u.Idprojectlot == projectlotid && u.Isdeleted == false)
                .Select(u => new ProjectTasksDto
                {
                    Id = u.Id,
                    Name = u.Taskname,
                    projectLotId = u.Idprojectlot ?? 0 
                }).ToList();

            return Ok(projects);
        }

    }
}
