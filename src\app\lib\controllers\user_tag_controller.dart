import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/models/ManagerDto.dart';
import 'package:tmt_mobile/models/tag.dart';
import 'package:tmt_mobile/models/usertag.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/utils/userServices.dart';
import 'package:tmt_mobile/widgets/big_text.dart';

class UserTagController extends GetxController {
  var userTags = <UserTag>[].obs;
  var tags = <Tag>[].obs;
  var managers = <ManagerDto>[].obs;
  var isLoading = false.obs;
  final UserServices userServices = UserServices();

  @override
  void onInit() {
    super.onInit();
    fetchUserTags();
    fetchTags();
    fetchManagers();
  }

  Future<void> fetchUserTags() async {
    isLoading(true);
    try {
      var response = await UserServices.getUserTags();
      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List;
        userTags.value = data.map((json) => UserTag.fromJson(json)).toList();
      } else {
        Get.snackbar(
            'Error', 'Failed to fetch user tags: ${response.statusCode}');
      }
    } catch (e) {
      Get.snackbar('Error', 'An error occurred while fetching user tags');
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchTags() async {
    isLoading(true);
    try {
      var response = await UserServices.getTags();
      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List;
        tags.value = data.map((json) => Tag.fromJson(json)).toList();
      } else {
        Get.snackbar('Error', 'Failed to fetch tags: ${response.statusCode}');
      }
    } catch (e) {
      Get.snackbar('Error', 'An error occurred while fetching tags');
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchManagers() async {
    isLoading(true);
    try {
      var response = await userServices.getManagers();
      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        var managerList = <ManagerDto>[];
        if (data['manager'] != null) {
          managerList.add(ManagerDto.fromJson(data['manager']));
        }
        if (data['secondManager'] != null) {
          managerList.add(ManagerDto.fromJson(data['secondManager']));
        }
        managers.value = managerList;
      } else {
        Get.snackbar(
            'Error', 'Failed to fetch managers: ${response.statusCode}');
      }
    } catch (e) {
      Get.snackbar('Error', 'An error occurred while fetching managers');
    } finally {
      isLoading(false);
    }
  }

  Future<void> addUserTag(UserTag userTag) async {
    isLoading(true);
    try {
      var response = await UserServices.addUserTag(userTag);
      if (response.statusCode == 201) {
        userTags.add(UserTag.fromJson(json.decode(response.body)));
        GlobalController global = Get.find<GlobalController>();
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? 'Succès' : 'Success',
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            global.lang.value == "fr"
                ? 'Tag utilisateur ajouté avec succès'
                : 'User tag added successfully',
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
        print('User tag added successfully: ${response.body}');
      } else {
        var errorMessage = 'Failed to add user tag: ${response.statusCode}';
        try {
          var errorBody = json.decode(response.body);
          if (errorBody['message'] != null) {
            errorMessage = errorBody['message'];
          }
        } catch (e) {
          print('Error parsing error response: $e');
        }
        GlobalController global = Get.find<GlobalController>();
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: global.lang.value == "fr" ? 'Erreur' : 'Error',
            size: 18,
            color: Colors.red,
          ),
          messageText: Text(
            errorMessage,
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          overlayBlur: 1.5,
        );
        print(
            'Failed to add user tag: ${response.statusCode} - ${response.body}');
      }
    } catch (e, stackTrace) {
      GlobalController global = Get.find<GlobalController>();
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? 'Erreur' : 'Error',
          size: 18,
          color: MyColors.blackbackground2,
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? 'Une erreur s\'est produite lors de l\'ajout du tag utilisateur'
              : 'An error occurred while adding user tag',
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
      print('Error occurred while adding user tag: $e');
      print('Stack trace: $stackTrace');
    } finally {
      isLoading(false);
    }
  }
}
