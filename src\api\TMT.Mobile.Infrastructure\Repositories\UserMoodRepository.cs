using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class UserMoodRepository : CoreRepository<UserMood, DynamicDbContext>
    {
        public UserMoodRepository(DynamicDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<UserMood>> GetAllAsync()
        {
            return await context.UserMoods.ToListAsync();        
        }


        public async Task<UserMood?> GetByIdAsync(int id)
        {
            return await context.UserMoods.FindAsync(id);
        }

        public async Task<UserMood> AddAsync(UserMood userMood)
        {
            context.UserMoods.Add(userMood);
            await context.SaveChangesAsync();
            return userMood;
        }
   
        public async Task<List<UserMood>> GetByUserIdAsync(int userId)
        {
            return await context.UserMoods
                .Where(lr => lr.IdUser == userId)
                .ToListAsync();
        }
    }
}
