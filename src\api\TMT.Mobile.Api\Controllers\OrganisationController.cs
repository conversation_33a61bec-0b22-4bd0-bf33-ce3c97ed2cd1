﻿
using Microsoft.AspNetCore.Mvc;

using TMT.Mobile.Core.Entities;
using Microsoft.EntityFrameworkCore;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Infrastructure.Repositories;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Collections;
using System.Data.Entity;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using System.Data;
using Microsoft.AspNetCore.Mvc.Filters;
using TMT.Mobile.Core.Consts;
using TMT.Mobile.Core.Entities.DTOs.Authentification;
using Microsoft.AspNetCore.Authentication.Cookies;
using TMT.Mobile.Api.Interfaces;
using TMT.Mobile.Api.Services;
using Microsoft.IdentityModel.Tokens;
using EntityState = Microsoft.EntityFrameworkCore.EntityState;

namespace TMT.Mobile.Api.Controllers
{

  [Authorize]
  public class OrganisationController : ControllerBase
  {
    private readonly TmtMobileContext _context;
    private readonly UserRepository _userRepository;
    private readonly ITokenService _tokenService;
    public OrganisationController(TmtMobileContext context, UserRepository userRepository, ITokenService tokenService)
    {
      _context = context;
      _userRepository = userRepository;
      _tokenService = tokenService;
    }



    [HttpGet("getalluserorg")]
    public IActionResult GetOrganizationsByEmail(string email)
    {
      User user = _context.Users.FirstOrDefault(u => u.Email == email);

      if (user == null)
      {
        return NotFound("User not found");
      }

      List<Organization> userOrganizations = _context.UserOrganizations
          .Where(uo => uo.IdUser == user.Id && !uo.IsDeleted)
          .Select(uo => uo.Organization)
          .ToList();

      if (userOrganizations.IsNullOrEmpty())
      {
        return NotFound("org not found");
      }

      List<OrganisationDto> result = userOrganizations.Select(u => new OrganisationDto
      {
        Guid = u.GUID,
        Name = u.Name
      }).ToList();


      return Ok(result);
    }
    [HttpGet("getOneOrg")]
    [Authorize]
    public IActionResult GetOneOrg(Guid guid)
    {
      var IsAuthenticated = User.Identity.IsAuthenticated;
      if (!IsAuthenticated)
      {
        return Unauthorized();
      }
      var email = User.FindFirst(ClaimTypes.Email)?.Value;
      var userName = User.FindFirst(ClaimTypes.Name)?.Value;
      Organization org = _context.Organizations.FirstOrDefault(or => or.GUID == guid);
      if (org == null)
      {
        return BadRequest();
      }


      var dbContextOptions = new DbContextOptionsBuilder<DynamicDbContext>()
 .UseNpgsql(org.ConnectionString)
 .Options;

      // Create a new instance of the DynamicDbContext with the updated options
      using (var dbContext = new DynamicDbContext(dbContextOptions))
      {
        // Use the DynamicDbContext to interact with the dynamic database
        // Perform database operations as needed
      }
      User user = _context.Users.FirstOrDefault(u => u.Email == email);


      var claims = new List<Claim>
                    {
                    new Claim(ClaimTypes.Name, user.ToString()),
                    new Claim(ClaimTypes.Email, user.Email),
                    new Claim(TMTClaimsTypes.UserLogin, user.Email),
                    new Claim(TMTClaimsTypes.UserGuid, user.GUID.ToString()),
                    new Claim(TMTClaimsTypes.UserId, user.Id.ToString())
                    };
      _context.Users.Attach(user!);
      user.FavoriteOrganization = org;

      _context.Entry<User>(user).State = EntityState.Modified;
      _context.SaveChanges();
      claims.Add(new Claim(TMTClaimsTypes.OrgName, user.FavoriteOrganization.Name));
      claims.Add(new Claim(TMTClaimsTypes.OrgGuid, org.GUID.ToString()));
      var Owner = user.FavoriteOrganization.IdOwner == user.Id;
      claims.Add(new Claim(TMTClaimsTypes.Owner, Owner.ToString()));
      var AdminRole = _userRepository.IsAdmin(user.Id, user.FavoriteOrganization.GUID).Result;
      claims.Add(new Claim(TMTClaimsTypes.IsAdmin, AdminRole.ToString()));
      var SuperUserRole = _userRepository.IsSuperUser(user.Id, user.FavoriteOrganization.GUID).Result;
      claims.Add(new Claim(TMTClaimsTypes.IsSuperUser, SuperUserRole.ToString()));
      var UserRole = _userRepository.GetUserRole(user.Id, user.FavoriteOrganization.GUID).Result;
      claims.Add(new Claim(ClaimTypes.Role, UserRole.ToString()));
      claims.Add(new Claim(TMTClaimsTypes.OrgUserId, user.Id.ToString()));
      var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
      var principal = new ClaimsPrincipal(identity);



      return Ok(new AppUserDto
      {
        Email = user.Email,
        GUID = user.GUID,


        Token = _tokenService.CreateToken(user!, claims)
      });

    }
  }



}












