using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
    public class SupplierPayment : IEntity
  {
    public SupplierPayment()
    {
      LetteringDetails = new HashSet<SupplierLetteringDetail>();
    }
    public int Id { get; set; }
    public DateTime DatePayment { get; set; }
    public double Amount { get; set; }
    public double? AmountCertificate { get; set; }
    public double AmountTotal { get { return Amount + AmountCertificate.GetValueOrDefault(0); } }
    public int? IdCurrency { get; set; }
    public Double ExchangeRate { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; } = DateTime.Now;
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public string? Notes { get; set; }
    public string? Reference { get; set; }
    public int? IdType { get; set; }
    public int? IdSupplier { get; set; }
    public Currency Currency { get; set; }
    public Appvariables Type { get; set; }
    public Counterparties Supplier { get; set; }
    public int? IdEntity { get; set; }
    public virtual OrgEntity? Entity { get; set; }
    public virtual ICollection<SupplierLetteringDetail> LetteringDetails { get; set; }
  }
}
