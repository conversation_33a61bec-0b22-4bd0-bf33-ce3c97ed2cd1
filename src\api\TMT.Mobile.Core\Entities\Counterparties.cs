using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace TMT.Mobile.Core.Entities
{
  public partial class Counterparties : IEntity
  {
    public Counterparties()
    {
    //   Counterpartycontacts = new HashSet<Counterpartycontacts>();
    //   Projects = new HashSet<Projects>();
    //   Bills = new HashSet<Bill>();
    //   Payments = new HashSet<Payment>();
    //   Fees = new HashSet<Projectfees>();
    //   Letterings = new HashSet<Lettering>();
    //   SupplierLetterings = new HashSet<SupplierLettering>();
    //   SupplierPayments = new HashSet<SupplierPayment>();
    //   OrgEntities = new HashSet<OrgEntity>();
    //   FeesClients = new HashSet<Projectfees>();
    //   ExpenseRequests = new HashSet<ExpenseRequest>();  
    }

    public int Id { get; set; }

    public string Reference
    {
      get
      {
        return "CL." + this.Id.ToString("D3");
      }
    }
    public string? Counterpartyname { get; set; }
    public string? Counterpartyreference { get; set; }
    public string? Note { get; set; }
    public string? Billingaddress { get; set; }
    public string? Phonenumber { get; set; }
    public string? Vatcode { get; set; }
    public string? ClientMainContactName { get; set; }
    public string? ClientMainContactInfo { get; set; }
    public int? Paymentterms { get; set; }
    public int? Idcounterpartytype { get; set; }
    public bool Isdeleted { get; set; }
    public bool Isinternal { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }

    public bool ExcludeTaxes { get; set; }
    public bool IsEntity { get; set; } = false;
    public virtual Appvariables Counterpartytype { get; set; }
    // public virtual ICollection<Counterpartycontacts> Counterpartycontacts { get; set; }
    // public virtual ICollection<Bill> Bills { get; set; }
    public virtual ICollection<Projects> Projects { get; set; }
    // public virtual ICollection<Payment> Payments { get; set; }
    // public virtual ICollection<Projectfees> Fees { get; set; }
    // public virtual ICollection<Projectfees> FeesClients { get; set; }
    // // public virtual ICollection<Lettering> Letterings { get; set; }
    // public virtual ICollection<SupplierLettering> SupplierLetterings { get; set; }
    // public virtual ICollection<SupplierPayment> SupplierPayments { get; set; }

    // public virtual ICollection<OrgEntity> OrgEntities { get; set; }

     public virtual ICollection<ExpenseRequest> ExpenseRequests { get; set; }
    public override string ToString()
    {
      return this.Reference;
    }
  }
}
