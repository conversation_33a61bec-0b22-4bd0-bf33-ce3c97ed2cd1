import 'package:flutter/material.dart';
import 'package:flutter_advanced_drawer/flutter_advanced_drawer.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/menucontroller.dart';
import 'package:tmt_mobile/controllers/myorganisationcontroller.dart';
import 'package:tmt_mobile/controllers/sidemenucontroller.dart';
import 'package:tmt_mobile/screens/landingScreen.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/utils/userPrefrences.dart';
import 'package:tmt_mobile/widgets/big_text.dart';
import 'package:tmt_mobile/utils/permission_utils.dart';
import 'package:tmt_mobile/widgets/buttonwithicon.dart';

class SideMenu extends GetView<SideMenuController> {
  final AdvancedDrawerController advancedDrawerController;
  const SideMenu({super.key, required this.advancedDrawerController});
  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    bool customTileExpanded = false;
    MyOrganisationController orgCon = Get.find<MyOrganisationController>();
    GlobalController global = Get.find<GlobalController>();
    Menucontroller mController = Get.find<Menucontroller>();

    void showUnauthorizedSnackbar() {
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Non Autorisé" : "UNAUTHORIZED",
          size: 18,
          color: const Color.fromARGB(255, 161, 43, 34),
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Vous devez d'abord choisir une organisation"
              : "You need to choose an organisation first",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    }

    void showNotManagerSnackbar() {
      Get.snackbar(
        '',
        '',
        titleText: BigText(
          text: global.lang.value == "fr" ? "Accès Refusé" : "ACCESS DENIED",
          size: 18,
          color: const Color.fromARGB(255, 161, 43, 34),
        ),
        messageText: Text(
          global.lang.value == "fr"
              ? "Vous n'êtes pas un manager"
              : "You are not a manager",
          style: TextStyle(
            fontSize: 17,
          ),
        ),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
        overlayBlur: 1.5,
      );
    }

    return SafeArea(
      child: Container(
        child: ListTileTheme(
          textColor: Colors.white,
          iconColor: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Container(
                width: 100.0,
                height: 100.0,
                margin: const EdgeInsets.only(
                  top: 20.0,
                  bottom: 30,
                ),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.black26,
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  "assets/logotmt.svg",
                  fit: BoxFit.fill,
                ),
              ),
              BigText(
                text: "TMT/Mobile",
              ),
              SizedBox(
                height: screenHeight * 0.03,
              ),
              Divider(
                color: MyColors.inputcolorfill.withOpacity(0.4),
                indent: 40,
                endIndent: 40,
                thickness: 1,
                height: 0.6,
              ),
              SizedBox(
                height: screenHeight * 0.01,
              ),
              Obx(() =>
                  orgCon.orglist.isNotEmpty && orgCon.selectedOrgIndex != -1
                      ? ListTile(
                          trailing: Icon(
                            Icons.circle,
                            color: Colors.green,
                            size: 15,
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 20),
                          title: Text(
                            orgCon.orglist[orgCon.selectedOrgIndex.value].name
                                .toString(),
                            style: TextStyle(
                                fontFamily: "aileron",
                                fontSize: 15,
                                fontWeight: FontWeight.bold),
                          ),
                        )
                      : SizedBox.shrink()),
              Divider(
                color: MyColors.inputcolorfill.withOpacity(0.4),
                indent: 20,
                endIndent: 20,
                thickness: 1,
                height: 0.6,
              ),
              Expanded(
                flex: 4,
                child: ListView(
                  children: [
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 0;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.view_timeline_rounded),
                          title: Text(global.lang == "fr"
                              ? "Saisie de temps"
                              : 'Timesheet'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 1;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.workspaces_rounded),
                          title: Text(global.lang == "fr"
                              ? "Mes Organisations"
                              : 'My Organisations'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 2;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.beach_access),
                          title: Text(global.lang == "fr"
                              ? "Demande de Congés"
                              : 'Vacation Request'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 3;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.home_work),
                          title: Text(global.lang == "fr"
                              ? "Demande de Travail à Distance"
                              : 'Remote Work Request'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 4;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.mood),
                          title: Text(global.lang == "fr"
                              ? "Soumettre l'Humeur"
                              : 'Submit Mood'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 5;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.list),
                          title: Text(global.lang == "fr"
                              ? "Historique de Congés"
                              : 'Vacation History'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 6;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.work),
                          title: Text(global.lang == "fr"
                              ? "Historique de Travail à Distance"
                              : 'Remote Work History'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 11;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.face),
                          title: Text(global.lang == "fr"
                              ? "Historique de l'humeur"
                              : 'Mood History'),
                        )),
                    Obx(() => ListTile(
                          onTap: () async {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              bool isManager = await isCurrentUserManager();
                              print('Is manager: $isManager'); // Debug log
                              if (isManager) {
                                mController.screenindex.value = 9;
                                advancedDrawerController.hideDrawer();
                              } else {
                                showNotManagerSnackbar();
                              }
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.admin_panel_settings),
                          title: Text(global.lang == "fr"
                              ? "Espace Manager"
                              : 'Manager Space'),
                        )),
                    Obx(() => ListTile(
                          onTap: () {
                            if (UserPrefrences.getCureentOrg() != "" &&
                                UserPrefrences.getCureentOrg() != null) {
                              mController.screenindex.value = 10;
                              advancedDrawerController.hideDrawer();
                            } else {
                              showUnauthorizedSnackbar();
                            }
                          },
                          leading: Icon(Icons.people),
                          title: Text(global.lang == "fr"
                              ? "Partagez Vos Avis"
                              : 'Share Your Opinion'),
                        )),
                    Obx(() => ListTile(
                          onTap: () async {
                            final storage = FlutterSecureStorage();
                            await storage.delete(key: "jwt");
                            await UserPrefrences.deleteAll();

                            await Get.deleteAll();
                            Get.lazyPut(() => GlobalController());
                            Get.offAll(LandingScreen());
                          },
                          leading: Icon(Icons.logout),
                          title: Text(
                              global.lang == "fr" ? 'Déconnecter' : 'Log out'),
                        )),
                  ],
                ),
              ),
              SizedBox(height: 8),
              DefaultTextStyle(
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white54,
                ),
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 16.0),
                  child: GestureDetector(
                    onTap: () {
                      final global = Get.find<GlobalController>();
                      final isFrench = global.lang.value == "fr";

                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: Text(isFrench
                              ? "Politique de confidentialité et conditions d'utilisation"
                              : "Privacy Policy & Terms of Service"),
                          content: SingleChildScrollView(
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                    fontSize: 12, color: Colors.black),
                                children: [
                                  TextSpan(
                                    text: isFrench
                                        ? "TMT Software respecte votre vie privée. Cette politique de confidentialité explique comment nous collectons, utilisons et protégeons vos informations.\n\n"
                                        : "TMT Software respects your privacy. This Privacy Policy explains how we collect, use, and protect your information.\n\n",
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "1. Informations que nous collectons\n"
                                        : "1. Information We Collect\n",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "Nous pouvons collecter :\n- Données de compte : nom, e-mail, détails de l'organisation.\n- Données de demande : dates de congé/télétravail, raisons, commentaires.\n- Données d'utilisation : journaux d'activité de l'application.\n- Évaluations/commentaires : notes et commentaires sur les collaborateurs.\n\n"
                                        : "We may collect:\n- Account Data: Name, email, organization details.\n- Request Data: Leave/remote work dates, reasons, comments.\n- Usage Data: Logs of app activity.\n- Ratings/Feedback: Star ratings and comments on collaborators.\n\n",
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "2. Comment nous utilisons vos données\n"
                                        : "2. How We Use Your Data\n",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "Nous utilisons les données pour :\n- Faciliter les processus RH.\n- Envoyer des notifications système et e-mails.\n- Améliorer les fonctionnalités de l'application.\n\n"
                                        : "We use the data to:\n- Facilitate HR processes.\n- Send system and email notifications.\n- Improve app features.\n\n",
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "3. Partage de vos données\n"
                                        : "3. Sharing Your Data\n",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "Vos données sont partagées uniquement avec votre organisation (managers, RH/admin). Nous ne vendons pas vos données personnelles à des tiers.\n\n"
                                        : "Your data is shared only with your organization (managers, HR/admin). We do not sell your personal data to third parties.\n\n",
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "4. Sécurité des données\n"
                                        : "4. Data Security\n",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "Nous appliquons des mesures de sécurité standard. L'accès est basé sur les rôles et chiffré si nécessaire.\n\n"
                                        : "We apply standard security measures. Access is role-based and encrypted where necessary.\n\n",
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "5. Vos droits\n"
                                        : "5. Your Rights\n",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "Vous pouvez demander à consulter, modifier ou supprimer vos données via votre organisation. Pour toute préoccupation, contactez votre organisation ou le support à <EMAIL>.\n\n"
                                        : "You can request to view, modify, or delete your data through your organization. For concerns, contact your organization or <NAME_EMAIL>.\n\n",
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "6. Modifications de cette politique\n"
                                        : "6. Changes to This Policy\n",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text: isFrench
                                        ? "Nous pouvons mettre à jour cette politique. Les changements seront notifiés via l'application."
                                        : "We may update this policy. Changes will be notified through the app.",
                                  ),
                                ],
                              ),
                            ),
                          ),
                          actions: [
                            ButtonWithIcon(
                              text: global.lang.value == "fr"
                                  ? 'Fermer'
                                  : 'Close',
                              mainColor: Colors.grey,
                              textcolor: Colors.white,
                              onPressed: () => Get.back(),
                              height: 40,
                              width: 100,
                              fontSize: 15,
                            ),
                          ],
                        ),
                      );
                    },
                    child: Text(
                      'Terms of Service & Privacy Policy',
                      style: TextStyle(
                        decoration: TextDecoration.underline,
                        color: Colors.white70,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
