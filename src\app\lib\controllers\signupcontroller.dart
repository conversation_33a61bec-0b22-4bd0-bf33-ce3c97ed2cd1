import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/models/userdata.dart';
import 'package:tmt_mobile/utils/userServices.dart';

class SignUpController extends GetxController {
  final GlobalController global;
  SignUpController(this.global);
  var SignUpKey = GlobalKey<FormState>();
  var context = UserServices();
  var showError = false.obs;
  var errormsg = "Bad Credentials".obs;
  var email = TextEditingController().obs;
  var password = TextEditingController().obs;
  var nom = TextEditingController().obs;
  var prenom = TextEditingController().obs;
  var passwtoggl = true.obs;
  var passwtogg2 = true.obs;
  var loading = false.obs;
  var confirmpaswword = TextEditingController().obs;
  String? validateThese(String c1) {
    if (c1.isEmpty) {
      return global.lang.value == "fr"
          ? "Ce champ ne peut pas être vide"
          : "This field can't be empty";
    }
    return null;
  }

  Future<bool> onSubmit(userRegistration data) async {
    loading.value = true;

    var response = await context.registration(data);
    if (response.statusCode == 200) {
      var temp = json.decode(response.body);
      print(temp);
      global.appuser.value.email = temp["email"];
      global.appuser.value.token = temp["token"];
      global.appuser.value.isvalidated = false;
      global.appuser.value.guid = temp["guid"];

      final storage = FlutterSecureStorage();
      await storage.write(
          key: 'valid', value: global.appuser.value.isvalidated.toString());
      await storage.write(key: 'jwt', value: global.appuser.value.token);
// Write value

      return true;
    } else {
      loading.value = false;

      showError.value = true;
      errormsg.value = response.body.toString();
      return false;
    }
  }

  @override
  void onInit() {
    passwtoggl.value = true;
    passwtogg2.value = true;

    super.onInit();
  }

  String? validateEmail(String c1) {
    if (c1.isEmpty) {
      return global.lang.value == "fr"
          ? "Ce champ ne peut pas être vide"
          : "This field can't be empty";
    } else if (c1.contains("@") == false) {
      return global.lang.value == "fr"
          ? "Vérifiez le champ email"
          : "Verify email field";
    }
    return null;
  }

  String? validatePasswordlen(String c1) {
    if (c1.isEmpty) {
      return global.lang.value == "fr"
          ? "Le mot de passe est obligatoire"
          : "Password is required";
    } else if (c1.length < 6) {
      return global.lang.value == "fr"
          ? "Minimum 6 caractères"
          : "Minimum 6 characters";
    }
    return null;
  }

  String? validatePassword(String c1, c2) {
    if (c2.isEmpty || c2 == null) {
      return global.lang.value == "fr"
          ? "Confirmation mot de passe est obligatoire"
          : "Password confirmation is required";
    } else if (c1 != c2) {
      return global.lang.value == "fr"
          ? "Les mots de passe ne correspondent pas"
          : "Passwords do not match";
    }
    return null;
  }

  String? validatePhone(String c1) {
    if (!c1.isPhoneNumber) {
      return global.lang.value == "fr"
          ? "Format incorrect"
          : "Incorrect format";
    }
    return null;
  }
}
