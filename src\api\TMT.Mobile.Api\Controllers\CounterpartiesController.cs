using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Repositories;
using System.Security.Claims;
using TMT.Mobile.Core.Consts;
using Microsoft.EntityFrameworkCore;
using TMT.Mobile.Core.Entities.DTOs;

namespace TMT.Mobile.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CounterpartiesController : ControllerBase
    {        
        private readonly CounterpartiesRepository _counterpartiesRepository;

        public CounterpartiesController(        
            CounterpartiesRepository counterpartiesRepository)
        {
            _counterpartiesRepository = counterpartiesRepository;
        }


[HttpGet]
        public async Task<IActionResult> GetAllCounterparties()
        {
            var requests = await _counterpartiesRepository.GetAllAsync();
            return Ok(requests);
        }
    }
}
