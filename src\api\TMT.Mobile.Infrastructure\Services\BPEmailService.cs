﻿using MimeKit;
// using System.Net.Mail;
using MailKit.Net.Smtp;
using TMT.Mobile.Infrastructure.Services.Interfaces;
using TMT.Mobile.Infrastructure.Services.Interfaces;

namespace TMT.Mobile.Infrastructure.Services
{
    public class BPEmailService : IBPEmailService
    {
        public string _smtpServer { get; set; } = "ssl0.ovh.net";
        public int _smtpPort { get; set; } = 465;
        public string _sender { get; set; } = "<EMAIL>";
        public string _senderEmail { get; set; } = "<EMAIL>";
        public string _senderPwd { get; set; } = "Cesar@2103";

        public async void SendEmailDefault(string receiver, string receiverEmail, string subject, string Body)
        {
            SendEmail(string.Empty, string.Empty, string.Empty, receiver, receiverEmail, subject, Body, string.Empty, 0, true);
        }
        public async void SendEmail(string sender,
                              string senderEmail,
                              string senderPwd,
                              string receiver,
                              string receiverEmail,
                              string subject,
                              string Body,
                              string smtpServer,
                              int smtpPort,
                              bool useSSL)
        {
            if (string.IsNullOrEmpty(smtpServer)) smtpServer = _smtpServer;
            if (smtpPort == 0) smtpPort = _smtpPort;
            if (string.IsNullOrEmpty(sender)) sender = _sender;
            if (string.IsNullOrEmpty(senderEmail)) senderEmail = _senderEmail;
            if (string.IsNullOrEmpty(senderPwd)) senderPwd = _senderPwd;

            MimeMessage message = new MimeMessage();
            message.From.Add(new MailboxAddress(sender, senderEmail));
            message.To.Add(new MailboxAddress(receiver, receiverEmail));
            message.Subject = subject;

            BodyBuilder bodyBuilder = new BodyBuilder
            {
                HtmlBody = Body
            };
            message.Body = bodyBuilder.ToMessageBody();

            using (SmtpClient client = new SmtpClient())
            {
                await client.ConnectAsync(smtpServer, smtpPort, useSSL);
                client.AuthenticationMechanisms.Remove("XOAUTH2");
                await client.AuthenticateAsync(senderEmail, senderPwd);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);
            }

        }

        public async void SendEmailToMultipleReceivers(string sender,
                                     string senderEmail,
                                     string senderPwd,
                                     IEnumerable<string> receivers,
                                     string subject,
                                     string Body,
                                     string smtpServer,
                                     int smtpPort,
                                     bool useSSL)
        {
            if (string.IsNullOrEmpty(smtpServer)) smtpServer = _smtpServer;
            if (smtpPort == 0) smtpPort = _smtpPort;
            if (string.IsNullOrEmpty(sender)) sender = _sender;
            if (string.IsNullOrEmpty(senderEmail)) senderEmail = _senderEmail;
            if (string.IsNullOrEmpty(senderPwd)) senderPwd = _senderPwd;

            MimeMessage message = new MimeMessage();
            message.From.Add(new MailboxAddress(sender, senderEmail));
            InternetAddressList list = new InternetAddressList();
            foreach (string item in receivers)
            {
                MailboxAddress receiver = new MailboxAddress(item);
                list.Add(receiver);
            }
            message.To.AddRange(list);
            message.Subject = subject;

            BodyBuilder bodyBuilder = new BodyBuilder
            {
                HtmlBody = Body
            };
            message.Body = bodyBuilder.ToMessageBody();

            using (SmtpClient client = new SmtpClient())
            {
                await client.ConnectAsync(smtpServer, smtpPort, useSSL);
                client.AuthenticationMechanisms.Remove("XOAUTH2");
                await client.AuthenticateAsync(senderEmail, senderPwd);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);
            }

        }
    }
}
