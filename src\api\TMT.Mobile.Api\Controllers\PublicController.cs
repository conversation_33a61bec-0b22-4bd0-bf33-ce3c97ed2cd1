﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using TMT.Mobile.Api.Interfaces;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Infrastructure;
using Microsoft.AspNetCore.Http.HttpResults;

namespace TMT.Mobile.Api.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class PublicController : ControllerBase
  {
    private readonly ILogger<TimesheetController> _logger;
   
    private readonly CommonRepository _CommonRepository;
    public PublicController( ILogger<TimesheetController> logger, CommonRepository commonRepository)
    {

      _CommonRepository = commonRepository;
      _logger = logger;

    }
    [HttpGet("HealthCheck")]
    public async Task<string> HealthCheck()
    {
      try
      {
        var result = "Healthcheck Ok => " +  _CommonRepository.HealthCheck();
        _logger.LogInformation(result);
        return result;
      }
      catch (Exception ex)
      {
        var error = "Healthcheck KO!!! => " + ex.Message;
        _logger.LogError(error);
        return error;
      }

    }
  }
}
