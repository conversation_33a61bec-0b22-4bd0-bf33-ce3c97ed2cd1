import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/remote_work_request_controller.dart';
import 'package:tmt_mobile/models/ManagerDto.dart';
import 'package:tmt_mobile/models/remote_work_request_model.dart';
import 'package:tmt_mobile/utils/myColors.dart';

class RemoteWorkRequestValidationPage extends StatelessWidget {
  final RemoteWorkRequestController controller =
      Get.put(RemoteWorkRequestController());

  RemoteWorkRequestValidationPage({super.key});

  @override
  Widget build(BuildContext context) {
    GlobalController global = Get.find<GlobalController>();

    // ✅ Fetch assigned requests when the widget is first built
    controller.fetchAssignedRemoteWorkRequests();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          global.lang.value == "fr"
              ? 'Validation des demandes'
              : 'Request Validation',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        backgroundColor: MyColors.MainRedBig,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: <Widget>[
          Container(
            margin: EdgeInsets.only(right: 8),
            child: PopupMenuButton<String>(
              icon: Icon(Icons.language, color: Colors.white, size: 24),
              onSelected: (String choice) {},
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              itemBuilder: (BuildContext context) {
                return [
                  PopupMenuItem<String>(
                    value: "fr",
                    child: Text(
                      global.lang.value == "fr" ? "Français" : "French",
                      style: global.lang.value == "fr"
                          ? TextStyle(
                              fontWeight: FontWeight.bold, color: Colors.blue)
                          : TextStyle(fontWeight: FontWeight.normal),
                    ),
                    onTap: () => global.lang.value = "fr",
                  ),
                  PopupMenuItem<String>(
                    value: "en",
                    child: Text(
                      global.lang.value == "fr" ? "Anglais" : "English",
                      style: global.lang.value == "en"
                          ? TextStyle(
                              fontWeight: FontWeight.bold, color: Colors.blue)
                          : TextStyle(fontWeight: FontWeight.normal),
                    ),
                    onTap: () => global.lang.value = "en",
                  ),
                ];
              },
            ),
          ),
        ],
      ),
      backgroundColor: Colors.grey[50],
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(MyColors.MainRedSecond),
                ),
                SizedBox(height: 16),
                Text(
                  global.lang.value == "fr"
                      ? 'Chargement des demandes...'
                      : 'Loading requests...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        // Filter only submitted requests
        var submittedRequests = controller.remoteWorkRequests
            .where((request) =>
                controller.getStatusName(request.statusId) == 'Submitted')
            .toList();

        if (submittedRequests.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 80,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 16),
                Text(
                  global.lang.value == "fr"
                      ? 'Aucune demande en attente'
                      : 'No pending requests',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  global.lang.value == "fr"
                      ? 'Toutes les demandes ont été traitées'
                      : 'All requests have been processed',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          color: MyColors.MainRedSecond,
          onRefresh: () async {
            await controller.fetchRemoteWorkRequests();
          },
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: submittedRequests.length,
            itemBuilder: (context, index) {
              var request = submittedRequests[index];
              return Container(
                margin: EdgeInsets.only(bottom: 16),
                child: _buildModernRequestCard(context, request, global),
              );
            },
          ),
        );
      }),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildModernRequestCard(BuildContext context,
      RemoteWorkRequest request, GlobalController global) {
    return GestureDetector(
      onTap: () => _showDetailsDialog(context, request),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _showDetailsDialog(context, request),
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with title and status
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: MyColors.MainRedSecond.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.assignment,
                          color: MyColors.MainRedSecond,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              global.lang.value == "fr"
                                  ? 'Demande de télétravail'
                                  : 'Remote Work Request',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 4),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getStatusColor(controller
                                        .getStatusName(request.statusId))
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _getTranslatedStatus(
                                    controller.getStatusName(request.statusId)),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: _getStatusColor(controller
                                      .getStatusName(request.statusId)),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  // Request details
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              SizedBox(width: 8),
                              Text(
                                '${request.quantity.toInt()} ${global.lang.value == "fr" ? "jours" : "days"}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _formatDate(request.dateStart),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 44,
                          child: ElevatedButton.icon(
                            onPressed: () =>
                                _showCommentDialog(context, request.id, true),
                            icon: Icon(Icons.check, size: 18),
                            label: Text(
                              global.lang.value == "fr"
                                  ? 'Approuver'
                                  : 'Approve',
                              style: TextStyle(
                                  fontSize: 10, fontWeight: FontWeight.w600),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              elevation: 2,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          height: 44,
                          child: ElevatedButton.icon(
                            onPressed: () =>
                                _showCommentDialog(context, request.id, false),
                            icon: Icon(Icons.close, size: 18),
                            label: Text(
                              global.lang.value == "fr" ? 'Rejeter' : 'Reject',
                              style: TextStyle(
                                  fontSize: 10, fontWeight: FontWeight.w600),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              elevation: 2,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      Container(
                        height: 44,
                        width: 44,
                        child: ElevatedButton(
                          onPressed: () =>
                              _showReassignManagerDialog(context, request.id),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            elevation: 2,
                            padding: EdgeInsets.zero,
                          ),
                          child: Icon(Icons.swap_horiz, size: 18),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: Colors.blue.shade600,
              ),
              SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailCard({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: MyColors.MainRedSecond.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: MyColors.MainRedSecond,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[600],
                    letterSpacing: 0.5,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: valueColor ?? Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _approveRequest(int? requestId, String comment) {
    if (requestId != null) {
      controller.approveRemoteWorkRequest(requestId, comment);
    }
  }

  void _rejectRequest(int? requestId, String comment) {
    if (requestId != null) {
      controller.rejectRemoteWorkRequest(requestId, comment);
    }
  }

  void _showCommentDialog(
      BuildContext context, int? requestId, bool isApprove) {
    TextEditingController commentController = TextEditingController();
    GlobalController global = Get.find<GlobalController>();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isApprove ? Icons.check_circle : Icons.cancel,
                color: isApprove ? Colors.green : Colors.red,
                size: 24,
              ),
              SizedBox(width: 10),
              Text(
                global.lang.value == "fr"
                    ? (isApprove
                        ? 'Approuver la demande'
                        : 'Rejeter la demande')
                    : (isApprove ? 'Approve Request' : 'Reject Request'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: isApprove ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 10),
                TextFormField(
                  controller: commentController,
                  decoration: InputDecoration(
                    labelText:
                        global.lang.value == "fr" ? 'Commentaire' : 'Comment',
                    hintText: global.lang.value == "fr"
                        ? 'Ajoutez un commentaire (optionnel)'
                        : 'Add a comment (optional)',
                    prefixIcon:
                        Icon(Icons.comment, color: MyColors.MainRedSecond),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide:
                          BorderSide(color: MyColors.MainRedSecond, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                SizedBox(
                  width: 100,
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[400],
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 2,
                    ),
                    child: Text(
                      global.lang.value == "fr" ? 'Annuler' : 'Cancel',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 100,
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () {
                      if (isApprove) {
                        _approveRequest(requestId, commentController.text);
                      } else {
                        _rejectRequest(requestId, commentController.text);
                      }
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isApprove ? Colors.green : Colors.red,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 3,
                    ),
                    child: Text(
                      global.lang.value == "fr" ? 'Confirmer' : 'Confirm',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  void _showDetailsDialog(BuildContext context, RemoteWorkRequest request) {
    GlobalController global = Get.find<GlobalController>();
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade600,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  global.lang.value == "fr"
                      ? 'Détails de la demande'
                      : 'Request Details',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Colors.blue.shade600,
                  ),
                ),
              ],
            ),
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailCard(
                    icon: Icons.work_outline,
                    label: global.lang.value == "fr"
                        ? 'Type de demande'
                        : 'Request Type',
                    value: global.lang.value == "fr"
                        ? 'Télétravail'
                        : 'Remote Work',
                  ),
                  _buildDetailCard(
                    icon: Icons.access_time,
                    label: global.lang.value == "fr" ? 'Durée' : 'Duration',
                    value:
                        '${request.quantity.toInt()} ${global.lang.value == "fr" ? "jours" : "days"}',
                  ),
                  _buildDetailCard(
                    icon: Icons.calendar_today,
                    label: global.lang.value == "fr"
                        ? 'Date de début'
                        : 'Start Date',
                    value: _formatDate(request.dateStart),
                  ),
                  _buildDetailCard(
                    icon: Icons.person,
                    label: global.lang.value == "fr"
                        ? 'Manager assigné'
                        : 'Assigned Manager',
                    value: controller
                        .getManagerName(request.assignedToManagerId ?? 0),
                  ),
                  _buildDetailCard(
                    icon: Icons.info,
                    label: global.lang.value == "fr" ? 'Statut' : 'Status',
                    value: _getTranslatedStatus(
                        controller.getStatusName(request.statusId)),
                    valueColor: _getStatusColor(
                        controller.getStatusName(request.statusId)),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade500, Colors.blue.shade700],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      global.lang.value == "fr" ? 'Fermer' : 'Close',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Color _getStatusColor(String? statusName) {
    switch (statusName) {
      case 'Submitted':
        return const Color.fromARGB(255, 224, 214, 29);
      case 'Rejected':
        return Colors.red;
      case 'Approved':
        return Colors.green;
      default:
        return Colors.black;
    }
  }

  void _showReassignManagerDialog(BuildContext context, int? requestId) {
    if (requestId != null) {
      int? selectedManagerId;
      TextEditingController commentController = TextEditingController();
      GlobalController global = Get.find<GlobalController>();

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.swap_horiz,
                  color: Colors.blue,
                  size: 20,
                ),
                SizedBox(width: 8),
                Flexible(
                  child: Text(
                    global.lang.value == "fr" ? 'Réassigner' : 'Reassign',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Information section
                    Container(
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.blue.shade700,
                            size: 16,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              global.lang.value == "fr"
                                  ? 'Choisir un nouveau manager.'
                                  : 'Choose a new manager.',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20),
                    // Manager selection section
                    Text(
                      global.lang.value == "fr"
                          ? 'Nouveau manager'
                          : 'New Manager',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    SizedBox(height: 8),
                    Obx(() {
                      if (controller.isLoading.value) {
                        return Container(
                          height: 45,
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.blue),
                                  ),
                                ),
                                SizedBox(width: 8),
                                Text(
                                  global.lang.value == "fr"
                                      ? 'Chargement...'
                                      : 'Loading...',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonFormField<int>(
                          hint: Text(
                            global.lang.value == "fr"
                                ? 'Choisir un manager'
                                : 'Choose a manager',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          value: selectedManagerId,
                          onChanged: (int? newValue) {
                            selectedManagerId = newValue;
                          },
                          items: controller.managers.map((ManagerDto manager) {
                            return DropdownMenuItem<int>(
                              value: manager.id,
                              child: Text(
                                '${manager.firstName} ${manager.lastName}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          decoration: InputDecoration(
                            labelText: global.lang.value == "fr"
                                ? 'Manager'
                                : 'Manager',
                            labelStyle: TextStyle(fontSize: 12),
                            prefixIcon: Icon(Icons.swap_horiz,
                                color: Colors.blue.shade600, size: 18),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                  color: Colors.blue.shade600, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                        ),
                      );
                    }),
                    SizedBox(height: 20),
                    // Comment section
                    Text(
                      global.lang.value == "fr"
                          ? 'Raison de la réassignation'
                          : 'Reason for Reassignment',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextFormField(
                        controller: commentController,
                        decoration: InputDecoration(
                          labelText: global.lang.value == "fr"
                              ? 'Commentaire'
                              : 'Comment',
                          labelStyle: TextStyle(fontSize: 12),
                          hintText: global.lang.value == "fr"
                              ? 'Expliquez...'
                              : 'Explain...',
                          hintStyle: TextStyle(fontSize: 12),
                          prefixIcon: Icon(
                            Icons.edit_note,
                            color: Colors.blue.shade600,
                            size: 18,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                                color: Colors.blue.shade600, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        maxLines: 3,
                        minLines: 2,
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Cancel button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: OutlinedButton(
                        onPressed: () => Get.back(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.grey[700],
                          side:
                              BorderSide(color: Colors.grey[300]!, width: 1.5),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          global.lang.value == "fr" ? 'Annuler' : 'Cancel',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 12),
                    // Reassign button
                    Container(
                      width: double.infinity,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade500, Colors.blue.shade700],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ElevatedButton(
                        onPressed: () {
                          if (selectedManagerId != null) {
                            controller.reassignManagerR(requestId,
                                selectedManagerId!, commentController.text);
                            Get.back();
                            // Show success message
                            Get.snackbar(
                              global.lang.value == "fr" ? 'Succès' : 'Success',
                              global.lang.value == "fr"
                                  ? 'Manager réassigné avec succès'
                                  : 'Manager reassigned successfully',
                              backgroundColor: Colors.green,
                              colorText: Colors.white,
                              icon:
                                  Icon(Icons.check_circle, color: Colors.white),
                              duration: Duration(seconds: 3),
                            );
                          } else {
                            Get.snackbar(
                              global.lang.value == "fr" ? 'Erreur' : 'Error',
                              global.lang.value == "fr"
                                  ? 'Veuillez sélectionner un Manager'
                                  : 'Please select a manager',
                              backgroundColor: Colors.red,
                              colorText: Colors.white,
                              icon: Icon(Icons.error, color: Colors.white),
                              duration: Duration(seconds: 3),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          global.lang.value == "fr" ? 'Réassigner' : 'Reassign',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      );
    }
  }

  String _getTranslatedStatus(String? statusName) {
    GlobalController global = Get.find();
    switch (statusName) {
      case 'Submitted':
        return global.lang.value == "fr" ? 'Soumis' : 'Submitted';
      case 'Rejected':
        return global.lang.value == "fr" ? 'Rejeté' : 'Rejected';
      case 'Approved':
        return global.lang.value == "fr" ? 'Approuvé' : 'Approved';
      default:
        return global.lang.value == "fr" ? 'Inconnu' : 'Unknown';
    }
  }
}
