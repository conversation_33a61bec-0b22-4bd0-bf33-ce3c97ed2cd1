class UserTag {
  int id;
  String userGuid;
  int idTag;
  double tagScore;
  String? comment;
  DateTime createdDate;
  int idCollaborator;

  UserTag({
    required this.id,
    required this.userGuid,
    required this.idTag,
    required this.tagScore,
    this.comment,
    required this.createdDate,
    required this.idCollaborator,
  });

  factory UserTag.fromJson(Map<String, dynamic> json) {
    return UserTag(
      id: json['id'],
      userGuid: json['userGuid'],
      idTag: json['idTag'],
      tagScore: (json['tagScore'] as num)
          .toDouble(), // Ensure tagScore is treated as double
      comment: json['comment'],
      createdDate: DateTime.parse(json['createdDate']),
      idCollaborator: json['idCollaborator'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userGuid': userGuid,
      'idTag': idTag,
      'tagScore': tagScore,
      'comment': comment,
      'createdDate': createdDate.toIso8601String(),
      'idCollaborator': idCollaborator,
    };
  }
}
