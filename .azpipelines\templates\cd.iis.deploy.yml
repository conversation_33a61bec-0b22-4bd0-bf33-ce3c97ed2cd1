parameters:
- name: TargetEnvironment
  type: string
- name: appsParameters
  type: object

jobs:
- deployment:
  displayName: "Web App Deployment"
  pool:
    vmImage: 'windows-latest' 
  environment:
    name: ${{ parameters.TargetEnvironment }}
    resourceType: VirtualMachine
  strategy:
    runOnce:
        deploy:
            steps:
            - task: FileTransform@1
              displayName: 'Configuring appsettings.json'
              inputs:
                folderPath: '$(Agent.BuildDirectory)/${{parameters.appsParameters.FileTransformFolderPath}}'
                fileType: json
                targetFiles: '**/appsettings.json'
            - task: IISWebAppManagementOnMachineGroup@0
              displayName: 'Configure IIS Web Site'
              inputs:
                EnableIIS: true
                IISDeploymentType: 'IISWebsite'
                ActionIISWebsite: 'CreateOrUpdateWebsite'
                WebsiteName: '${{parameters.appsParameters.WebsiteName}}'
                WebsitePhysicalPath: '%SystemDrive%\inetpub\wwwroot\${{parameters.appsParameters.WebsiteName}}'
                AddBinding: true
                Bindings: |
                    {
                        bindings:[
                            {
                                "protocol":"${{parameters.appsParameters.Binding.protocol}}",
                                "ipAddress":"${{parameters.appsParameters.Binding.ipAddress}}",
                                "hostname":"${{parameters.appsParameters.Binding.hostname}}",
                                "port":"${{parameters.appsParameters.Binding.port}}",
                                "sslThumbprint":"${{parameters.appsParameters.Binding.sslThumbprint}}",
                                "sniFlag":false
                            }
                        ]
                    }
                CreateOrUpdateAppPoolForWebsite: true
                AppPoolNameForWebsite: 'POOL_${{parameters.appsParameters.WebsiteName}}'
                DotNetVersionForWebsite: 'No Managed Code'
                ParentWebsiteNameForVD: '${{parameters.appsParameters.WebsiteName}}'
                VirtualPathForVD: '${{parameters.appsParameters.WebsiteName}}'
                ParentWebsiteNameForApplication: '${{parameters.appsParameters.WebsiteName}}'
                VirtualPathForApplication: '${{parameters.appsParameters.WebsiteName}}'
                AppPoolName: 'POOL_${{parameters.appsParameters.WebsiteName}}'

            - task: IISWebAppDeploymentOnMachineGroup@0
              displayName: 'IIS Deployment'
              inputs:
                WebSiteName: '${{parameters.appsParameters.WebsiteName}}'
                Package: '$(Agent.BuildDirectory)/${{parameters.appsParameters.FileTransformFolderPath}}'
                RemoveAdditionalFilesFlag: true
                TakeAppOfflineFlag: true
                XmlTransformation: true
                XmlVariableSubstitution: true       

