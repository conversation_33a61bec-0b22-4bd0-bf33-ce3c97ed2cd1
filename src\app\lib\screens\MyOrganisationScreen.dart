import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/myorganisationcontroller.dart';
import 'package:tmt_mobile/widgets/orgContainer.dart';
import 'package:tmt_mobile/utils/myColors.dart';

class MyOrganisationScreen extends StatelessWidget {
  const MyOrganisationScreen({super.key});

  // Translation helper method
  String _translate(String key, GlobalController global) {
    if (global.lang.value == "fr") {
      switch (key) {
        case 'my_organizations':
          return 'Mes Organisations';
        case 'select_organization':
          return 'Sélectionnez votre organisation';
        case 'select_organization_continue':
          return 'Sélectionnez votre organisation pour continuer';
        case 'loading_organizations':
          return 'Chargement des organisations...';
        case 'error':
          return 'Erreur';
        case 'retry':
          return 'Réessayer';
        case 'no_organizations':
          return 'Aucune Organisation';
        case 'no_organizations_message':
          return 'Vous n\'appartenez à aucune organisation. Veuillez contacter votre administrateur.';
        case 'no_organizations_message_tablet':
          return 'Vous n\'appartenez à aucune organisation. Veuillez contacter votre administrateur pour obtenir l\'accès.';
        case 'pull_to_refresh':
          return 'Tirez vers le bas pour actualiser';
        case 'selected':
          return 'Sélectionné';
        case 'tap_to_select':
          return 'Appuyez pour sélectionner';
        default:
          return key;
      }
    } else {
      switch (key) {
        case 'my_organizations':
          return 'My Organizations';
        case 'select_organization':
          return 'Select your organization';
        case 'select_organization_continue':
          return 'Select your organization to continue';
        case 'loading_organizations':
          return 'Loading organizations...';
        case 'error':
          return 'Error';
        case 'retry':
          return 'Retry';
        case 'no_organizations':
          return 'No Organizations';
        case 'no_organizations_message':
          return 'You don\'t belong to any organization. Please contact your administrator.';
        case 'no_organizations_message_tablet':
          return 'You don\'t belong to any organization. Please contact your administrator for access.';
        case 'pull_to_refresh':
          return 'Pull down to refresh';
        case 'selected':
          return 'Selected';
        case 'tap_to_select':
          return 'Tap to select';
        default:
          return key;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final GlobalController global = Get.find<GlobalController>();

    return FutureBuilder(
      future: Get.putAsync<MyOrganisationController>(() async {
        final controller = MyOrganisationController(global);
        await controller.getUserOrg(); // Ensure data is loaded before UI builds
        return controller;
      }),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final controller = snapshot.data as MyOrganisationController;

        return Scaffold(
          body: global.devType.value == "tablet"
              ? organisationScreenTablet(controller, global)
              : organisationScreenAndroid(controller, global),
        );
      },
    );
  }

  Widget organisationScreenAndroid(
      MyOrganisationController controller, GlobalController global) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[50]!,
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          // Modern Header Section

          // Content Section
          Expanded(
            child: RefreshIndicator(
              onRefresh: controller.refreshOrganizations,
              color: MyColors.MainRedBig,
              child: Obx(() {
                if (controller.isLoading.value) {
                  return _buildLoadingState(global);
                }

                if (controller.errorMessage.value.isNotEmpty) {
                  return _buildErrorState(controller, global);
                }

                if (controller.orglist.isNotEmpty) {
                  return _buildOrganizationsList(controller);
                }

                return _buildEmptyState(global);
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget organisationScreenTablet(
      MyOrganisationController controller, GlobalController global) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[50]!,
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          // Modern Header Section for Tablet
          Container(
            padding: EdgeInsets.fromLTRB(40, 30, 40, 24),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedBig.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.business_outlined,
                    color: MyColors.MainRedBig,
                    size: 32,
                  ),
                ),
                SizedBox(width: 24),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Obx(() => Text(
                            _translate('my_organizations', global),
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          )),
                      SizedBox(height: 8),
                      Obx(() => Text(
                            _translate('select_organization_continue', global),
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Content Section for Tablet
          Expanded(
            child: RefreshIndicator(
              onRefresh: controller.refreshOrganizations,
              color: MyColors.MainRedBig,
              child: Obx(() {
                if (controller.isLoading.value) {
                  return _buildLoadingStateTablet(global);
                }

                if (controller.errorMessage.value.isNotEmpty) {
                  return _buildErrorStateTablet(controller, global);
                }

                if (controller.orglist.isNotEmpty) {
                  return _buildOrganizationsListTablet(controller);
                }

                return _buildEmptyStateTablet(global);
              }),
            ),
          ),
        ],
      ),
    );
  }

  // Helper Methods for Modern UI States
  Widget _buildLoadingState(GlobalController global) {
    return ListView(
      children: [
        SizedBox(height: MediaQuery.of(Get.context!).size.height * 0.3),
        Center(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: MyColors.MainRedBig.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: CircularProgressIndicator(
                  color: MyColors.MainRedBig,
                  strokeWidth: 3,
                ),
              ),
              SizedBox(height: 24),
              Obx(() => Text(
                    _translate('loading_organizations', global),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  )),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(
      MyOrganisationController controller, GlobalController global) {
    return ListView(
      children: [
        SizedBox(height: MediaQuery.of(Get.context!).size.height * 0.25),
        Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 20),
            padding: EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red,
                  ),
                ),
                SizedBox(height: 20),
                Obx(() => Text(
                      _translate('error', global),
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    )),
                SizedBox(height: 12),
                Text(
                  controller.errorMessage.value,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton.icon(
                    onPressed: controller.refreshOrganizations,
                    icon: Icon(Icons.refresh, size: 20),
                    label: Obx(() => Text(
                          _translate('retry', global),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        )),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: MyColors.MainRedBig,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrganizationsList(MyOrganisationController controller) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      itemCount: controller.orglist.length,
      itemBuilder: (context, index) => Obx(
        () => Container(
          margin: EdgeInsets.only(bottom: 12),
          child: OrgContainer(
            name: controller.orglist[index].name.toString(),
            clicked: index == controller.selectedOrgIndex.value,
            clickme: () async {
              var name = controller.orglist[index].guid.toString();
              await controller.selectedOrg(name, index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(GlobalController global) {
    return ListView(
      children: [
        SizedBox(height: MediaQuery.of(Get.context!).size.height * 0.25),
        Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 20),
            padding: EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.business_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                ),
                SizedBox(height: 24),
                Obx(() => Text(
                      _translate('no_organizations', global),
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    )),
                SizedBox(height: 12),
                Obx(() => Text(
                      _translate('no_organizations_message', global),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    )),
                SizedBox(height: 20),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedBig.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.refresh,
                        size: 16,
                        color: MyColors.MainRedBig,
                      ),
                      SizedBox(width: 8),
                      Obx(() => Text(
                            _translate('pull_to_refresh', global),
                            style: TextStyle(
                              color: MyColors.MainRedBig,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Tablet-specific helper methods
  Widget _buildLoadingStateTablet(GlobalController global) {
    return ListView(
      children: [
        SizedBox(height: MediaQuery.of(Get.context!).size.height * 0.25),
        Center(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: MyColors.MainRedBig.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: CircularProgressIndicator(
                  color: MyColors.MainRedBig,
                  strokeWidth: 4,
                ),
              ),
              SizedBox(height: 32),
              Obx(() => Text(
                    _translate('loading_organizations', global),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  )),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorStateTablet(
      MyOrganisationController controller, GlobalController global) {
    return ListView(
      children: [
        SizedBox(height: MediaQuery.of(Get.context!).size.height * 0.2),
        Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 40),
            padding: EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 15,
                  offset: Offset(0, 6),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                ),
                SizedBox(height: 24),
                Obx(() => Text(
                      _translate('error', global),
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    )),
                SizedBox(height: 16),
                Text(
                  controller.errorMessage.value,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 16,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 32),
                SizedBox(
                  width: 200,
                  height: 52,
                  child: ElevatedButton.icon(
                    onPressed: controller.refreshOrganizations,
                    icon: Icon(Icons.refresh, size: 24),
                    label: Obx(() => Text(
                          _translate('retry', global),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        )),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: MyColors.MainRedBig,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrganizationsListTablet(MyOrganisationController controller) {
    return GridView.builder(
      padding: EdgeInsets.symmetric(horizontal: 40, vertical: 16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 20,
        mainAxisSpacing: 20,
        childAspectRatio: 3.5,
      ),
      itemCount: controller.orglist.length,
      itemBuilder: (context, index) => Obx(
        () => OrgContainer(
          name: controller.orglist[index].name.toString(),
          clicked: index == controller.selectedOrgIndex.value,
          clickme: () async {
            var name = controller.orglist[index].guid.toString();
            await controller.selectedOrg(name, index);
          },
        ),
      ),
    );
  }

  Widget _buildEmptyStateTablet(GlobalController global) {
    return ListView(
      children: [
        SizedBox(height: MediaQuery.of(Get.context!).size.height * 0.2),
        Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 40),
            padding: EdgeInsets.all(40),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 15,
                  offset: Offset(0, 6),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    Icons.business_outlined,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                ),
                SizedBox(height: 32),
                Obx(() => Text(
                      _translate('no_organizations', global),
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    )),
                SizedBox(height: 16),
                Obx(() => Text(
                      _translate('no_organizations_message_tablet', global),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    )),
                SizedBox(height: 24),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  decoration: BoxDecoration(
                    color: MyColors.MainRedBig.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.refresh,
                        size: 20,
                        color: MyColors.MainRedBig,
                      ),
                      SizedBox(width: 12),
                      Obx(() => Text(
                            _translate('pull_to_refresh', global),
                            style: TextStyle(
                              color: MyColors.MainRedBig,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
