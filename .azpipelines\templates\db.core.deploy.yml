parameters:
- name: coreConnectionString
  type: string
- name: jobName
  type: string
jobs:
- deployment: ${{parameters.jobName}}
  displayName: "Core DB Updates"
  environment: 'TMT-QA'
  pool:
    vmImage: 'windows-latest'   
  strategy:
    runOnce:
      deploy:
          steps:
          - checkout: self

          - task: rdagumampan.yuniql-azdevops-extensions.install-task.UseYuniqlCLI@1
            displayName: 'Use Yuniql'

          - task: rdagumampan.yuniql-azdevops-extensions.run-task.RunYuniqlCLI@1
            displayName: 'Run Yuniql'
            inputs:
                connectionString: ${{parameters.coreConnectionString}}
                workspacePath: '$(System.DefaultWorkingDirectory)/db/Core'
                targetPlatform: postgresql
                autoCreateDatabase: true
                additionalArguments: '--debug'

