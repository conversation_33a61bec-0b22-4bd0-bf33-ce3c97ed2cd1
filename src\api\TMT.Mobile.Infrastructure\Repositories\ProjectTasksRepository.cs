﻿

using Microsoft.EntityFrameworkCore;

using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class ProjectTasksRepository : CoreRepository<Projecttasks, DynamicDbContext>
    {

        public ProjectTasksRepository(DynamicDbContext context) : base(context)
        {
        }

        public async Task<Projecttasks>? GetProjectTasks(int id)
        {
            try
            {
                var projecttask = await context.Projecttasks.Where(p => p.Id == id).SingleAsync();
                return projecttask!;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}


