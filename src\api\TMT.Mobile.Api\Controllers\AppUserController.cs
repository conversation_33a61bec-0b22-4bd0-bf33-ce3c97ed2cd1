using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Repositories;
using System.Security.Claims;
using TMT.Mobile.Core.Consts;
using Microsoft.EntityFrameworkCore;
using TMT.Mobile.Core.Entities.DTOs;

namespace TMT.Mobile.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AppUserController : ControllerBase
    {        
        private readonly ILogger<AppUserController> _logger;
        private readonly AppUserRepository _appUserRepository;

        public AppUserController(        
            ILogger<AppUserController> logger,
            AppUserRepository appUserRepository)
        {
            _logger = logger;
            _appUserRepository = appUserRepository;
        }


[HttpGet("get-my-manager")]
public async Task<ActionResult<ManagerHierarchyDto>> GetMyManagerHierarchy()
{
    var email = User.FindFirst(ClaimTypes.Email)?.Value;
    if (string.IsNullOrEmpty(email))
    {
        return BadRequest("Email not found");
    }

    var managerHierarchy = await _appUserRepository.GetManagerHierarchyByUserEmail(email);
    return managerHierarchy != null ? Ok(managerHierarchy) : NotFound();
}
    }
}
