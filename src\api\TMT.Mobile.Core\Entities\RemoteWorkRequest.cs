using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
  public class RemoteWorkRequest : IEntity
  {
    public int Id { get; set; }
    public int RequestorId { get; set; }
    public int? AssignedToManagerId { get; set; }
    public double Quantity { get; set; }
    public DateTime DateStart { get; set; }
    public string? Comment { get; set; }
    public int StatusId { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public Appvariables Status { get; set; }
    // public Appusers Requestor { get; set; }
    // public Appusers? AssignedToManager { get; set; }  
    }
}
