using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TagsController : ControllerBase
    {
        private readonly TagsRepository _tagsRepository;

        public TagsController(TagsRepository tagsRepository)
        {
            _tagsRepository = tagsRepository;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllTags()
        {
            var variables = await _tagsRepository.GetAllAsync();
            return Ok(variables);
        }
    }
}
