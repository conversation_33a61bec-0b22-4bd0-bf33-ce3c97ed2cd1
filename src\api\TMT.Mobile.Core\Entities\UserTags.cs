using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;

namespace TMT.Mobile.Core.Entities
{
  public class UserTags : IEntity
  {
    public int Id { get; set; }
    public Guid UserGuid { get; set; }
    public int IdTag { get; set; }

    public double TagScore { get; set; }
    public bool? IsLatest { get; set; }

    public string? CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedDate { get; set; }

    /// <summary>
    /// Person To assess
    /// </summary>
    public Appusers? Collaborator { get; set; }
    public Tag? Tag { get; set; }
    public string? Comment { get; set; }
    public int? IdCollaborator { get; set; }
  }
}
