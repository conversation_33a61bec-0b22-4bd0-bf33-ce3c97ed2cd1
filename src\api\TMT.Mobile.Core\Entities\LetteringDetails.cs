 using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
    public class LetteringDetail : IEntity
  {
    public int Id { get; set; }
    public int IdLettering { get; set; }
   // public LetteringDetailType DetailType { get; set; }
    /// <summary>
    /// +1 for incoming bills,  -1 for outcoming payments & credits
    /// </summary>
    public int Direction { get; set; }
    public int? IdBill { get; set; }
    public int? IdPayment { get; set; }
    public double Amount { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime Createddate { get; set; } = DateTime.Now;
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public Lettering Lettering { get; set; }
    public Bill? Bill { get; set; }
    public Payment? Payment { get; set; }
  }
}
