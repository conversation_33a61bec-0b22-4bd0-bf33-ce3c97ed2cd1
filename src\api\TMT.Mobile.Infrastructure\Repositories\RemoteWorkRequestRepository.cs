using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
namespace TMT.Mobile.Infrastructure.Repositories
{
    public class RemoteWorkRequestRepository : CoreRepository<RemoteWorkRequest, DynamicDbContext>
    {

        public RemoteWorkRequestRepository(DynamicDbContext context) : base(context)
        {

        }

        public async Task<IEnumerable<RemoteWorkRequest>> GetAllAsync()
        {
                return await context.RemoteWorkRequests
                         .Include(r => r.Status) 
                         .ToListAsync();        }

        public async Task<RemoteWorkRequest?> GetByIdAsync(int id)
        {
                return await context.RemoteWorkRequests
                         .Include(r => r.Status) 
                         .FirstOrDefaultAsync(r => r.Id == id);        }

        public async Task AddAsync(RemoteWorkRequest remoteWorkRequest)
        {
            await context.RemoteWorkRequests.AddAsync(remoteWorkRequest);
            await context.SaveChangesAsync();
        }

        public async Task UpdateAsync(RemoteWorkRequest remoteWorkRequest)
        {
            context.RemoteWorkRequests.Update(remoteWorkRequest);
            await context.SaveChangesAsync();
        }

        public async Task DeleteAsync(RemoteWorkRequest remoteWorkRequest)
        {
            context.RemoteWorkRequests.Remove(remoteWorkRequest);
            await context.SaveChangesAsync();
        }

       public async Task<List<RemoteWorkRequest>> GetByUserIdAsync(int requestorId)
        {
            return await context.RemoteWorkRequests
                .Where(lr => lr.RequestorId == requestorId && !lr.Isdeleted)
                .ToListAsync();
        }

        public async Task<List<RemoteWorkRequest>> GetAssignedToUserAsync(int requestorId)
        {
            return await context.RemoteWorkRequests
                .Where(lr => !lr.Isdeleted && 
                            (lr.AssignedToManagerId == requestorId))
                .ToListAsync();
        }
    }
}