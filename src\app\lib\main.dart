import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/screens/splashScreen.dart';
import 'package:tmt_mobile/screens/remote_work_requests_list_screen.dart';
import 'package:tmt_mobile/screens/vacation_requests_list_screen.dart';
import 'package:tmt_mobile/utils/userPrefrences.dart';
import 'package:tmt_mobile/utils/utils.dart';
import 'dart:io';

void main() async {
  // Ignore SSL Certificate
  HttpOverrides.global = MyHttpOverrides();

  WidgetsFlutterBinding.ensureInitialized();

  // Register GlobalController before runApp
  Get.put(GlobalController(), permanent: true);

  if (getDeviceType() == "tablet") {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  } else {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  await UserPrefrences.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Tmt Software',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: SplashPage(),
      routes: {
        '/remote_work_requests_list_screen': (context) =>
            RemoteWorkRequestsListScreen(),
        '/vacation_requests_list_screen': (context) =>
            VacationRequestsListScreen(),
      },
      onGenerateRoute: (settings) {
        if (settings.name == '/remote_work_requests_list_screen') {
          return MaterialPageRoute(
              builder: (context) => RemoteWorkRequestsListScreen());
        }
        if (settings.name == '/vacation_requests_list_screen') {
          return MaterialPageRoute(
              builder: (context) => VacationRequestsListScreen());
        }
        return null;
      },
    );
  }
}
