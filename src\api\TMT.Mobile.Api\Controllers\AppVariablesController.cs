using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Repositories;

namespace TMT.Mobile.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AppVariableController : ControllerBase
    {
        private readonly AppvariableRepository _appvariableRepository;

        public AppVariableController(AppvariableRepository appvariableRepository)
        {
            _appvariableRepository = appvariableRepository;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllAppVariables([FromQuery] string language = "en")
        {
            var variables = await _appvariableRepository.GetAllAsync(language);
            return Ok(variables);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetAppVariableById(int id, [FromQuery] string language = "en")
        {
            var variable = await _appvariableRepository.GetByIdAsync(id, language);
            if (variable == null)
            {
                string errorMessage = language.ToLower() == "fr" 
                    ? $"Aucune variable trouvée avec l'ID {id}" 
                    : $"No variable found with ID {id}";
                return NotFound(errorMessage);
            }

            return Ok(variable);
        }

        [HttpGet("leave-request-types")]
        public async Task<IActionResult> GetLeaveRequestTypes([FromQuery] string language = "en")
        {
            var variables = await _appvariableRepository.GetLeaveRequestTypesAsync(language);
            return Ok(variables);
        }
        
        [HttpGet("remote-work-status")]
        public async Task<IActionResult> GetRemoteWorkStatuses([FromQuery] string language = "en")
        {
            var statuses = await _appvariableRepository.GetRemoteWorkRequestTypesAsync(language);
            return Ok(statuses);
        }

        [HttpGet("expense-request-status")]
        public async Task<IActionResult> GetExpenseRequestStatus([FromQuery] string language = "en")
        {
            var statuses = await _appvariableRepository.GetExpenseRequestTypesAsync(language);
            return Ok(statuses);
        }

        [HttpGet("project-fees-types")]
        public async Task<IActionResult> GetProjectFeesTypes([FromQuery] string language = "en")
        {
            var statuses = await _appvariableRepository.GetProjectFeesTypesAsync(language);
            return Ok(statuses);
        }
         [HttpGet("project-fees-status")]
        public async Task<IActionResult> GetProjectFeesStatus([FromQuery] string language = "en")
        {
            var statuses = await _appvariableRepository.GetProjectFeesStatusAsync(language);
            return Ok(statuses);
        }
        [HttpGet("statusByName/{name}")]
        public async Task<IActionResult> GetStatusIdByNameAsync(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                return BadRequest("Name is required.");
            }

            var statusId = await _appvariableRepository.GetStatusIdByNameAsync(name);
            
            if (statusId == null)
            {
                return NotFound($"Status with name '{name}' not found.");
            }

            return Ok(statusId);
        }
    }
}