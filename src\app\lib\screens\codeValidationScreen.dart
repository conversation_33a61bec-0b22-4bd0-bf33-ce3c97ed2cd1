import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/codevalidationcontroller.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/menucontroller.dart';
import 'package:tmt_mobile/screens/MenuScreen.dart';
import 'package:tmt_mobile/screens/landingScreen.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/widgets/inputfield.dart';

class CodeValidationScreen extends StatelessWidget {
  const CodeValidationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    GlobalController globalController = Get.find<GlobalController>();
    Get.put(CodeValidationController(globalController));
    CodeValidationController controller = Get.find<CodeValidationController>();
    return Scaffold(
        appBar: AppBar(
          backgroundColor: MyColors.MainRedBig,
          centerTitle: true,
          elevation: 0,
          title: Text(
            globalController.lang.value == "fr" ? "Validation" : "Validation",
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
              fontFamily: "aileron",
            ),
          ),
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios, color: Colors.white, size: 22),
            onPressed: () {
              Get.offAll(LandingScreen());
            },
          ),
        ),
        backgroundColor: Colors.grey[50],
        body: globalController.devType.value == "tablet"
            ? codevalidationTablet(
                screenWidth, screenHeight, globalController, controller)
            : codevalidationAndroid(
                screenHeight, globalController, controller, screenWidth));
  }

  Widget codevalidationAndroid(
      double screenHeight,
      GlobalController globalController,
      CodeValidationController controller,
      double screenWidth) {
    return Container(
      height: screenHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[50]!,
            Colors.white,
          ],
        ),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Email sent message
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue[200]!, width: 1),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.email_outlined,
                      color: Colors.blue[600],
                      size: 24,
                    ),
                    SizedBox(height: 8),
                    Text(
                      globalController.lang.value == "fr"
                          ? "Un code de validation a été envoyé à"
                          : "A verification code has been sent to",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue[700],
                        fontFamily: "aileron",
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      globalController.appuser.value.email ?? "",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue[800],
                        fontFamily: "aileron",
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              SizedBox(height: screenHeight * 0.025),

              // Form with Code Input Field and Button
              Form(
                key: controller.validCodeKey,
                child: Column(
                  children: [
                    // Code Input Field with modern styling
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Myinput(
                        controller: controller.code.value,
                        validate: (v) => controller.validateThese(v!),
                        labelText: globalController.lang.value == "fr"
                            ? "Code de Validation"
                            : "Validation Code",
                        icon: Icons.security,
                        fontsize: 18,
                        aligncenter: true,
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.025),

                    // Validate Button with modern styling
                    Container(
                      width: double.infinity,
                      height: 56,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          colors: [MyColors.MainRedBig, MyColors.MainRedSecond],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: MyColors.MainRedBig.withOpacity(0.3),
                            blurRadius: 12,
                            offset: Offset(0, 6),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: () async {
                          bool isValidate =
                              controller.validCodeKey.currentState!.validate();
                          if (isValidate) {
                            var checksub = await controller.onSubmit(
                                globalController.appuser.value.email.toString(),
                                controller.code.value.text);
                            if (checksub == true) {
                              await Get.putAsync<Menucontroller>(
                                  () async => Menucontroller(),
                                  permanent: true);
                              Menucontroller menuController =
                                  Get.find<Menucontroller>();
                              menuController.screenindex.value = 1;
                              Get.offAll(MenuScreen());
                            }
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Text(
                          globalController.lang.value == "fr"
                              ? "Valider"
                              : "Validate",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            fontFamily: "aileron",
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: screenHeight * 0.015),

              // Error Message with modern styling
              Visibility(
                visible: controller.showError.value,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  margin: EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.red[200]!, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: MyColors.MainRedBig,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          controller.errormsg.value,
                          style: TextStyle(
                            color: MyColors.MainRedBig,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            fontFamily: "aileron",
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Resend Code Button with modern styling
              SizedBox(
                width: double.infinity,
                child: TextButton.icon(
                  onPressed: () async {
                    if (controller.loading.value == false) {
                      await controller.sendValidationCode(
                          globalController.appuser.value.email!);
                    }
                  },
                  icon: Icon(
                    Icons.refresh,
                    color: MyColors.thirdColor,
                    size: 18,
                  ),
                  label: Text(
                    globalController.lang.value == "fr"
                        ? "Renvoyer le code"
                        : "Resend the Code",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: MyColors.thirdColor,
                      fontFamily: "aileron",
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                    backgroundColor: MyColors.thirdColor.withOpacity(0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

              SizedBox(height: screenHeight * 0.015),
            ],
          ),
        ),
      ),
    );
  }

  Widget codevalidationTablet(double screenWidth, double screenHeight,
      GlobalController globalController, CodeValidationController controller) {
    return Container(
      height: screenHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[50]!,
            Colors.white,
          ],
        ),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(50.0),
          child: Center(
            child: SizedBox(
              width: screenWidth * 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: screenHeight * 0.04),

                  // Header Icon for Tablet
                  Container(
                    padding: EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: MyColors.MainRedBig.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Icon(
                      Icons.mark_email_read_outlined,
                      color: MyColors.MainRedBig,
                      size: 64,
                    ),
                  ),

                  SizedBox(height: screenHeight * 0.03),

                  // Title for Tablet
                  Text(
                    globalController.lang.value == "fr"
                        ? "Vérification par e-mail"
                        : "Email Verification",
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: MyColors.mainblack,
                      fontFamily: "aileron",
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.02),

                  // Email sent message for Tablet
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.blue[200]!, width: 1),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.email_outlined,
                          color: Colors.blue[600],
                          size: 32,
                        ),
                        SizedBox(height: 12),
                        Text(
                          globalController.lang.value == "fr"
                              ? "Un code de validation a été envoyé à"
                              : "A verification code has been sent to",
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.blue[700],
                            fontFamily: "aileron",
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 8),
                        Text(
                          globalController.appuser.value.email ?? "",
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue[800],
                            fontFamily: "aileron",
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: screenHeight * 0.03),

                  // Instructions for Tablet
                  Text(
                    globalController.lang.value == "fr"
                        ? "Veuillez saisir le code de validation ci-dessous"
                        : "Please enter the verification code below",
                    style: TextStyle(
                      fontSize: 20,
                      color: MyColors.Strokecolor,
                      fontFamily: "aileron",
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.04),

                  // Form with Code Input Field and Button for Tablet
                  Form(
                    key: controller.validCodeKey,
                    child: Column(
                      children: [
                        // Code Input Field for Tablet with modern styling
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 15,
                                offset: Offset(0, 6),
                              ),
                            ],
                          ),
                          child: Myinput(
                            controller: controller.code.value,
                            validate: (v) => controller.validateThese(v!),
                            labelText: globalController.lang.value == "fr"
                                ? "Code de Validation"
                                : "Validation Code",
                            icon: Icons.security,
                            fontsize: 20,
                            aligncenter: true,
                          ),
                        ),

                        SizedBox(height: screenHeight * 0.04),

                        // Validate Button for Tablet with modern styling
                        Container(
                          width: double.infinity,
                          height: 64,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            gradient: LinearGradient(
                              colors: [
                                MyColors.MainRedBig,
                                MyColors.MainRedSecond
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: MyColors.MainRedBig.withOpacity(0.3),
                                blurRadius: 15,
                                offset: Offset(0, 8),
                              ),
                            ],
                          ),
                          child: ElevatedButton(
                            onPressed: () async {
                              bool isValidate = controller
                                  .validCodeKey.currentState!
                                  .validate();
                              if (isValidate) {
                                var checksub = await controller.onSubmit(
                                    globalController.appuser.value.email
                                        .toString(),
                                    controller.code.value.text);
                                if (checksub == true) {
                                  await Get.putAsync<Menucontroller>(
                                      () async => Menucontroller(),
                                      permanent: true);
                                  Menucontroller menuController =
                                      Get.find<Menucontroller>();
                                  menuController.screenindex.value = 1;
                                  Get.offAll(MenuScreen());
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: Text(
                              globalController.lang.value == "fr"
                                  ? "Valider"
                                  : "Validate",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                fontFamily: "aileron",
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: screenHeight * 0.02),

                  // Error Message for Tablet with modern styling
                  Visibility(
                    visible: controller.showError.value,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(20),
                      margin: EdgeInsets.only(bottom: 20),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.red[200]!, width: 1),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: MyColors.MainRedBig,
                            size: 28,
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              controller.errormsg.value,
                              style: TextStyle(
                                color: MyColors.MainRedBig,
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                fontFamily: "aileron",
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Resend Code Button for Tablet with modern styling
                  SizedBox(
                    width: double.infinity,
                    child: TextButton.icon(
                      onPressed: () async {
                        if (controller.loading.value == false) {
                          await controller.sendValidationCode(
                              globalController.appuser.value.email!);
                        }
                      },
                      icon: Icon(
                        Icons.refresh,
                        color: MyColors.thirdColor,
                        size: 24,
                      ),
                      label: Text(
                        globalController.lang.value == "fr"
                            ? "Renvoyer le code"
                            : "Resend the Code",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: MyColors.thirdColor,
                          fontFamily: "aileron",
                        ),
                      ),
                      style: TextButton.styleFrom(
                        padding:
                            EdgeInsets.symmetric(vertical: 20, horizontal: 32),
                        backgroundColor: MyColors.thirdColor.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: screenHeight * 0.03),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
