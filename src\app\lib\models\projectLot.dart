class ProjectLot {
  int id;
  int idprojet;
  String name;

  ProjectLot({required this.id, required this.idprojet, required this.name});

  factory ProjectLot.fromJson(Map<String, dynamic> json) {
    return ProjectLot(
      id: json['id'],
      idprojet: json['idprojet'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'idprojet': idprojet,
      'name': name,
    };
  }
}
