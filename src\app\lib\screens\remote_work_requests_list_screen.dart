import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/remote_work_request_controller.dart';
import 'package:tmt_mobile/models/remote_work_request_model.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/widgets/buttonwithicon.dart';

import 'package:tmt_mobile/controllers/globalcontroller.dart';

class RemoteWorkRequestsListScreen extends StatefulWidget {
  const RemoteWorkRequestsListScreen({super.key});

  @override
  _RemoteWorkRequestsListScreenState createState() =>
      _RemoteWorkRequestsListScreenState();
}

class _RemoteWorkRequestsListScreenState
    extends State<RemoteWorkRequestsListScreen> {
  final RemoteWorkRequestController controller =
      Get.put(RemoteWorkRequestController());
  String selectedStatus = 'All';

  @override
  Widget build(BuildContext context) {
    controller.fetchRemoteWorkRequests();
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Obx(() {
        // Get the GlobalController inside Obx to properly observe lang changes
        GlobalController global = Get.find<GlobalController>();

        if (controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(MyColors.MainRedSecond),
                ),
                SizedBox(height: 16),
                Text(
                  global.lang.value == "fr"
                      ? 'Chargement des demandes...'
                      : 'Loading requests...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }
        var filteredRequests = controller.remoteWorkRequests.where((request) {
          if (selectedStatus == 'All') return true;
          return controller.getStatusName(request.statusId) == selectedStatus;
        }).toList();
        if (filteredRequests.isEmpty) {
          return Center(
            child: Container(
              padding: EdgeInsets.all(32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: MyColors.MainRedSecond.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Icon(
                      Icons.work_off,
                      size: 64,
                      color: MyColors.MainRedSecond,
                    ),
                  ),
                  SizedBox(height: 24),
                  Text(
                    global.lang.value == "fr"
                        ? 'Aucune demande trouvée'
                        : 'No Requests Found',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    global.lang.value == "fr"
                        ? 'Aucune demande de travail à distance trouvée pour le filtre sélectionné.'
                        : 'No remote work requests found for the selected filter.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 32),
                  ButtonWithIcon(
                    text: global.lang.value == "fr" ? 'Actualiser' : 'Refresh',
                    mainColor: MyColors.MainRedSecond,
                    textcolor: Colors.white,
                    onPressed: () => controller.fetchRemoteWorkRequests(),
                    height: 48,
                    width: 140,
                    fontSize: 16,
                  ),
                ],
              ),
            ),
          );
        }
        return RefreshIndicator(
          color: MyColors.MainRedSecond,
          onRefresh: () async {
            await controller.fetchRemoteWorkRequests();
          },
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: filteredRequests.length,
            itemBuilder: (context, index) {
              var request = filteredRequests[index];
              return Obx(() {
                GlobalController global = Get.find<GlobalController>();
                return Container(
                  margin: EdgeInsets.only(bottom: 16),
                  child: _buildModernRequestCard(request, global),
                );
              });
            },
          ),
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showFilterDialog(context);
        },
        backgroundColor: MyColors.MainRedSecond,
        child: Icon(Icons.filter_list, color: Colors.white),
      ),
    );
  }

  Widget _buildModernRequestCard(
      RemoteWorkRequest request, GlobalController global) {
    return GestureDetector(
      onTap: () => _showDetailsDialog(context, request),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _showDetailsDialog(context, request),
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _getStatusColor(
                                  controller.getStatusName(request.statusId))
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.work_outline,
                          color: _getStatusColor(
                              controller.getStatusName(request.statusId)),
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              global.lang.value == "fr"
                                  ? 'Demande N° ${request.id}'
                                  : 'Request N° ${request.id}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.info,
                                  size: 16,
                                  color: _getStatusColor(controller
                                      .getStatusName(request.statusId)),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  _getTranslatedStatus(controller
                                      .getStatusName(request.statusId)),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: _getStatusColor(controller
                                        .getStatusName(request.statusId)),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      if (controller.getStatusName(request.statusId) ==
                          'Submitted') ...[
                        Container(
                          margin: EdgeInsets.only(left: 8),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.blue.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: Icon(Icons.edit,
                                      color: Colors.blue, size: 20),
                                  onPressed: () =>
                                      _showEditDialog(context, request),
                                  padding: EdgeInsets.all(8),
                                  constraints: BoxConstraints(),
                                ),
                              ),
                              SizedBox(width: 8),
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.red.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: Icon(Icons.delete,
                                      color: Colors.red, size: 20),
                                  onPressed: () => _deleteRequest(request.id),
                                  padding: EdgeInsets.all(8),
                                  constraints: BoxConstraints(),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ] else ...[
                        Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: MyColors.MainRedSecond.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.arrow_forward_ios,
                            size: 16,
                            color: MyColors.MainRedSecond,
                          ),
                        ),
                      ],
                    ],
                  ),
                  SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              SizedBox(width: 8),
                              Text(
                                '${request.quantity.toInt()} ${global.lang.value == "fr" ? "jours" : "days"}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _formatDate(request.dateStart),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Obx(() {
          GlobalController global = Get.find();
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 10,
            backgroundColor: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Container(
                    padding: EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          MyColors.MainRedSecond,
                          MyColors.MainRedSecond.withOpacity(0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            Icons.filter_list,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            global.lang.value == "fr"
                                ? 'Filtrer par statut'
                                : 'Filter by Status',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Get.back(),
                          child: Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Content
                  Padding(
                    padding: EdgeInsets.all(20),
                    child: Column(
                      children: [
                        _buildFilterOption(
                          context: context,
                          title: global.lang.value == "fr" ? 'Tous' : 'All',
                          icon: Icons.list_alt,
                          isSelected: selectedStatus == 'All',
                          onTap: () {
                            setState(() {
                              selectedStatus = 'All';
                            });
                            Get.back();
                          },
                          color: Colors.blue,
                        ),
                        SizedBox(height: 12),
                        _buildFilterOption(
                          context: context,
                          title: global.lang.value == "fr"
                              ? 'Soumis'
                              : 'Submitted',
                          icon: Icons.schedule,
                          isSelected: selectedStatus == 'Submitted',
                          onTap: () {
                            setState(() {
                              selectedStatus = 'Submitted';
                            });
                            Get.back();
                          },
                          color: const Color.fromARGB(255, 224, 214, 29),
                        ),
                        SizedBox(height: 12),
                        _buildFilterOption(
                          context: context,
                          title: global.lang.value == "fr"
                              ? 'Approuvé'
                              : 'Approved',
                          icon: Icons.check_circle,
                          isSelected: selectedStatus == 'Approved',
                          onTap: () {
                            setState(() {
                              selectedStatus = 'Approved';
                            });
                            Get.back();
                          },
                          color: Colors.green,
                        ),
                        SizedBox(height: 12),
                        _buildFilterOption(
                          context: context,
                          title:
                              global.lang.value == "fr" ? 'Rejeté' : 'Rejected',
                          icon: Icons.cancel,
                          isSelected: selectedStatus == 'Rejected',
                          onTap: () {
                            setState(() {
                              selectedStatus = 'Rejected';
                            });
                            Get.back();
                          },
                          color: Colors.red,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  Widget _buildFilterOption({
    required BuildContext context,
    required String title,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withOpacity(0.2),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ]
              : [],
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: isSelected ? color : Colors.grey[400],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  color: isSelected ? color : Colors.grey[700],
                ),
              ),
            ),
            if (isSelected)
              Container(
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? statusName) {
    switch (statusName) {
      case 'Submitted':
        return const Color.fromARGB(255, 224, 214, 29);
      case 'Rejected':
        return Colors.red;
      case 'Approved':
        return Colors.green;
      default:
        return Colors.black;
    }
  }

  String _getTranslatedStatus(String? statusName) {
    GlobalController global = Get.find();
    switch (statusName) {
      case 'Submitted':
        return global.lang.value == "fr" ? 'Soumis' : 'Submitted';
      case 'Rejected':
        return global.lang.value == "fr" ? 'Rejeté' : 'Rejected';
      case 'Approved':
        return global.lang.value == "fr" ? 'Approuvé' : 'Approved';
      default:
        return global.lang.value == "fr" ? 'Inconnu' : 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildDetailCard({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: MyColors.MainRedSecond.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: MyColors.MainRedSecond,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[600],
                    letterSpacing: 0.5,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: valueColor ?? Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDetailsDialog(BuildContext context, RemoteWorkRequest request) {
    showDialog(
      context: context,
      builder: (context) {
        return Obx(() {
          GlobalController global = Get.find();
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Container(
              padding: EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                color: MyColors.MainRedSecond.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.info_outline,
                    color: MyColors.MainRedSecond,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text(
                    global.lang.value == "fr"
                        ? 'Détails de la demande'
                        : 'Request Details',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: MyColors.MainRedSecond,
                    ),
                  ),
                ],
              ),
            ),
            content: SingleChildScrollView(
              child: SizedBox(
                width: double.maxFinite,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailCard(
                      icon: Icons.access_time,
                      label: global.lang.value == "fr" ? 'Durée' : 'Duration',
                      value:
                          '${request.quantity.toInt()} ${global.lang.value == "fr" ? "jours" : "days"}',
                    ),
                    _buildDetailCard(
                      icon: Icons.calendar_today,
                      label: global.lang.value == "fr"
                          ? 'Date de début'
                          : 'Start Date',
                      value: _formatDate(request.dateStart),
                    ),
                    _buildDetailCard(
                      icon: Icons.info,
                      label: global.lang.value == "fr" ? 'Statut' : 'Status',
                      value: _getTranslatedStatus(
                          controller.getStatusName(request.statusId)),
                      valueColor: _getStatusColor(
                          controller.getStatusName(request.statusId)),
                    ),
                    _buildDetailCard(
                      icon: Icons.person,
                      label: global.lang.value == "fr"
                          ? 'Manager assigné'
                          : 'Assigned Manager',
                      value: controller
                          .getManagerName(request.assignedToManagerId ?? 0),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ButtonWithIcon(
                  text: global.lang.value == "fr" ? 'Fermer' : 'Close',
                  mainColor: MyColors.MainRedSecond,
                  textcolor: Colors.white,
                  onPressed: () => Get.back(),
                  height: 45,
                  width: double.infinity,
                  fontSize: 16,
                ),
              ),
            ],
          );
        });
      },
    );
  }

  void _deleteRequest(int? requestId) {
    if (requestId != null) {
      controller.deleteRemoteWorkRequest(requestId);
    }
  }

  void _showEditDialog(BuildContext context, RemoteWorkRequest request) {
    final formKey = GlobalKey<FormState>();
    final TextEditingController quantityController =
        TextEditingController(text: request.quantity.toInt().toString());
    DateTime startDate = request.dateStart;
    int? selectedManager = request.assignedToManagerId;
    showDialog(
      context: context,
      builder: (context) {
        return Obx(() {
          GlobalController global = Get.find();
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.edit,
                  color: MyColors.MainRedSecond,
                  size: 24,
                ),
                SizedBox(width: 10),
                Text(
                  global.lang.value == "fr"
                      ? 'Modifier la demande'
                      : 'Edit Request',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: MyColors.MainRedSecond,
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20),
                      TextFormField(
                        controller: quantityController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: global.lang.value == "fr"
                              ? 'Durée (jours)'
                              : 'Duration (days)',
                          prefixIcon: Icon(Icons.access_time,
                              color: MyColors.MainRedSecond),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide: BorderSide(
                                color: MyColors.MainRedSecond, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.grey[50],
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return global.lang.value == "fr"
                                ? 'Veuillez entrer la quantité'
                                : 'Please enter the quantity';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16),
                      Obx(() {
                        if (controller.isLoading.value) {
                          return Center(child: CircularProgressIndicator());
                        }
                        if (!controller.managers
                            .any((manager) => manager.id == selectedManager)) {
                          selectedManager = null;
                        }
                        return DropdownButtonFormField<int>(
                          value: selectedManager,
                          items: controller.managers.map((manager) {
                            return DropdownMenuItem<int>(
                              value: manager.id,
                              child: Text(
                                  '${manager.firstName} ${manager.lastName}'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            selectedManager = value;
                          },
                          decoration: InputDecoration(
                            labelText: global.lang.value == "fr"
                                ? 'Manager assigné'
                                : 'Assigned Manager',
                            prefixIcon: Icon(Icons.person,
                                color: MyColors.MainRedSecond),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(15),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(15),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(15),
                              borderSide: BorderSide(
                                  color: MyColors.MainRedSecond, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.grey[50],
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                        );
                      }),
                      SizedBox(height: 20),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(15),
                          color: Colors.grey[50],
                        ),
                        child: ListTile(
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          leading: Icon(Icons.calendar_today,
                              color: MyColors.MainRedSecond),
                          title: Text(
                            global.lang.value == "fr"
                                ? 'Date de début'
                                : 'Start Date',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                          subtitle: Text(
                            _formatDate(startDate),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          trailing: Icon(Icons.arrow_drop_down,
                              color: MyColors.MainRedSecond),
                          onTap: () async {
                            DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: startDate,
                              firstDate: DateTime(2000),
                              lastDate: DateTime(2101),
                              builder: (context, child) {
                                return Theme(
                                  data: Theme.of(context).copyWith(
                                    colorScheme: ColorScheme.light(
                                      primary: MyColors.MainRedSecond,
                                      onPrimary: Colors.white,
                                      surface: Colors.white,
                                      onSurface: Colors.black,
                                    ),
                                  ),
                                  child: child!,
                                );
                              },
                            );
                            if (picked != null && picked != startDate) {
                              startDate = picked;
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SizedBox(
                    width: 100,
                    height: 40,
                    child: ElevatedButton(
                      onPressed: () => Get.back(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[400],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        elevation: 2,
                      ),
                      child: Text(
                        global.lang.value == "fr" ? 'Annuler' : 'Cancel',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 100,
                    height: 40,
                    child: ElevatedButton(
                      onPressed: () {
                        if (formKey.currentState!.validate()) {
                          RemoteWorkRequest updatedRequest = RemoteWorkRequest(
                            id: request.id,
                            requestorId: request.requestorId,
                            assignedToManagerId: selectedManager,
                            quantity: double.parse(quantityController.text),
                            dateStart: startDate,
                            statusId: request.statusId,
                            isDeleted: request.isDeleted,
                            createdDate: request.createdDate,
                            updatedDate: DateTime.now(),
                          );
                          controller.updateRemoteWorkRequest(updatedRequest);
                          controller.fetchRemoteWorkRequests();
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: MyColors.MainRedSecond,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        elevation: 3,
                      ),
                      child: Text(
                        global.lang.value == "fr" ? 'Confirmer' : 'Confirm',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
        });
      },
    );
  }
}
