using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

using TMT.Mobile.Core;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Core.Entities
{
  public class Currency : IEntity
  {
    public Currency()
    {
    //   Bills = new HashSet<Bill>();
    //   Payments = new HashSet<Payment>();
    //   SupplierPayments = new HashSet<SupplierPayment>();
    //   Projectbills = new HashSet<Projectbills>();
    //   Fees = new HashSet<Projectfees>();
    //   ExchangeRates = new HashSet<CurrencyExchangeRate>();
    //   ProjectUserRates = new HashSet<ProjectUserRate>();
    //   Projectorders = new HashSet<Projectorders>();
    //   ExpenseRequests = new HashSet<ExpenseRequest>();
    }
    public int Id { get; set; }
    public string? Symbol { get; set; }
    public string? Name { get; set; }
    public string? IsoCode { get; set; }

    // public virtual ICollection<Bill> Bills { get; set; }
    // public virtual ICollection<Payment> Payments { get; set; }
    // public virtual ICollection<SupplierPayment> SupplierPayments { get; set; }
    // public virtual ICollection<Projectbills> Projectbills { get; set;}
    // public virtual ICollection<Projectfees> Fees { get; set; }
    // public virtual ICollection<CurrencyExchangeRate> ExchangeRates { get; set; }
    // public virtual ICollection<ProjectUserRate> ProjectUserRates { get; set; }
    // public virtual ICollection<Projectorders> Projectorders { get; set; }
    // public virtual ICollection<ExpenseRequest> ExpenseRequests { get; set; }
  }
}