variables:
- name: keyVaultName
  value: tmt-core-qa-akv-01
- name: wap.settings
  value: >
    [{"name": "ConnectionStrings__TMTConnection",
      "value": "$(CoreConnectionStrings)",
      "slotSetting": false},
     {"name": "GoogleIdentity__ClientID",
      "value": "$(GoogleIdentityClientID)",
      "slotSetting": false},
     {"name": "GoogleIdentity__ClientSecret",
      "value": "$(GoogleIdentityClientSecret)",
      "slotSetting": false},
     {"name": "MicrosoftIdentity__Instance",
      "value": "https://login.microsoftonline.com/",
      "slotSetting": false},
     {"name": "MicrosoftIdentity__Domain",
      "value": "tmtrack.net/",
      "slotSetting": false},
     {"name": "MicrosoftIdentity__ClientId",
      "value": "$(MicrosoftIdentityClientId)",
      "slotSetting": false},
     {"name": "MicrosoftIdentity__ClientSecret",
      "value": "$(MicrosoftIdentityClientSecret)",
      "slotSetting": false},
     {"name": "MicrosoftIdentity__TenantId",
      "value": "common",
      "slotSetting": false},
     {"name": "MicrosoftIdentity__CallbackPath",
      "value": "/signin-oidc",
      "slotSetting": false},
     {"name": "AzureCommunicationService__EmailServiceConnectionString",
      "value": "$(AzureCommunicationServiceEmailServiceConnectionString)",
      "slotSetting": false},
     {"name": "AzureCommunicationService__EmailServiceSender",
      "value": "<EMAIL>",
      "slotSetting": false},
     {"name": "Release__Branch",
      "value": "$(Build.SourceBranch)",
      "slotSetting": false},
     {"name": "AzureCoreSTAInfo__Name",
      "value": "$(AzureCoreSTAInfoName)",
      "slotSetting": false},
     {"name": "AzureCoreSTAInfo__Key",
      "value": "$(AzureCoreSTAInfoKey)",
      "slotSetting": false},
     {"name": "AzureSTAInfo__Name",
      "value": "$(AzureSTAInfoName)",
      "slotSetting": false},
     {"name": "AzureSTAInfo__Key",
      "value": "$(AzureSTAInfoKey)",
      "slotSetting": false},
     {"name": "ServiceBus__ConnectionString",
      "value": "$(ServiceBusConnectionString)",
      "slotSetting": false}]