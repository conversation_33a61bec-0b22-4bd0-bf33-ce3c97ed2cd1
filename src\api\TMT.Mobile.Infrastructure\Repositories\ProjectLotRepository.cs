﻿
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
namespace TMT.Mobile.Infrastructure.Repositories
{
  public class ProjectLotRepository : CoreRepository<Projectlots, DynamicDbContext>
  {

    public ProjectLotRepository(DynamicDbContext context) : base(context)
    {
    }

    public async Task<Projectlots>? GetProjectLot(int id)
    {
      try
      {
        var projectlot = await context.Projectlots.Where(p => p.Id == id).SingleAsync();
        return projectlot!;
      }
      catch (Exception ex)
      {
        throw ex;
      }
    }
  }
}


