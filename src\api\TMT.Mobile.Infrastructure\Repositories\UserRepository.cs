﻿using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Infrastructure.Repositories
{
  public class UserRepository : CoreRepository<User, TmtMobileContext>
  {
    private static Random random = new Random();
    public UserRepository(TmtMobileContext context) : base(context)
    {
    }

    public async Task<User>? GetUserByEmail(string email)
    {
      try
      {
        var user = await context.Users.Where(p => p.Email == email).SingleOrDefaultAsync();
        return user!;
      }
      catch (Exception ex)
      {
        throw ex;
      }
    }
    public async Task<OrgRoles> GetUserRole(int id, Guid org)
    {
      var result = await (from o in context.UserOrganizations
                          where o.IdUser == id
                          && o.Organization.GUID == org
                          && o.IsDeleted == false
                          select o).SingleOrDefaultAsync();
      if (result == null)
        return OrgRoles.None;
      return result.Role;
    }
    public async Task<bool> IsSuperUser(int iduser, Guid org)
    {
      var result = await (from o in context.UserOrganizations
                          where o.IdUser == iduser
                          && o.IsDeleted == false
                          && o.Organization.GUID == org
                          select o).SingleOrDefaultAsync();
      if (result == null)
        return false;
      return result.IsSuperUser;
    }
    public async Task<UserOrganizations> GetUserOrganizationAsync(string login, Guid orgGuid)
    {
      var query = from r in context.UserOrganizations
                  where r.Organization.GUID == orgGuid
                  && r.User.Login == login
                  && r.IsDeleted == false
                  select r;
      try
      {
        var result = await query.SingleOrDefaultAsync();
        return result;
      }
      catch (Exception ex)
      {
        var m = ex.Message;
        throw ex;
      }

    }

    public async Task<bool> IsOwner(int iduser, Guid org)
    {
      var result = await (from o in context.Organizations
                          where o.GUID == org
                          && o.IsDeleted == false
                          select o).SingleOrDefaultAsync();
      if (result == null)
        return false;
      return result.IdOwner == iduser;
    }
    public async Task<bool> IsAdmin(int iduser, Guid org)
    {
      var result = await (from o in context.UserOrganizations
                          where o.IdUser == iduser
                          && o.IsDeleted == false
                          && o.Organization.GUID == org
                          select o).SingleOrDefaultAsync();
      if (result == null)
        return false;
      return result.IsAdmin;
    }
    public User UpdateRegistrationCode(string email, string registrationCode)
    {
      var user = context.Users.Where(p => p.Email == email).SingleOrDefault();
      context.Users.Attach(user!);
      user!.AccountValidationNumber = registrationCode;
      user.AccountValidationRequestedDate = DateTime.Now.AddDays(2);
      context.Entry<User>(user).State = EntityState.Modified;
      context.SaveChanges();
      return user;
    }
    public User UpdateResetPasswordCode(string email, string resetCode)
    {
      var user = context.Users.Where(p => p.Email == email).SingleOrDefault();
      context.Users.Attach(user!);
      user!.ResetPasswordNumber = resetCode;
      user!.ResetPasswordValidated = false;
      user!.ResetPasswordRequestedDate = DateTime.Now.AddDays(2);
      context.Entry<User>(user).State = EntityState.Modified;
      context.SaveChanges();
      return user;
    }

    public User ValidateRegistry(string email)
    {
      var user = context.Users.Where(p => p.Email == email).SingleOrDefault();
      context.Users.Attach(user!);
      user!.AccountValidationDate = DateTime.Now;

      user.AccountValidated = true;
      context.Entry<User>(user).State = EntityState.Modified;
      context.SaveChanges();
      return user;
    }

    //public static string RandomString(int length)
    //{
    //  const string chars = "0123456789abcdef";
    //  return new string(Enumerable.Repeat(chars, length)
    //    .Select(s => s[random.Next(s.Length)]).ToArray());
    //}
  }
}
