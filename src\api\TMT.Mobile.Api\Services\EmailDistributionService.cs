using System;
using System.Net.Mail;
using System.Threading.Tasks;
using Azure;
using Azure.Communication.Email;
using Microsoft.Extensions.Configuration;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Core.Entities;
using Microsoft.EntityFrameworkCore;

namespace TMT.Mobile.Api.Services
{
    public class EmailDistributionService : IEmailService
    {
        private readonly string _senderAddress;
        private readonly EmailClient _emailClient;
        private readonly DynamicDbContext _Context;

        public EmailDistributionService(IConfiguration configuration, DynamicDbContext Context)
        {
            string connectionString = configuration["AzureCommunicationService:EmailServiceConnectionString"];
            _senderAddress = configuration["AzureCommunicationService:EmailServiceSender"];
            _emailClient = new EmailClient(connectionString);
            _Context = Context;
        }

        public async Task SendEmailAsync(string recipientEmail, string subject, string body)
        {
            await SendAsync(recipientEmail, subject, body);
        }

        public async Task<EmailSendOperation> SendAsync(string recipientEmail, string subject, string body)
        {
            EmailSendOperation emailSendOperation = await _emailClient.SendAsync(
                WaitUntil.Started,
                senderAddress: _senderAddress,
                recipientAddress: recipientEmail,
                subject: subject,
                htmlContent: body
            );

            string status = "Succeeded";
            if ((await emailSendOperation.UpdateStatusAsync()).IsError)
            {
                status = "Failed";
            }

            await SaveLogs(recipientEmail, subject, body, string.Empty, status);
            return emailSendOperation;
        }

        private async Task SaveLogs(string recipientEmail, string subject, string body, string? cc = null, string status = "Succeeded")
        {
            EmailsLog log = new EmailsLog
            {
                Sender = "Hello TM/T",
                SenderEmail = "<EMAIL>",
                Recipients = recipientEmail,
                Subject = subject,
                Body = body,
                CarbonCopy = cc,
                Status = status
            };
            _Context.EmailLogs.Add(log);
            await _Context.SaveChangesAsync();
        }
    }
}
