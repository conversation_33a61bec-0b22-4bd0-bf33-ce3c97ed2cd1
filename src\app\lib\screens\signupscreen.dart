import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:email_validator/email_validator.dart';

import 'package:flutter/material.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/signupcontroller.dart';
import 'package:tmt_mobile/models/userdata.dart';
import 'package:tmt_mobile/screens/codeValidationScreen.dart';
import 'package:tmt_mobile/screens/signinscreen.dart';
import 'package:tmt_mobile/widgets/big_text.dart';
import 'package:tmt_mobile/widgets/inputfield.dart';

import '../utils/myColors.dart';
import '../widgets/buttonwithicon.dart';

class SignupScreen extends GetView<SignUpController> {
  const SignupScreen({super.key});

  validateForm() async {}

  @override
  Widget build(BuildContext context) {
    double screenWidht = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    GlobalController globalController = Get.find<GlobalController>();
    Future signup() async {
      print(controller.email.value.text);
      bool isValidate = controller.SignUpKey.currentState!.validate();

      if (isValidate) {
        var user = userRegistration(
            email: controller.email.value.text,
            firstname: controller.nom.value.text,
            lastname: controller.prenom.value.text,
            password: controller.password.value.text);
        var checkReg = await controller.onSubmit(user);
        if (checkReg == true) {
          Get.offAll(CodeValidationScreen());
        }
      }
    }

    Get.put(SignUpController(globalController));
    return Scaffold(
      backgroundColor: Color(0xffffffff),
      body: globalController.devType.value == "tablet"
          ? signupscreenTablet(
              screenHeight, screenWidht, globalController, signup)
          : signupscreenAndroid(
              screenWidht, screenHeight, globalController, signup),
    );
  }

  Widget signupscreenAndroid(double screenWidht, double screenHeight,
      GlobalController globalController, Future<dynamic> Function() signup) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[50]!,
            Colors.white,
            Colors.grey[50]!,
          ],
          stops: [0.0, 0.5, 1.0],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: screenWidht * .06),
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            child: Obx(
              () => Form(
                key: controller.SignUpKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Header Section

                    // Input Fields Container
                    Container(
                      padding: EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(0, 255, 255, 255),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Column(
                        children: [
                          // Personal Information Section
                          Row(
                            children: [
                              Icon(
                                Icons.person_outline,
                                color: MyColors.MainRedBig,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                globalController.lang.value == "fr"
                                    ? "Informations personnelles"
                                    : "Personal Information",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: MyColors.mainblack,
                                  fontFamily: "aileron",
                                ),
                              ),
                            ],
                          ),

                          SizedBox(height: 16),

                          // First Name Input
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Myinput(
                              labelText: globalController.lang.value == "fr"
                                  ? "Nom"
                                  : "First Name",
                              controller: controller.nom.value,
                              validate: (v) => controller.validateThese(v!),
                              icon: Icons.person_outline,
                              fontsize: 16,
                            ),
                          ),

                          SizedBox(height: 16),

                          // Last Name Input
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Myinput(
                              labelText: globalController.lang.value == "fr"
                                  ? "Prénom"
                                  : "Last Name",
                              controller: controller.prenom.value,
                              validate: (v) => controller.validateThese(v!),
                              icon: Icons.person_outline,
                              fontsize: 16,
                            ),
                          ),

                          SizedBox(height: 20),

                          // Account Information Section
                          Row(
                            children: [
                              Icon(
                                Icons.email_outlined,
                                color: MyColors.MainRedBig,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                globalController.lang.value == "fr"
                                    ? "Informations du compte"
                                    : "Account Information",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: MyColors.mainblack,
                                  fontFamily: "aileron",
                                ),
                              ),
                            ],
                          ),

                          SizedBox(height: 16),

                          // Email Input
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Myinput(
                              labelText: "Email",
                              controller: controller.email.value,
                              validate: (v) =>
                                  v != null && !EmailValidator.validate(v)
                                      ? globalController.lang.value == "fr"
                                          ? 'Entrer un email valide'
                                          : 'Enter a valid email'
                                      : null,
                              icon: Icons.email_outlined,
                              fontsize: 16,
                            ),
                          ),

                          SizedBox(height: 16),

                          // Password Input
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Myinput(
                              labelText: globalController.lang.value == "fr"
                                  ? "Mot de passe"
                                  : "Password",
                              controller: controller.password.value,
                              validate: (v) =>
                                  controller.validatePasswordlen(v!),
                              obscureText: controller.passwtoggl.value,
                              icon: Icons.lock_outline,
                              Suffixicon: Icons.visibility_outlined,
                              Suffixiconoff: Icons.visibility_off_outlined,
                              suffixiconfun: () {
                                controller.passwtoggl.value =
                                    !controller.passwtoggl.value;
                              },
                              fontsize: 16,
                            ),
                          ),

                          SizedBox(height: 16),

                          // Confirm Password Input
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Myinput(
                              obscureText: controller.passwtogg2.value,
                              labelText: globalController.lang.value == "fr"
                                  ? "Confirmer le mot de passe"
                                  : "Confirm Password",
                              controller: controller.confirmpaswword.value,
                              validate: (v) => controller.validatePassword(
                                  controller.password.value.text, v!),
                              icon: Icons.lock_outline,
                              Suffixicon: Icons.visibility_outlined,
                              Suffixiconoff: Icons.visibility_off_outlined,
                              suffixiconfun: () {
                                controller.passwtogg2.value =
                                    !controller.passwtogg2.value;
                              },
                              fontsize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.0001),

                    // Error Message
                    Visibility(
                      visible: controller.showError.value,
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16),
                        margin: EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.red[200]!, width: 1),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: MyColors.MainRedBig,
                              size: 24,
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                controller.errormsg.value,
                                style: TextStyle(
                                  color: MyColors.MainRedBig,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: "aileron",
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Sign Up Button
                    controller.loading.value == false
                        ? Container(
                            width: double.infinity,
                            height: 56,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: LinearGradient(
                                colors: [
                                  MyColors.MainRedBig,
                                  MyColors.MainRedSecond
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                            ),
                            child: ElevatedButton(
                              onPressed: () async {
                                await signup();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                              child: Text(
                                globalController.lang.value == "fr"
                                    ? "S'inscrire"
                                    : "Sign Up",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: "aileron",
                                ),
                              ),
                            ),
                          )
                        : SizedBox(
                            height: 56,
                            child: Center(
                              child: CircularProgressIndicator(
                                color: MyColors.MainRedBig,
                                strokeWidth: 3,
                              ),
                            ),
                          ),

                    SizedBox(height: screenHeight * 0.025),

                    // Divider with "or"
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 1,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.transparent,
                                  MyColors.Strokecolor.withOpacity(0.3),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            globalController.lang.value == "fr" ? "ou" : "or",
                            style: TextStyle(
                              fontSize: 16,
                              color: MyColors.Strokecolor,
                              fontWeight: FontWeight.w500,
                              fontFamily: "aileron",
                            ),
                          ),
                        ),
                        Expanded(
                          child: Container(
                            height: 1,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  MyColors.Strokecolor.withOpacity(0.3),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: screenHeight * 0.025),

                    // Sign In Link
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: MyColors.thirdColor.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: MyColors.thirdColor.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            globalController.lang.value == "fr"
                                ? "Vous avez déjà un compte ?"
                                : "Already have an account?",
                            style: TextStyle(
                              color: MyColors.Strokecolor,
                              fontSize: 16,
                              fontFamily: "aileron",
                            ),
                          ),
                          SizedBox(height: 8),
                          TextButton(
                            onPressed: () {
                              Get.offAll(SignInScreen());
                            },
                            style: TextButton.styleFrom(
                              backgroundColor:
                                  MyColors.thirdColor.withOpacity(0.1),
                              padding: EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 24),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              globalController.lang.value == "fr"
                                  ? "Se connecter"
                                  : "Sign In",
                              style: TextStyle(
                                color: MyColors.thirdColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                fontFamily: "aileron",
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.04),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Center signupscreenTablet(double screenHeight, double screenWidht,
      GlobalController globalController, Future<dynamic> Function() signup) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(30),
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: SizedBox(
              height: screenHeight * 0.8,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      width: screenWidht * 0.4,
                      decoration: BoxDecoration(
                          color: Color(0xffffffff),
                          borderRadius: BorderRadius.circular(10)),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: screenHeight * 0.06,
                          ),
                          SvgPicture.asset(
                            "assets/logotmt.svg",
                            fit: BoxFit.cover,
                            width: screenWidht * 0.30,
                          ),
                          SizedBox(
                            height: screenHeight * 0.03,
                          ),
                          BigText(
                            text: "TM/T Software ",
                            color: Colors.black,
                            size: 40,
                          ),
                          SizedBox(
                            height: screenHeight * 0.01,
                          ),
                          Center(
                            child: Text(
                              "PILOTEZ VOS PROJETS AVEC LES BONS INDICATEURS  ",
                              style: TextStyle(fontSize: 17),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(width: screenWidht * 0.03),
                    VerticalDivider(
                      width: 7,
                      thickness: 2,
                      color: MyColors.Strokecolor.withOpacity(0.3),
                      indent: 10, //spacing at the start of divider
                      endIndent: 10,
                    ),
                    SizedBox(width: screenWidht * 0.08),
                    Obx(
                      () => Form(
                        key: controller.SignUpKey,
                        child: SizedBox(
                          width: screenWidht * 0.35,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: screenHeight * 0.01,
                              ),
                              BigText(
                                text: globalController.lang.value == "fr"
                                    ? "créer un compte"
                                    : "create an account",
                                color: Colors.black,
                                textAlign: TextAlign.left,
                                size: screenHeight * 0.038,
                              ),
                              SizedBox(
                                height: screenHeight * 0.02,
                              ),
                              SizedBox(
                                  width: screenWidht * 0.90,
                                  child: Myinput(
                                    labelText:
                                        globalController.lang.value == "fr"
                                            ? "Nom"
                                            : "First Name",
                                    controller: controller.nom.value,
                                    validate: (v) =>
                                        v == "" ? 'nom est obligatoire' : null,
                                    icon: Icons.person,
                                  )),
                              SizedBox(
                                height: screenWidht * 0.01,
                              ),
                              SizedBox(
                                  width: screenWidht * 0.90,
                                  child: Myinput(
                                    labelText:
                                        globalController.lang.value == "fr"
                                            ? "Prénom"
                                            : "Last Name",
                                    controller: controller.prenom.value,
                                    validate: (v) => v == ""
                                        ? 'prénom est obligatoire'
                                        : null,
                                    icon: Icons.person,
                                  )),
                              SizedBox(
                                height: screenWidht * 0.01,
                              ),
                              SizedBox(
                                  width: screenWidht * 0.90,
                                  child: Myinput(
                                    labelText: "Email",
                                    controller: controller.email.value,
                                    validate: (v) =>
                                        v != null && !EmailValidator.validate(v)
                                            ? 'Enter a valid email'
                                            : null,
                                    icon: Icons.mail,
                                  )),
                              SizedBox(
                                height: screenWidht * 0.01,
                              ),
                              SizedBox(
                                  width: screenWidht * 0.90,
                                  child: Myinput(
                                    labelText:
                                        globalController.lang.value == "fr"
                                            ? "mot de passe"
                                            : "Password",
                                    controller: controller.password.value,
                                    validate: (v) =>
                                        controller.validatePasswordlen(v!),
                                    obscureText: controller.passwtoggl.value,
                                    icon: Icons.lock,
                                    Suffixicon: Icons.visibility,
                                    Suffixiconoff: Icons.visibility_off,
                                    suffixiconfun: () {
                                      controller.passwtoggl.value =
                                          !controller.passwtoggl.value;
                                    },
                                  )),
                              SizedBox(
                                height: screenWidht * 0.01,
                              ),
                              SizedBox(
                                  width: screenWidht * 0.90,
                                  child: Myinput(
                                    obscureText: controller.passwtogg2.value,
                                    labelText:
                                        globalController.lang.value == "fr"
                                            ? "confirmer le mot de passe"
                                            : "Confirm password",
                                    controller:
                                        controller.confirmpaswword.value,
                                    validate: (v) =>
                                        controller.validatePassword(
                                            controller.password.value.text, v!),
                                    icon: Icons.lock,
                                    Suffixicon: Icons.visibility,
                                    Suffixiconoff: Icons.visibility_off,
                                    suffixiconfun: () {
                                      controller.passwtogg2.value =
                                          !controller.passwtogg2.value;
                                    },
                                  )),
                              SizedBox(
                                height: screenWidht * 0.01,
                              ),
                              controller.loading.value == false
                                  ? ButtonWithIcon(
                                      onPressed: () async => await signup(),
                                      text: globalController.lang.value == "fr"
                                          ? "S'inscrire"
                                          : "Sign in",
                                      mainColor: MyColors.MainRedBig,
                                      fontSize: 18,
                                      textcolor: Colors.white,
                                      height: screenHeight * 0.065,
                                    )
                                  : Center(
                                      child: CircularProgressIndicator(
                                          color: MyColors.thirdColor),
                                    ),
                              SizedBox(
                                height: screenHeight * 0.01,
                              ),
                              Row(children: <Widget>[
                                Expanded(
                                    child: Divider(
                                  color: MyColors.Strokecolor.withOpacity(
                                      0.3), //color of divider
                                  height: 5, //height spacing of divider
                                  thickness: 2, //thickness of divier line
                                  indent: 25, //spacing at the start of divider
                                  endIndent: 25, //spacing at the end of divider
                                )),
                                Text(
                                  globalController.lang.value == "fr"
                                      ? "ou"
                                      : "or",
                                  style: TextStyle(fontSize: 18),
                                ),
                                Expanded(
                                    child: Divider(
                                  color: MyColors.Strokecolor.withOpacity(
                                      0.3), //color of divider
                                  height: 5, //height spacing of divider
                                  thickness: 2, //thickness of divier line
                                  indent: 25, //spacing at the start of divider
                                  endIndent: 25, //spacing at the end of divider
                                )),
                              ]),
                              Center(
                                child: TextButton(
                                    onPressed: () {
                                      Get.offAll(SignInScreen());
                                    },
                                    child: RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16,
                                            ),
                                            children: [
                                              TextSpan(
                                                text: globalController
                                                            .lang.value ==
                                                        "fr"
                                                    ? "Vous avez déjà un compte ?"
                                                    : "Already have an account ?",
                                                style: TextStyle(
                                                    color:
                                                        MyColors.BordersGrey),
                                              ),
                                              TextSpan(
                                                  text: globalController
                                                              .lang.value ==
                                                          "fr"
                                                      ? ' Connectez-vous.'
                                                      : "Sign in",
                                                  style: TextStyle(
                                                      color:
                                                          MyColors.thirdColor)),
                                            ]))),
                              )
                            ],
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )),
        ),
      ),
    );
  }
}
