using Microsoft.EntityFrameworkCore;

using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class LeaveRequestRepository : CoreRepository<LeaveRequest, DynamicDbContext>
    {

        public LeaveRequestRepository(DynamicDbContext context) : base(context)
        {

        }

        public async Task<IEnumerable<LeaveRequest>> GetAllAsync()
        {
            return await context.LeaveRequests.ToListAsync();
        }

        public async Task<LeaveRequest?> GetByIdAsync(int id)
        {
            return await context.LeaveRequests.FindAsync(id);
        }

        public async Task AddAsync(LeaveRequest leaveRequest)
        {
            await context.LeaveRequests.AddAsync(leaveRequest);
            await context.SaveChangesAsync();
        }

        public async Task UpdateAsync(LeaveRequest leaveRequest)
        {
            context.LeaveRequests.Update(leaveRequest);
            await context.SaveChangesAsync();
        }

        public async Task DeleteAsync(LeaveRequest leaveRequest)
        {
            context.LeaveRequests.Remove(leaveRequest);
            await context.SaveChangesAsync();
        }

        public async Task<List<LeaveRequest>> GetByUserIdAsync(int userId)
        {
            return await context.LeaveRequests
                .Where(lr => lr.Iduser == userId && !lr.Isdeleted)
                .ToListAsync();
        }

        public async Task<List<LeaveRequest>> GetAssignedToUserAsync(int userId)
        {
            return await context.LeaveRequests
                .Where(lr => !lr.Isdeleted && 
                            (lr.Idassignedtomanager == userId || lr.Idassignedtohr == userId))
                .ToListAsync();
        }


    }
}