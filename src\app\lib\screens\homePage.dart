import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/controllers/homepagecontroller.dart';
import 'package:tmt_mobile/models/project.dart';
import 'package:tmt_mobile/utils/myColors.dart';
import 'package:tmt_mobile/widgets/big_text.dart';
import 'package:tmt_mobile/widgets/buttonwithicon.dart';
import 'package:tmt_mobile/widgets/dropdown.dart';
import 'package:tmt_mobile/widgets/inputfield.dart';
import 'package:toggle_switch/toggle_switch.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    Get.put(HomePageController());
    GlobalController globalController = Get.find<GlobalController>();
    HomePageController homeController = Get.find<HomePageController>();

    void onAddQuantity() {
      var stringQuantity = homeController.quantiteController.value.text;
      var numericQuantity = double.parse(stringQuantity) + 1;
      homeController.quantiteController.value.text = numericQuantity.toString();
    }

    Future addImput() async {
      var response = await homeController.context.addTimesheet(
          homeController.projetController.value.id,
          homeController.facetController.value.id,
          homeController.tacheController.value.id,
          DateTime.parse(homeController.birthdate.value.text),
          double.parse(homeController.quantiteController.value.text));
      if (response.statusCode == 200) {
        print("Timesheet added successfully");
        await homeController
            .getTimesheets(DateTime.parse(homeController.birthdate.value.text));
      } else {
        print("Error: ${response.statusCode}");
        print("Response: ${response.body}");
      }
    }

    Future addTimesheet() async {
      bool isValidate = homeController.homeKey.currentState!.validate();
      if (isValidate) {
        await addImput();
        Get.snackbar(
          '',
          '',
          titleText: BigText(
            text: globalController.lang.value == "fr" ? "Succès" : "Success",
            size: 18,
            color: Colors.green,
          ),
          messageText: Text(
            globalController.lang.value == "fr"
                ? " Feuille de temps ajoutée avec succès"
                : "Timesheet Added Successfully!",
            style: TextStyle(
              fontSize: 17,
            ),
          ),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: MyColors.BordersGrey.withOpacity(0.4),
          duration: const Duration(seconds: 1),
          overlayBlur: 0.7,
        );
      }
    }

    Future addDay() async {
      print(homeController.birthdate.value.text);
      DateTime dt1 = DateTime.parse(homeController.birthdate.value.text);
      DateTime dt2 = dt1.add(Duration(days: 1));
      homeController.birthdate.value.text =
          DateFormat("yyyy-MM-dd").format(dt2);
      homeController.birthdate.refresh(); // Force reactive update
      await homeController
          .getTimesheets(DateTime.parse(homeController.birthdate.value.text));
    }

    Future subtractDay() async {
      print(homeController.birthdate.value.text);
      DateTime dt1 = DateTime.parse(homeController.birthdate.value.text);
      DateTime dt2 = dt1.subtract(Duration(days: 1));
      homeController.birthdate.value.text =
          DateFormat("yyyy-MM-dd").format(dt2);
      homeController.birthdate.refresh(); // Force reactive update
      await homeController
          .getTimesheets(DateTime.parse(homeController.birthdate.value.text));
    }

    void onMinusQuantity() {
      var stringQuantity = homeController.quantiteController.value.text;
      var numericQuantity = double.parse(stringQuantity) - 1;
      homeController.quantiteController.value.text = numericQuantity.toString();
    }

    return globalController.devType.value == "tablet"
        ? homePageTablet(
            homeController,
            screenWidth,
            screenHeight,
            subtractDay,
            context,
            addDay,
            onMinusQuantity,
            globalController,
            onAddQuantity,
            addTimesheet)
        : homePageAndroid(
            homeController,
            screenHeight,
            screenWidth,
            subtractDay,
            context,
            addDay,
            onMinusQuantity,
            globalController,
            onAddQuantity,
            addTimesheet);
  }

  Container homePageAndroid(
      HomePageController homeController,
      double screenHeight,
      double screenWidth,
      Future<dynamic> Function() subtractDay,
      BuildContext context,
      Future<dynamic> Function() addDay,
      void Function() onMinusQuantity,
      GlobalController globalController,
      void Function() onAddQuantity,
      Future<dynamic> Function() addTimesheet) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.grey[50]!,
            Colors.white,
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: homeController.homeKey,
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            child: Column(
              children: [
                // Modern Date Selector Card
                Obx(() => Container(
                      margin: EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            blurRadius: 10,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 12.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: MyColors.MainRedBig.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: IconButton(
                                icon: Icon(Icons.chevron_left,
                                    color: MyColors.MainRedBig, size: 28),
                                onPressed: () async {
                                  if (homeController.listviewload.value ==
                                      false) {
                                    await subtractDay();
                                  }
                                },
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: () async {
                                  DateTime? pickdate = await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.parse(
                                          homeController.birthdate.value.text),
                                      firstDate: DateTime(1920),
                                      lastDate: DateTime(2060));
                                  if (pickdate != null) {
                                    homeController.birthdate.value.text =
                                        DateFormat("yyyy-MM-dd")
                                            .format(pickdate);
                                    homeController.birthdate
                                        .refresh(); // Force reactive update
                                    await homeController.getTimesheets(
                                        DateTime.parse(homeController
                                            .birthdate.value.text));
                                  }
                                },
                                child: Container(
                                  margin: EdgeInsets.symmetric(horizontal: 16),
                                  padding: EdgeInsets.symmetric(
                                      vertical: 12, horizontal: 16),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[50],
                                    borderRadius: BorderRadius.circular(12),
                                    border:
                                        Border.all(color: Colors.grey[200]!),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.calendar_today,
                                          color: MyColors.MainRedBig, size: 18),
                                      SizedBox(width: 6),
                                      Flexible(
                                        child: Text(
                                          DateFormat('MMM dd, yyyy').format(
                                              DateTime.parse(homeController
                                                  .birthdate.value.text)),
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black87,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: MyColors.MainRedBig.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: IconButton(
                                icon: Icon(Icons.chevron_right,
                                    color: MyColors.MainRedBig, size: 28),
                                onPressed: () async {
                                  if (homeController.listviewload.value ==
                                      false) {
                                    await addDay();
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    )),
                // Modern Form Card
                Obx(() => Visibility(
                      visible: homeController.toggleHide.isFalse,
                      child: Container(
                        margin: EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              blurRadius: 10,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                globalController.lang.value == "fr"
                                    ? "Nouvelle Entrée"
                                    : "New Entry",
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              SizedBox(height: 20),

                              // Project Dropdown
                              _buildModernDropdownSection(
                                globalController.lang.value == "fr"
                                    ? "Projet"
                                    : "Project",
                                Icons.folder_outlined,
                                MyDropDown(
                                  fontsize: 14,
                                  aligncenter: true,
                                  projectcontroller:
                                      homeController.projetController.value,
                                  icon: Icons.folder_copy,
                                  labelText: globalController.lang.value == "fr"
                                      ? "Sélectionner un projet"
                                      : "Select project",
                                  project: homeController.listproject,
                                  Suffixicon: Icons.arrow_drop_down,
                                  onChangedProject: (Project? p) async {
                                    await homeController.onChangeProject(p!.id);
                                    homeController.projetController.value = p;
                                  },
                                ),
                              ),

                              SizedBox(height: 16),

                              // Lot Dropdown
                              homeController.loadlots.value == false
                                  ? _buildModernDropdownSection(
                                      globalController.lang.value == "fr"
                                          ? "Lot"
                                          : "Lot",
                                      Icons.work_outline,
                                      MyDropDown(
                                        fontsize: 14,
                                        projectlotscontroller: homeController
                                            .facetController.value,
                                        icon: Icons.work,
                                        labelText:
                                            globalController.lang.value == "fr"
                                                ? "Sélectionner un lot"
                                                : "Select lot",
                                        projectLot: homeController.listphase,
                                        Suffixicon: Icons.arrow_drop_down,
                                        onChangedProjectlot: (dd) async {
                                          await homeController
                                              .onChangeProjectlot(dd!.id);
                                          homeController.facetController.value =
                                              dd;
                                        },
                                      ),
                                    )
                                  : _buildLoadingSection(
                                      globalController.lang.value == "fr"
                                          ? "Chargement des lots..."
                                          : "Loading lots..."),

                              SizedBox(height: 16),

                              // Task Dropdown
                              homeController.loadtasks.value == false
                                  ? _buildModernDropdownSection(
                                      globalController.lang.value == "fr"
                                          ? "Tâche"
                                          : "Task",
                                      Icons.task_outlined,
                                      MyDropDown(
                                        projecttaskscontroller: homeController
                                            .tacheController.value,
                                        icon: Icons.task,
                                        labelText:
                                            globalController.lang.value == "fr"
                                                ? "Sélectionner une tâche"
                                                : "Select task",
                                        fontsize: 14,
                                        projecttasks: homeController.listtache,
                                        Suffixicon: Icons.arrow_drop_down,
                                        onChangedProjecttask: (dd) => {
                                          homeController.tacheController.value =
                                              dd!,
                                        },
                                      ),
                                    )
                                  : _buildLoadingSection(
                                      globalController.lang.value == "fr"
                                          ? "Chargement des tâches..."
                                          : "Loading tasks..."),
                            ],
                          ),
                        ),
                      ),
                    )),
                // Modern Quantity Input Card
                Obx(() => Visibility(
                      visible: homeController.toggleHide.isFalse,
                      child: Container(
                        margin: EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              blurRadius: 10,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color:
                                          MyColors.MainRedBig.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(Icons.access_time,
                                        color: MyColors.MainRedBig, size: 20),
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    globalController.lang.value == "fr"
                                        ? "Temps de Travail"
                                        : "Work Time",
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16),

                              // Quantity Input with Modern Design
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.grey[50],
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.grey[200]!),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: MyColors.MainRedBig.withOpacity(
                                            0.1),
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(12),
                                          bottomLeft: Radius.circular(12),
                                        ),
                                      ),
                                      child: IconButton(
                                        onPressed: onMinusQuantity,
                                        icon: Icon(Icons.remove,
                                            color: MyColors.MainRedBig,
                                            size: 24),
                                      ),
                                    ),
                                    Expanded(
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 16),
                                        child: TextFormField(
                                          controller: homeController
                                              .quantiteController.value,
                                          keyboardType: TextInputType.number,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black87,
                                          ),
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            hintText:
                                                globalController.lang.value ==
                                                        "fr"
                                                    ? "Heures"
                                                    : "Hours",
                                            hintStyle: TextStyle(
                                                color: Colors.grey[400]),
                                          ),
                                          validator: (v) =>
                                              homeController.validateThese2(v!),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: MyColors.MainRedBig.withOpacity(
                                            0.1),
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(12),
                                          bottomRight: Radius.circular(12),
                                        ),
                                      ),
                                      child: IconButton(
                                        onPressed: onAddQuantity,
                                        icon: Icon(Icons.add,
                                            color: MyColors.MainRedBig,
                                            size: 24),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              SizedBox(height: 20),

                              // Modern Action Buttons
                              Column(
                                children: [
                                  // Copy Latest Button
                                  SizedBox(
                                    width: double.infinity,
                                    height: 48,
                                    child: ElevatedButton.icon(
                                      onPressed: () async {
                                        await homeController
                                            .copyLasttimesheet();
                                      },
                                      icon: Icon(Icons.copy_outlined, size: 18),
                                      label: Text(
                                        globalController.lang.value == "fr"
                                            ? "Copier Dernière Entrée"
                                            : "Copy Latest Entry",
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: MyColors.thirdColor,
                                        foregroundColor: Colors.white,
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 12),
                                  // Add Button
                                  SizedBox(
                                    width: double.infinity,
                                    height: 48,
                                    child: ElevatedButton.icon(
                                      onPressed: () async {
                                        await addTimesheet();
                                      },
                                      icon: Icon(Icons.add_circle_outline,
                                          size: 18),
                                      label: Text(
                                        globalController.lang.value == "fr"
                                            ? "Ajouter"
                                            : "Add",
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: MyColors.MainRedBig,
                                        foregroundColor: Colors.white,
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    )),
                Obx(() => Visibility(
                      visible: homeController.toggleHide.isFalse,
                      child: SizedBox(
                        height: screenHeight * 0.01,
                      ),
                    )),
                // Modern Toggle Button
                Obx(() => Container(
                      margin: EdgeInsets.only(bottom: 16),
                      child: TextButton.icon(
                        onPressed: () async {
                          homeController.toggleHide.value =
                              !homeController.toggleHide.value;
                        },
                        icon: Icon(
                          homeController.toggleHide.value == false
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: MyColors.MainRedBig,
                          size: 24,
                        ),
                        label: Text(
                          globalController.lang.value == "fr"
                              ? (homeController.toggleHide.value == false
                                  ? "Masquer le Formulaire"
                                  : "Afficher le Formulaire")
                              : (homeController.toggleHide.value == false
                                  ? "Hide Form"
                                  : "Show Form"),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: MyColors.MainRedBig,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          backgroundColor: MyColors.MainRedBig.withOpacity(0.1),
                          padding: EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    )),

                // Modern Toggle Switch Card
                Container(
                  margin: EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.08),
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: MyColors.MainRedBig.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(Icons.view_list,
                                  color: MyColors.MainRedBig, size: 20),
                            ),
                            SizedBox(width: 12),
                            Text(
                              globalController.lang.value == "fr"
                                  ? "Mode d'Affichage"
                                  : "Display Mode",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        Obx(() => ToggleSwitch(
                              fontSize: 14,
                              minHeight: 50,
                              minWidth: double.infinity,
                              initialLabelIndex:
                                  homeController.toggleController.value,
                              cornerRadius: 12.0,
                              activeFgColor: Colors.white,
                              inactiveBgColor: Colors.grey[200]!,
                              inactiveFgColor: Colors.grey[600]!,
                              totalSwitches: 2,
                              labels: globalController.lang.value == "fr"
                                  ? ["Jour Sélectionné", "Dernière Entrée"]
                                  : ["Selected Day", "Latest Entry"],
                              icons: [Icons.today, Icons.history],
                              activeBgColors: [
                                [MyColors.MainRedBig],
                                [MyColors.MainRedBig]
                              ],
                              onToggle: (index) async {
                                homeController.toggleController.value = index!;
                                if (homeController.toggleController.value ==
                                    1) {
                                  await homeController.getRecentTimesheet();
                                } else {
                                  // Use the currently selected date instead of resetting to today
                                  var currentDate = homeController
                                          .birthdate.value.text.isNotEmpty
                                      ? homeController.birthdate.value.text
                                      : DateFormat("yyyy-MM-dd")
                                          .format(DateTime.now());
                                  await homeController.getTimesheets(
                                      DateTime.parse(currentDate));
                                }
                              },
                            )),
                      ],
                    ),
                  ),
                ),
                // Timesheet List Section
                Container(
                  height: 400, // Fixed height for the list
                  child: Obx(
                    () => homeController.listviewload.value == false
                        ? homeController.imputationlist.isNotEmpty
                            ? ListView.builder(
                                itemCount: homeController.imputationlist.length,
                                itemBuilder: (BuildContext context, int index) {
                                  return Dismissible(
                                    key: Key(homeController
                                        .imputationlist[index].id
                                        .toString()),
                                    direction: homeController
                                                .imputationlist[index]
                                                .isValidated ==
                                            false
                                        ? DismissDirection.startToEnd
                                        : DismissDirection.none,
                                    onDismissed: (direction) async {
                                      if (homeController.imputationlist[index]
                                              .isValidated ==
                                          false) {
                                        await homeController.deleteTimesheet(
                                            homeController
                                                .imputationlist[index].id!);
                                        await homeController.getTimesheets(
                                            DateTime.parse(homeController
                                                .birthdate.value.text));
                                        homeController.imputationlist.refresh();
                                        Get.snackbar(
                                          '',
                                          '',
                                          titleText: BigText(
                                            text: globalController.lang.value ==
                                                    "fr"
                                                ? "Succées"
                                                : "Success",
                                            size: 18,
                                            color: Colors.green,
                                          ),
                                          messageText: Text(
                                            globalController.lang.value == "fr"
                                                ? "Feuille de temps supprimée avec succès!"
                                                : "Timesheet Removed Successfully!",
                                            style: TextStyle(
                                              fontSize: 17,
                                            ),
                                          ),
                                          snackPosition: SnackPosition.BOTTOM,
                                          backgroundColor:
                                              MyColors.BordersGrey.withOpacity(
                                                  0.4),
                                          overlayBlur: 1.5,
                                        );
                                      }
                                    },
                                    background: homeController
                                                .imputationlist[index]
                                                .isValidated ==
                                            false
                                        ? Container(
                                            width: 20,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceEvenly,
                                              children: [
                                                Icon(
                                                  Icons.double_arrow,
                                                  size: 30,
                                                  color: MyColors.MainRedSecond,
                                                ),
                                                Text(
                                                  globalController.lang.value ==
                                                          "fr"
                                                      ? "Glisser pour Supprimer"
                                                      : "Drag to Delete",
                                                  style:
                                                      TextStyle(fontSize: 18),
                                                ),
                                                Icon(
                                                  Icons.double_arrow,
                                                  size: 30,
                                                  color: MyColors.MainRedSecond,
                                                ),
                                              ],
                                            ),
                                          )
                                        : SizedBox.shrink(),
                                    child: Container(
                                      margin: EdgeInsets.only(bottom: 12),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.08),
                                            blurRadius: 8,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.all(16),
                                        child: Row(
                                          children: [
                                            // Left Icon
                                            Container(
                                              padding: EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: MyColors.thirdColor
                                                    .withOpacity(0.1),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Icon(
                                                Icons.assignment_outlined,
                                                color: MyColors.thirdColor,
                                                size: 24,
                                              ),
                                            ),
                                            SizedBox(width: 16),
                                            // Content
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // Project
                                                  Row(
                                                    children: [
                                                      Text(
                                                        globalController.lang
                                                                    .value ==
                                                                "fr"
                                                            ? "Projet: "
                                                            : "Project: ",
                                                        style: TextStyle(
                                                          fontSize: 13,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              Colors.grey[600],
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: Text(
                                                          homeController
                                                              .imputationlist[
                                                                  index]
                                                              .projet,
                                                          style: TextStyle(
                                                            fontSize: 13,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color:
                                                                Colors.black87,
                                                          ),
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(height: 4),
                                                  // Lot
                                                  Row(
                                                    children: [
                                                      Text(
                                                        globalController.lang
                                                                    .value ==
                                                                "fr"
                                                            ? "Lot: "
                                                            : "Lot: ",
                                                        style: TextStyle(
                                                          fontSize: 13,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              Colors.grey[600],
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: Text(
                                                          homeController
                                                              .imputationlist[
                                                                  index]
                                                              .phase,
                                                          style: TextStyle(
                                                            fontSize: 13,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color:
                                                                Colors.black87,
                                                          ),
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(height: 4),
                                                  // Task
                                                  Row(
                                                    children: [
                                                      Text(
                                                        globalController.lang
                                                                    .value ==
                                                                "fr"
                                                            ? "Tâche: "
                                                            : "Task: ",
                                                        style: TextStyle(
                                                          fontSize: 13,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              Colors.grey[600],
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: Text(
                                                          homeController
                                                              .imputationlist[
                                                                  index]
                                                              .tache,
                                                          style: TextStyle(
                                                            fontSize: 13,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color:
                                                                Colors.black87,
                                                          ),
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            // Right side - Status and Hours
                                            Column(
                                              children: [
                                                // Status indicator
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                                  decoration: BoxDecoration(
                                                    color: homeController
                                                                .imputationlist[
                                                                    index]
                                                                .isValidated ==
                                                            false
                                                        ? Colors.orange
                                                            .withOpacity(0.1)
                                                        : Colors.green
                                                            .withOpacity(0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Icon(
                                                    homeController
                                                                .imputationlist[
                                                                    index]
                                                                .isValidated ==
                                                            false
                                                        ? Icons.pending_outlined
                                                        : Icons
                                                            .check_circle_outline,
                                                    color: homeController
                                                                .imputationlist[
                                                                    index]
                                                                .isValidated ==
                                                            false
                                                        ? Colors.orange
                                                        : Colors.green,
                                                    size: 16,
                                                  ),
                                                ),
                                                SizedBox(height: 8),
                                                // Hours
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 12,
                                                      vertical: 6),
                                                  decoration: BoxDecoration(
                                                    color: MyColors.MainRedBig
                                                        .withOpacity(0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                  ),
                                                  child: Text(
                                                    "${homeController.imputationlist[index].hours.toInt()}h",
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          MyColors.MainRedBig,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              )
                            : Padding(
                                padding: const EdgeInsets.all(30),
                                child: Text(
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: "aileron",
                                  ),
                                  globalController.lang.value == "fr"
                                      ? "Il semblerait qu'il n'y ait pas de feuilles de temps disponibles pour cette journée."
                                      : "It looks like there are no timesheets available for this day.",
                                ),
                              )
                        : Container(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "Loading...",
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontFamily: "aileron",
                                    ),
                                  ),
                                  SizedBox(
                                    height: 25,
                                  ),
                                  CircularProgressIndicator(
                                    color: MyColors.thirdColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Container homePageTablet(
      HomePageController homeController,
      double screenWidth,
      double screenHeight,
      Future<dynamic> Function() subtractDay,
      BuildContext context,
      Future<dynamic> Function() addDay,
      void Function() onMinusQuantity,
      GlobalController globalController,
      void Function() onAddQuantity,
      Future<dynamic> Function() addTimesheet) {
    return Container(
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Form(
          key: homeController.homeKey,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SizedBox(
                width: screenWidth * 0.35,
                child: Column(children: [
                  Container(
                    width: screenWidth * 0.9,
                    height: screenHeight * 0.06,
                    decoration: BoxDecoration(
                      color: MyColors.Strokecolor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(17),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IconButton(
                            icon: Icon(Icons.arrow_back),
                            onPressed: () async {
                              if (homeController.listviewload == false) {
                                await subtractDay();
                              }
                            },
                          ),
                          SizedBox(
                            width: screenWidth * 0.2,
                            child: GestureDetector(
                              onTap: () async {
                                DateTime? pickdate = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.parse(
                                        homeController.birthdate.value.text),
                                    firstDate: DateTime(1920),
                                    lastDate: DateTime(2060));
                                if (pickdate != null) {
                                  homeController.birthdate.value.text =
                                      DateFormat("yyyy-MM-dd").format(pickdate);
                                  await homeController.getTimesheets(
                                      DateTime.parse(
                                          homeController.birthdate.value.text));
                                }
                              },
                              child: Myinput(
                                aligncenter: true,
                                color: Colors.transparent,
                                enabled: false,
                                controller: homeController.birthdate.value,
                                validate: (v) =>
                                    homeController.validateThese(v!),
                                labelText: "today",
                                icon: Icons.calendar_month,
                                keyboardType: TextInputType.datetime,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: Icon(Icons.arrow_forward),
                            onPressed: () async {
                              if (homeController.listviewload.value == false) {
                                await addDay();
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: screenHeight * 0.01,
                  ),
                  Obx(() => homeController.loadproject.value == false
                      ? SizedBox(
                          child: SizedBox(
                            height: screenHeight * 0.068,
                            width: screenWidth * 0.9,
                            child: MyDropDown(
                                aligncenter: true,
                                projectcontroller:
                                    homeController.projetController.value,
                                icon: Icons.folder_copy,
                                labelText: "dddf",
                                project: homeController.listproject,
                                Suffixicon: Icons.arrow_drop_down,
                                onChangedProject: (Project? p) async {
                                  await homeController.onChangeProject(p!.id);
                                  homeController.projetController.value = p;
                                }),
                          ),
                        )
                      : CircularProgressIndicator(
                          color: MyColors.thirdColor,
                        )),
                  SizedBox(
                    height: screenHeight * 0.01,
                  ),
                  Obx(() => homeController.loadlots.value == false
                      ? SizedBox(
                          height: screenHeight * 0.068,
                          width: screenWidth * 0.9,
                          child: MyDropDown(
                              projectlotscontroller:
                                  homeController.facetController.value,
                              icon: Icons.work,
                              labelText: "dddf",
                              projectLot: homeController.listphase,
                              Suffixicon: Icons.arrow_drop_down,
                              onChangedProjectlot: (dd) async {
                                await homeController.onChangeProjectlot(dd!.id);
                                homeController.facetController.value = dd;
                              }))
                      : CircularProgressIndicator(
                          color: MyColors.thirdColor,
                        )),
                  SizedBox(
                    height: screenHeight * 0.01,
                  ),
                  Obx(() => homeController.loadtasks.value == false
                      ? SizedBox(
                          child: SizedBox(
                            height: screenHeight * 0.068,
                            width: screenWidth * 0.4,
                            child: MyDropDown(
                                projecttaskscontroller:
                                    homeController.tacheController.value,
                                icon: Icons.task,
                                labelText: "dddf",
                                projecttasks: homeController.listtache,
                                Suffixicon: Icons.arrow_drop_down,
                                onChangedProjecttask: (dd) => {
                                      homeController.tacheController.value =
                                          dd!,
                                      print(
                                          homeController.tacheController.value)
                                    }),
                          ),
                        )
                      : CircularProgressIndicator(
                          color: MyColors.thirdColor,
                        )),
                  SizedBox(
                    height: screenHeight * 0.01,
                  ),
                  Container(
                    width: screenWidth * 0.4,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(17),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        IconButton(
                            onPressed: onMinusQuantity,
                            icon: Icon(Icons.remove_circle)),
                        SizedBox(
                          width: screenWidth * 0.2,
                          child: Myinput(
                            labelText: globalController.lang.value == "fr"
                                ? "Quantity"
                                : "password",
                            textInputAction: TextInputAction.go,
                            keyboardType: TextInputType.number,
                            aligncenter: true,
                            controller: homeController.quantiteController.value,
                            icon: Icons.hourglass_bottom,
                            validate: (v) => homeController.validateThese2(v!),
                            onChanged: (value) {
                              final val = TextSelection.collapsed(
                                offset: homeController
                                    .quantiteController.value.text.length,
                              );
                              homeController
                                  .quantiteController.value.selection = val;
                            },
                          ),
                        ),
                        IconButton(
                          onPressed: onAddQuantity,
                          icon: Icon(Icons.add_circle),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: screenHeight * 0.02,
                  ),
                  SizedBox(
                    width: screenWidth * 0.9,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() => ButtonWithIcon(
                              onPressed: () async {
                                await homeController.copyLasttimesheet();
                              },
                              width: screenWidth * 0.18,
                              height: screenHeight * 0.068,
                              mainColor: MyColors.thirdColor,
                              textcolor: Colors.white,
                              fontSize: 14,
                              text: globalController.lang.value == "fr"
                                  ? "Copier la Dernière Entrée"
                                  : "Copy Latest Entry",
                            )),
                        Obx(() => homeController.listviewload == false
                            ? ButtonWithIcon(
                                onPressed: () async {
                                  if (homeController.listviewload == false) {
                                    await addTimesheet();
                                  }
                                },
                                text: globalController.lang.value == "fr"
                                    ? "Ajouter"
                                    : "Add",
                                mainColor: Colors.white,
                                urlimage: "assets/google.png",
                                fontSize: 14,
                                width: screenWidth * 0.10,
                                height: screenHeight * 0.068,
                              )
                            : CircularProgressIndicator(
                                color: MyColors.thirdColor,
                              ))
                      ],
                    ),
                  ),
                ]),
              ),
              VerticalDivider(
                width: 7,
                thickness: 2,
                color: MyColors.Strokecolor.withOpacity(0.2),
                indent: 5, // spacing at the start of divider
                endIndent: 55,
              ),
              SizedBox(
                width: screenWidth * 0.5,
                child: Column(
                  children: [
                    Obx(() => ToggleSwitch(
                          fontSize: 15,
                          minHeight: screenHeight * 0.065,
                          minWidth: screenWidth * 0.24,
                          initialLabelIndex:
                              homeController.toggleController.value,
                          cornerRadius: 20.0,
                          activeFgColor: MyColors.inputcolorfill,
                          inactiveBgColor: MyColors.NotCompletedStepText,
                          inactiveFgColor: MyColors.inputcolorfill,
                          totalSwitches: 2,
                          labels: globalController.lang.value == "fr"
                              ? ["Jour Sélectionné", "la Dernière Entrée"]
                              : ["Selected Day", "Latest Entry"],
                          icons: [Icons.today, Icons.history],
                          activeBgColors: [
                            [MyColors.MainRedSecond],
                            [MyColors.MainRedSecond]
                          ],
                          onToggle: (index) async {
                            homeController.toggleController.value = index!;
                            if (homeController.toggleController.value == 1) {
                              await homeController.getRecentTimesheet();
                            } else {
                              // Use the currently selected date instead of resetting to today
                              var currentDate =
                                  homeController.birthdate.value.text.isNotEmpty
                                      ? homeController.birthdate.value.text
                                      : DateFormat("yyyy-MM-dd")
                                          .format(DateTime.now());
                              await homeController
                                  .getTimesheets(DateTime.parse(currentDate));
                            }
                          },
                        )),
                    SizedBox(
                      height: screenHeight * 0.02,
                    ),
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: SizedBox(
                          height: screenHeight * 0.7,
                          child: Obx(
                            () => homeController.listviewload == false
                                ? homeController.imputationlist.isNotEmpty
                                    ? ListView.builder(
                                        padding: EdgeInsets.only(
                                            bottom: 15, top: 10),
                                        itemCount: homeController
                                            .imputationlist.length,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          return Dismissible(
                                            key: Key(homeController
                                                .imputationlist[index].id
                                                .toString()),
                                            direction: homeController
                                                        .imputationlist[index]
                                                        .isValidated ==
                                                    false
                                                ? DismissDirection.startToEnd
                                                : DismissDirection.none,
                                            onDismissed: (direction) async {
                                              if (homeController
                                                      .imputationlist[index]
                                                      .isValidated ==
                                                  false) {
                                                await homeController
                                                    .deleteTimesheet(
                                                        homeController
                                                            .imputationlist[
                                                                index]
                                                            .id!);
                                                await homeController
                                                    .getTimesheets(
                                                        DateTime.parse(
                                                            homeController
                                                                .birthdate
                                                                .value
                                                                .text));
                                                homeController.imputationlist
                                                    .refresh();
                                                Get.snackbar(
                                                  '',
                                                  '',
                                                  titleText: BigText(
                                                    text:
                                                        globalController.lang ==
                                                                "fr"
                                                            ? "Succées"
                                                            : "Success",
                                                    size: 18,
                                                    color: Colors.green,
                                                  ),
                                                  messageText: Text(
                                                    globalController
                                                                .lang.value ==
                                                            "fr"
                                                        ? "Feuille de temps supprimée avec succès!"
                                                        : "Timesheet Removed Successfully!",
                                                    style: TextStyle(
                                                      fontSize: 17,
                                                    ),
                                                  ),
                                                  snackPosition:
                                                      SnackPosition.BOTTOM,
                                                  backgroundColor:
                                                      MyColors.BordersGrey
                                                          .withOpacity(0.4),
                                                  overlayBlur: 1.5,
                                                );
                                              }
                                            },
                                            background: homeController
                                                        .imputationlist[index]
                                                        .isValidated ==
                                                    false
                                                ? Container(
                                                    width: 20,
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                    ),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceEvenly,
                                                      children: [
                                                        Icon(
                                                          Icons.double_arrow,
                                                          size: 30,
                                                          color: MyColors
                                                              .MainRedSecond,
                                                        ),
                                                        Text(
                                                          globalController.lang
                                                                      .value ==
                                                                  "fr"
                                                              ? "Glisser pour Supprimer"
                                                              : "Drag to Delete",
                                                          style: TextStyle(
                                                              fontSize: 18),
                                                        ),
                                                        Icon(
                                                          Icons.double_arrow,
                                                          size: 30,
                                                          color: MyColors
                                                              .MainRedSecond,
                                                        ),
                                                      ],
                                                    ),
                                                  )
                                                : SizedBox.shrink(),
                                            child: Card(
                                              child: SizedBox(
                                                height: screenHeight * 0.12,
                                                child: ListTile(
                                                  title: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 20,
                                                        vertical: 10),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        RichText(
                                                          textAlign:
                                                              TextAlign.left,
                                                          text: TextSpan(
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.black,
                                                              fontSize: 12,
                                                            ),
                                                            children: [
                                                              TextSpan(
                                                                text: globalController
                                                                            .lang ==
                                                                        "fr"
                                                                    ? "Projet : "
                                                                    : "Project : ",
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily:
                                                                      "aileron",
                                                                  fontSize: 18,
                                                                  color: MyColors
                                                                      .blackbackground2,
                                                                ),
                                                              ),
                                                              TextSpan(
                                                                text: homeController
                                                                    .imputationlist[
                                                                        index]
                                                                    .projet,
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily:
                                                                      "aileron",
                                                                  fontSize: 18,
                                                                  color: MyColors
                                                                      .blackbackground2,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          height: screenHeight *
                                                              0.002,
                                                        ),
                                                        RichText(
                                                          textAlign:
                                                              TextAlign.left,
                                                          text: TextSpan(
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.black,
                                                              fontSize: 18,
                                                            ),
                                                            children: [
                                                              TextSpan(
                                                                text: globalController
                                                                            .lang ==
                                                                        "fr"
                                                                    ? "Phase/lot : "
                                                                    : "Phase : ",
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily:
                                                                      "aileron",
                                                                  fontSize: 18,
                                                                  color: MyColors
                                                                      .blackbackground2,
                                                                ),
                                                              ),
                                                              TextSpan(
                                                                text: homeController
                                                                    .imputationlist[
                                                                        index]
                                                                    .phase,
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily:
                                                                      "aileron",
                                                                  fontSize: 18,
                                                                  color: MyColors
                                                                      .blackbackground2,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          height: screenHeight *
                                                              0.002,
                                                        ),
                                                        RichText(
                                                          textAlign:
                                                              TextAlign.left,
                                                          text: TextSpan(
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.black,
                                                              fontSize: 18,
                                                            ),
                                                            children: [
                                                              TextSpan(
                                                                text: globalController
                                                                            .lang ==
                                                                        "fr"
                                                                    ? "Tache : "
                                                                    : "Task : ",
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily:
                                                                      "aileron",
                                                                  fontSize: 18,
                                                                  color: MyColors
                                                                      .blackbackground2,
                                                                ),
                                                              ),
                                                              TextSpan(
                                                                text: homeController
                                                                    .imputationlist[
                                                                        index]
                                                                    .tache,
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily:
                                                                      "aileron",
                                                                  fontSize: 18,
                                                                  color: MyColors
                                                                      .blackbackground2,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  leading: Icon(
                                                    Icons.document_scanner,
                                                    size: 35,
                                                    color: MyColors.thirdColor,
                                                  ),
                                                  trailing: SizedBox(
                                                    width: screenWidth * 0.178,
                                                    child: Column(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: Icon(
                                                            Icons.circle,
                                                            color: homeController
                                                                        .imputationlist[
                                                                            index]
                                                                        .isValidated ==
                                                                    false
                                                                ? Colors.orange
                                                                : Colors.green,
                                                            size: 18,
                                                          ),
                                                        ),
                                                        Text(
                                                          homeController
                                                              .imputationlist[
                                                                  index]
                                                              .hours
                                                              .toString(),
                                                          style: TextStyle(
                                                            fontSize: 20,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      )
                                    : Padding(
                                        padding: const EdgeInsets.all(30),
                                        child: Text(
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            fontFamily: "aileron",
                                          ),
                                          globalController.lang.value == "fr"
                                              ? "Il semblerait qu'il n'y ait pas de feuilles de temps disponibles pour cette journée."
                                              : "It looks like there are no timesheets available for this day.",
                                        ),
                                      )
                                : Container(
                                    child: Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            "Loading...",
                                            style: TextStyle(
                                              fontSize: 20,
                                              fontFamily: "aileron",
                                            ),
                                          ),
                                          SizedBox(
                                            height: 25,
                                          ),
                                          CircularProgressIndicator(
                                            color: MyColors.thirdColor,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method for modern dropdown sections
  Widget _buildModernDropdownSection(
      String title, IconData icon, Widget dropdown) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: MyColors.MainRedBig.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: MyColors.MainRedBig, size: 20),
            ),
            SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: dropdown,
        ),
      ],
    );
  }

  // Helper method for loading sections
  Widget _buildLoadingSection(String text) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: MyColors.MainRedBig,
            ),
          ),
          SizedBox(width: 12),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
