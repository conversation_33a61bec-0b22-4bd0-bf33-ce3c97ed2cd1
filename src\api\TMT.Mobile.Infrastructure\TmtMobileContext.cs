﻿using Microsoft.EntityFrameworkCore;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Infrastructure
{
    public class TmtMobileContext :DbContext
    {
            public TmtMobileContext() { 
        
        
        }

        public TmtMobileContext(DbContextOptions<TmtMobileContext> options)
             : base(options)
        {



        }

        

        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<Organization> Organizations { get; set; }
        public virtual DbSet<UserOrganizations> UserOrganizations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            

            modelBuilder.Entity<User>(entity =>
      {
           entity.ToTable("users");
          entity.Property(e => e.Id)
           .HasColumnName("id")
           .ValueGeneratedOnAdd();
          entity.Property(e => e.Email).HasColumnName("email");
          entity.Property(e => e.FirstName).HasColumnName("firstname");
          entity.Property(e => e.LastName).HasColumnName("lastname");
          entity.Property(e => e.PasswordHash).HasColumnName("passwordhash");
          entity.Property(e => e.PasswordSalt).HasColumnName("passwordsalt");
          entity.Property(e => e.ResetPasswordRequestedDate).HasColumnName("resetpasswordrequesteddate");
          entity.Property(e => e.IsDeleted).HasColumnName("isdeleted");
          entity.Property(e => e.AccountValidationRequestedDate).HasColumnName("accountvalidationrequesteddate");
          entity.Property(e => e.AccountValidated).HasColumnName("accountvalidated");
          entity.Property(e => e.AccountValidationDate).HasColumnName("accountvalidationdate");
          entity.Property(e => e.AccountValidationNumber).HasColumnName("accountvalidationnumber");
          entity.Property(e => e.CreatedDate).HasColumnName("createddate");
          entity.Property(e => e.UpdatedDate).HasColumnName("updateddate");
          entity.Property(e => e.IdFavoriteOrganization).HasColumnName("idfavoriteorganization");
          entity.Property(e => e.FullName).HasColumnName("fullname");

          entity.Property(e => e.Login).HasColumnName("login");
          entity.Property(e => e.GUID).HasColumnName("guid").HasColumnType("uuid");

          entity.Property(e => e.ResetPasswordNumber).HasColumnName("resetpasswordnumber");
          entity.Property(e => e.ResetPasswordValidated).HasColumnName("resetpasswordvalidated");
          entity.Property(e => e.ResetPasswordValidationDate).HasColumnName("resetpasswordvalidationdate");

       
          entity.Property(e => e.Email).HasColumnName("email");
       
          entity.Property(e => e.IdentityProvider).HasColumnName("identityprovider").HasConversion<int>();
          entity.Property(e => e.IsInvitation).HasColumnName("isinvitation");
          entity.Property(e => e.MaxOrganizations).HasColumnName("maxorganizations");
          entity.HasMany(e => e.UserOrganizations).WithOne(x => x.User);
          entity.HasMany(e => e.OrganizationsOwned).WithOne(x => x.Owner);
          entity.HasOne(e => e.FavoriteOrganization).WithMany(x => x.FavoriteOrganizations).HasForeignKey(d => d.IdFavoriteOrganization);




      });


            modelBuilder.Entity<Organization>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.ToTable("organizations");

                entity.Property(e => e.Id)
                          .HasColumnName("id")
                          .ValueGeneratedOnAdd();
                entity.Property(e => e.GUID).HasColumnName("guid").HasColumnType("uuid");
                entity.Property(e => e.Name).HasColumnName("name");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.Reference).HasColumnName("reference");
                entity.Property(e => e.IdOwner).HasColumnName("idowner");
                entity.Property(e => e.IsDeleted).HasColumnName("isdeleted");
                entity.Property(e => e.ConnectionString).HasColumnName("connectionstring");
                entity.Property(e => e.CreatedDate).HasColumnName("createddate");
                entity.Property(e => e.UpdatedDate).HasColumnName("updateddate");
                entity.Property(e => e.CreationTemplate).HasColumnName("creationtemplate");
                entity.Property(e => e.IsActive).HasColumnName("isactive");
                entity.Property(e => e.IsSuspended).HasColumnName("issuspended");
                entity.Property(e => e.SchemaCreated).HasColumnName("schemacreated");

                entity.HasMany(e => e.UserOrganizations)
                            .WithOne(x => x.Organization);
                entity.HasOne(e => e.Owner)
                            .WithMany(e => e.OrganizationsOwned)
                            .HasForeignKey(d => d.IdOwner);

            }
      );


            modelBuilder.Entity<UserOrganizations>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.ToTable("userorganizations");

                entity.Property(e => e.Id)
                          .HasColumnName("id")
                          .ValueGeneratedOnAdd();

                entity.Property(e => e.IdOrganization).HasColumnName("idorganization");
                entity.Property(e => e.IdUser).HasColumnName("idperson");
                entity.Property(e => e.CreatedDate).HasColumnName("createddate");
                entity.Property(e => e.CreatedBy).HasColumnName("createdby");
                entity.Property(e => e.UpdatedDate).HasColumnName("updateddate");
                entity.Property(e => e.UpdatedBy).HasColumnName("updatedby");
                entity.Property(e => e.IsAdmin).HasColumnName("isadmin");
                entity.Property(e => e.IsSuperUser).HasColumnName("issuperuser");
                entity.Property(e => e.IsDeleted).HasColumnName("isdeleted");
                entity.Property(e => e.Role).HasColumnName("orgrole").HasConversion<int>();

                entity.HasOne(e => e.User)
                            .WithMany(x => x.UserOrganizations)
                            .HasForeignKey(d => d.IdUser);
                entity.HasOne(e => e.Organization)
                            .WithMany(x => x.UserOrganizations)
                            .HasForeignKey(d => d.IdOrganization);
            }
        );

        }



    }
}