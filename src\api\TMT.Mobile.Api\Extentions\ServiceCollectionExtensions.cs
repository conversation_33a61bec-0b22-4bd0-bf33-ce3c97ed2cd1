

using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Claims;
using System.Threading;

using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Consts;
using TMT.Mobile.Infrastructure;
using Organization = TMT.Mobile.Core.Entities.Organization;

namespace TMT.Mobile.Api.Services
{
  public static class ServiceCollectionExtensions
  {
    public static IServiceCollection UseConnectionPerOrganization(this IServiceCollection services, IConfiguration configuration)
    {
      services.AddScoped(serviceProvider =>
      {
        var dbContext = serviceProvider.GetRequiredService<TmtMobileContext>();
        var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
        return BuildServiceProvider(serviceProvider, dbContext, httpContextAccessor);
      });

      return services;
    }

    private static DynamicDbContext BuildServiceProvider(IServiceProvider serviceProvider, TmtMobileContext dbContext, IHttpContextAccessor httpContextAccessor)
    {
      //How to get the org reference from
      var _IHttpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
      var user = _IHttpContextAccessor.HttpContext.User as ClaimsPrincipal;
      var identity = user.Identity as ClaimsIdentity;
      var Org = (from c in user.Claims
                 where c.Type == TMTClaimsTypes.OrgGuid
                 select c).SingleOrDefault();

      if (Org != null)
      {
        var OrgGuid = new Guid(Org.Value);

        var userGuid = _IHttpContextAccessor.HttpContext.User.Claims.Single(x => x.Type == TMTClaimsTypes.UserGuid).Value;
        var userId = _IHttpContextAccessor.HttpContext.User.Claims.Single(x => x.Type == TMTClaimsTypes.UserId).Value;
        #region Get the organization
        Organization organization = dbContext.Organizations.FirstOrDefault(o => o.GUID == OrgGuid);
        if (organization == null)
        {

          return null;
        }
        #endregion

        #region Get User Organization permissions        

        var options = new DbContextOptionsBuilder<DynamicDbContext>()
                  .UseNpgsql(organization.ConnectionString)
                  .Options;
        var context = new DynamicDbContext(options);

        #endregion
        return context;
      }
      else
      {

        return null;
      }
    }

    private static void DisconnectUser(IHttpContextAccessor _IHttpContextAccessor)
    {

    }
  }
}
