using Microsoft.EntityFrameworkCore;

using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class ProjectfeesRepository : CoreRepository<Projectfees, DynamicDbContext>
    {

        public ProjectfeesRepository(DynamicDbContext context) : base(context)
        {

        }

        public async Task<IEnumerable<Projectfees>> GetAllAsync()
        {
        return await context.ProjectFees.ToListAsync();
        }
        public async Task<Projectfees?> GetByIdAsync(int id)
        {
            return await context.ProjectFees.FindAsync(id);
        }

        public async Task AddAsync(Projectfees projectfees)
        {
            await context.ProjectFees.AddAsync(projectfees);
            await context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Projectfees Projectfees)
        {
            context.ProjectFees.Update(Projectfees);
            await context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Projectfees Projectfees)
        {
            context.ProjectFees.Remove(Projectfees);
            await context.SaveChangesAsync();
        }

        public async Task<List<Projectfees>> GetByUserIdAsync(int userId)
        {
            return await context.ProjectFees
                .Where(lr => lr.IdClient == userId && !lr.Isdeleted)
                .ToListAsync();
        }

        public async Task<List<Projectfees>> GetAssignedToUserAsync(int userId)
        {
            return await context.ProjectFees
                .Where(lr => !lr.Isdeleted && 
                            (lr.IdAssignedto == userId))
                .ToListAsync();
        }

        public async Task<IEnumerable<Projectfees>> GetByCreatedByAsync(string createdBy)
    {
          return await context.ProjectFees
                .Where(lr => !lr.Isdeleted && 
                            (lr.Createdby == createdBy))
                .ToListAsync();
    }

    }
}