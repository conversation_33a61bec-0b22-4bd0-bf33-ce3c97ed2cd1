using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
  public partial class Projectbills : IEntity
  {
    public Projectbills()
    {
    //  BillDetails = new HashSet<BillDetail>();
    //  Projectfees = new HashSet<Projectfees>();
    }
    public int Id { get; set; }
    public DateTime? Datebillestimated { get; set; }
    public DateTime? Datebillplanned { get; set; }
    public DateTime? Datebill { get; set; }
    public int? Idbillstate { get; set; }
    public int? Idproject { get; set; }
    public int? Idorder { get; set; }
    public string? Reference { get; set; }
    public string? Billnumber { get; set; }
    public string? Comment { get; set; }
    public string? MilestoneName { get; set; }
    public double? Quantity { get; set; }
    public double? Percentage { get; set; }
    public double? Amount { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public int? IdProjectMilestone { get; set; }
    public string? Title { get; set; }
    public int? ProductId { get; set; }
    public double? UnitPrice { get; set; }
    public virtual Appvariables Billstate { get; set; }
    public virtual Projectorders RelatedOrder { get; set; }
    public virtual Projects Project { get; set; }
   // public virtual Projectmilestones ProjectMilestone { get; set; }
   // public virtual ICollection<BillDetail> BillDetails { get; set; }
   // public virtual Product Product { get; set; }
    public virtual ICollection<Projectfees> Projectfees { get; set; }
    public int? IdCurrency { get; set; }
    public Currency? Currency { get; set; }
    public double? ExchangeRate { get; set; }
  }
}
