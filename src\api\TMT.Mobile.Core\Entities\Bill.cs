using System;
using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;

namespace TMT.Mobile.Core.Entities
{
    public class Bill : IEntity
  {
    public int Id { get; set; }
    public DateTime BillDate { get; set; }
    public string? BillNumber { get; set; }
    public int BillTypeId { get; set; }
    public int ClientId { get; set; }

    public string? PublicNote { get; set; }
    public string? PrivateNote { get; set; }
    public bool Isdeleted { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public string? BillingAddress { get; set; }
    public string? ClientContactDetails { get; set; }
    public string? ClientVATNumber { get; set; }
    public bool? IsValidated { get; set; }
   // public virtual ICollection<BillDetail> BillDetails { get; set; }
    public Appvariables Type { get; set; }
    public Counterparties Client { get; set; }
   // public virtual ICollection<BillTaxe> BillTaxes { get; set; }
    public int Counter { get; set; }
    public int? IdProject { get; set; }
    public int? IdBankAccount { get; set; }
    public int? IdParentBill { get; set; }
    public Bill? ParentBill { get; set; }
    public ICollection<Bill> ChildBills { get; set; }
    public Projects Project { get; set; }
   // public BankAccount? Bank { get; set; }
    public int? IdStatus { get; set; }
    public Appvariables? Status { get; set; }
    public virtual ICollection<Projectfees> Projectfees { get; set; }
    public int? IdCurrency { get; set; }
    public Currency? Currency { get; set; }
    public double? ExchangeRate { get; set; }
   // public virtual ICollection<BillsPayments> BillsPayments { get; set; }
   // public virtual ICollection<LetteringDetail> LetteringDetails { get; set; }
    public DateTime? PaymentDate { get; set; }
    public double? AmountTaxIncl { get; set; }
    public double? AmountTaxExcl { get; set; }
    public double? AmountVat { get; set; }
    public int? IdEntity { get; set; }

   // public virtual OrgEntity? Entity { get; set; }
    public bool? IsTaxExcluded { get; set; }

    public Bill()
    {
    //  BillDetails = new HashSet<BillDetail>();
    //  BillTaxes = new HashSet<BillTaxe>();
    //  ChildBills = new HashSet<Bill>();
      Projectfees = new HashSet<Projectfees>();
    //  BillsPayments = new HashSet<BillsPayments>();
     // LetteringDetails = new HashSet<LetteringDetail>();
    }
  }
}
