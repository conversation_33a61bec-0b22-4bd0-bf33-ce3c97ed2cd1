using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace TMT.Mobile.Infrastructure.Repositories
{
    public class AppvariableRepository : CoreRepository<Appvariables, DynamicDbContext>
    {
        public AppvariableRepository(DynamicDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Appvariables>> GetAllAsync(string language = "en")
        {
            var variables = await context.Appvariables.ToListAsync();
            return ApplyLanguageFilter(variables, language);
        }

        public async Task<Appvariables> GetByIdAsync(int id, string language = "en")
        {
            var variable = await context.Appvariables
                .SingleOrDefaultAsync(p => p.Id == id);
            
            if (variable != null)
            {
                ApplyLanguageSelection(variable, language);
            }
            
            return variable;
        }

        public async Task<IEnumerable<Appvariables>> GetLeaveRequestTypesAsync(string language = "en")
        {
            var variables = await context.Appvariables
                .Where(a => a.Category == "LEAVE_REQUEST_TYPE")
                .ToListAsync();
                
            return ApplyLanguageFilter(variables, language);
        }

        public async Task<IEnumerable<Appvariables>> GetRemoteWorkRequestTypesAsync(string language = "en")
        {
            var variables = await context.Appvariables
                .Where(v => v.Category == "REMOTE_WORK_STATUS")
                .ToListAsync();
                
            return ApplyLanguageFilter(variables, language);
        }
        
         public async Task<IEnumerable<Appvariables>> GetExpenseRequestTypesAsync(string language = "en")
        {
            var variables = await context.Appvariables
                .Where(v => v.Category == "EXPENSE_REQUEST_STATUS")
                .ToListAsync();
                
            return ApplyLanguageFilter(variables, language);
        }
 
        public async Task<IEnumerable<Appvariables>> GetProjectFeesStatusAsync(string language = "en")
        {
            var variables = await context.Appvariables
                .Where(v => v.Category == "PROJECT_FEES_STATUS")
                .ToListAsync();
                
            return ApplyLanguageFilter(variables, language);
        }
        public async Task<IEnumerable<Appvariables>> GetProjectFeesTypesAsync(string language = "en")
        {
            var variables = await context.Appvariables
                .Where(v => v.Category == "PROJECT_FEES_TYPES")
                .ToListAsync();
                
            return ApplyLanguageFilter(variables, language);
        }
        private IEnumerable<Appvariables> ApplyLanguageFilter(IEnumerable<Appvariables> variables, string language)
        {
            foreach (var variable in variables)
            {
                ApplyLanguageSelection(variable, language);
            }
            return variables;
        }
        
        private void ApplyLanguageSelection(Appvariables variable, string language)
        {
            if (language.ToLower() == "fr")
            {
                if (variable.Category == "LEAVE_REQUEST_TYPE")
                {
                    switch (variable.Name)
                    {
                        case "PaidLeave":
                            variable.Name = "Congé payé";
                            break;
                        case "ExitPermit":
                            variable.Name = "Autorisation de sortie";
                            break;
                        case "SickLeave":
                            variable.Name = "Congé maladie";
                            break;
                        case "MaternityLeave":
                            variable.Name = "Congé maternité";
                            break;
                        case "PaternityLeave":
                            variable.Name = "Congé paternité";
                            break;
                        case "UnpaidLeave":
                            variable.Name = "Congé sans solde";
                            break;
                        case "ExceptionalLeave":
                            variable.Name = "Congé exceptionnel";
                            break;
                    }
                }
                else if (variable.Category == "REMOTE_WORK_STATUS")
                {
                    switch (variable.Name)
                    {
                        case "Submitted":
                            variable.Name = "Soumis";
                            break;
                        case "Approved":
                            variable.Name = "Approuvé";
                            break;
                        case "Rejected":
                            variable.Name = "Rejeté";
                            break;
                        case "Canceled":
                            variable.Name = "Annulé";
                            break;
                    }
                }
                else if (variable.Category == "EXPENSE_REQUEST_STATUS")
                {
                    switch (variable.Name)
                    {
                        case "Submitted":
                            variable.Name = "Soumis";
                            break;
                        case "Approved":
                            variable.Name = "Approuvé";
                            break;
                        case "Rejected":
                            variable.Name = "Rejeté";
                            break;
                        case "Canceled":
                            variable.Name = "Annulé";
                            break;
                    }
                }
                 else if (variable.Category == "PROJECT_FEES_STATUS")
                {
                    switch (variable.Name)
                    {
                        case "Waiting":
                            variable.Name = "Attente";
                            break;
                        case "Paid":
                            variable.Name = "payé";
                            break;
                    }
                }
                 else if (variable.Category == "PROJECT_FEES_TYPES")
                {
                    switch (variable.Name)
                    {
                        case "PerDiem":
                            variable.Name = "Journalière";
                            break;
                        case "FlightTicket":
                            variable.Name = "Billet d’avion";
                            break;
                        case "Transportation":
                            variable.Name = "Transport";
                            break;
                        case "Housing":
                            variable.Name = "Logement";
                            break;
                        case "SoftwareAcquisition":
                            variable.Name = "Acquisition de logiciels";
                            break;
                        case "HardwareAcquisition":
                            variable.Name = "Acquisition de matériel";
                            break;
                        case "Others":
                            variable.Name = "Autres";
                            break;
                    }
                }
                var frValue = GetPropertyValue(variable, "Valuefr");
                if (!string.IsNullOrEmpty(frValue))
                {
                    SetPropertyValue(variable, "Value", frValue);
                }
                
                var frDescription = GetPropertyValue(variable, "Descriptionfr");
                if (!string.IsNullOrEmpty(frDescription) && HasProperty(variable, "Description"))
                {
                    SetPropertyValue(variable, "Description", frDescription);
                }
            }
        }

        private bool HasProperty(object obj, string propertyName)
        {
            return obj.GetType().GetProperty(propertyName) != null;
        }

        private string GetPropertyValue(object obj, string propertyName)
        {
            var property = obj.GetType().GetProperty(propertyName);
            if (property != null)
            {
                return property.GetValue(obj)?.ToString();
            }
            return null;
        }

        private void SetPropertyValue(object obj, string propertyName, string value)
        {
            var property = obj.GetType().GetProperty(propertyName);
            if (property != null)
            {
                if (property.PropertyType == typeof(string))
                {
                    property.SetValue(obj, value);
                }
                else if (property.PropertyType == typeof(double?))
                {
                    if (double.TryParse(value, out double doubleValue))
                    {
                        property.SetValue(obj, doubleValue);
                    }
                }
                else if (property.PropertyType == typeof(int?))
                {
                    if (int.TryParse(value, out int intValue))
                    {
                        property.SetValue(obj, intValue);
                    }
                }
            }
        }
        
        public async Task<int?> GetStatusIdByNameAsync(string statusName)
        {
            string searchName = TranslateStatusNameToEnglish(statusName);
            
            var status = await context.Appvariables
                .FirstOrDefaultAsync(a => a.Name == searchName);
                if (status == null)
            {
                if (double.TryParse(statusName, out double numericValue))
                {
                    status = await context.Appvariables
                        .FirstOrDefaultAsync(a => a.Value == numericValue);
                }
                else
                {
                    status = await context.Appvariables
                        .FirstOrDefaultAsync(a => a.Value.ToString() == statusName);
                }
            }
            
            return status?.Id;
        }
        
        private string TranslateStatusNameToEnglish(string frenchName)
        {
            if (string.IsNullOrEmpty(frenchName))
                return frenchName;
                switch (frenchName.ToLower())
            {
                case "approuvé":
                    return "Approved";
                case "rejeté":
                    return "Rejected";
                case "en attente":
                    return "Pending";
                case "annulé":
                    return "Canceled";
                case "congé payé":
                    return "PaidLeave";
                case "autorisation de sortie":
                    return "ExitPermit";
                case "congé maladie":
                    return "SickLeave";
                case "congé maternité":
                    return "MaternityLeave";
                case "congé paternité":
                    return "PaternityLeave";
                case "congé sans solde":
                    return "UnpaidLeave";
                case "congé exceptionnel":
                    return "ExceptionalLeave";
                default:
                    return frenchName; 
            }
        }
    }
}