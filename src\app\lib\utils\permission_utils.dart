import 'dart:convert';
import 'package:tmt_mobile/utils/userServices.dart';
import 'package:tmt_mobile/models/ManagerDto.dart';
import 'package:tmt_mobile/utils/userPrefrences.dart';

Future<bool> isCurrentUserManager() async {
  try {
    final response = await UserServices().getManagers();
    print('Manager API response: ${response.body}');

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final currentEmail = UserPrefrences.getUserEmail()?.toLowerCase();

      final manager =
          data['manager'] != null ? ManagerDto.fromJson(data['manager']) : null;
      final secondManager = data['secondManager'] != null
          ? ManagerDto.fromJson(data['secondManager'])
          : null;

      final managerEmail = manager?.email.toLowerCase();
      final secondManagerEmail = secondManager?.email.toLowerCase();

      print('Current user email: $currentEmail');
      print('Manager email: $managerEmail');
      print('Second manager email: $secondManagerEmail');

      return currentEmail == managerEmail || currentEmail == secondManagerEmail;
    }
  } catch (e) {
    print("Error checking manager status: $e");
  }
  return false;
}
