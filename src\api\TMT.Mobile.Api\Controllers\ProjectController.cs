﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using TMT.Mobile.Api.Interfaces;
using TMT.Mobile.Api.Services;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Consts;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;

namespace TMT.Mobile.Api.Controllers
{
   
    public class ProjectController : ControllerBase
    {
        private readonly DynamicDbContext _DynamicDb;
        private readonly ITokenService _tokenService;
       

        public ProjectController(DynamicDbContext dynamicDb , ITokenService tokenService)
        {

            _DynamicDb = dynamicDb;
            _tokenService = tokenService;

        }


        [HttpGet("getProjects")]
        [Authorize]
        public async Task<IActionResult> GetProjects()
        {
            if (User?.Identity is not ClaimsIdentity identity || !identity.IsAuthenticated)
            {
                return Unauthorized("You are not authenticated");
            }

            var userIdClaim = identity.FindFirst(TMTClaimsTypes.UserId);
            var userLoginClaim = identity.FindFirst(TMTClaimsTypes.UserLogin);

            if (userIdClaim == null || userLoginClaim == null)
            {
                return Unauthorized("Missing required claims");
            }

            var userId = userIdClaim.Value;
            var email = userLoginClaim.Value;

            var projects = await _DynamicDb.Project
                .Where(p => !p.Isdeleted)
                .Select(p => new ProjectDto
                {
                    Id = p.Id,
                    Name = p.Projectname
                })
                .ToListAsync();

            return Ok(projects);
        }
        }
}
