class UserMood {
  bool isAnonymous;
  String? comment;
  int? score;

  UserMood({required this.isAnonymous, this.comment, this.score});

  Map<String, dynamic> toJson() {
    return {
      'isAnonymous': isAnonymous,
      'comment': comment,
      'score': score,
    };
  }

  factory UserMood.fromJson(Map<String, dynamic> json) {
    return UserMood(
      isAnonymous: json['isAnonymous'],
      comment: json['comment'],
      score: json['score'],
    );
  }
}
