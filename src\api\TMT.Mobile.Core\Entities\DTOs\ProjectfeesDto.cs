namespace TMT.Mobile.Core.Entities.DTOs
{
    public class ProjectfeesDto
    {
   public DateTime Feesdate { get; set; }
   public double Feesamount { get; set; }
   public int? IdFeestype { get; set; }
   public int? IdAssignedto { get; set; }
    public string? Comment { get; set; }
    public string? Reference { get; set; }
    public string? Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public double? Feesamounttaxincl { get; set; }
    public int? IdSupplier { get; set; }
    public int? IdCurrency { get; set; }

    }

}
