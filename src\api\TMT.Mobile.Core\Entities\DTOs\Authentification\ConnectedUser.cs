﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TMT.Mobile.Core.Entities.DTOs.Authentification
{
    public class ConnectedUser
    {
        public class ConnectedUserDto
        {
            public int Id { get; set; }
            public Guid GUID { get; set; }
            public string? Firstname { get; set; }
            public string? Lastname { get; set; }

            public string? Email { get; set; }
            public byte[]? PasswordHash { get; set; }
            public byte[]? PasswordSalt { get; set; }
        }
    }
}