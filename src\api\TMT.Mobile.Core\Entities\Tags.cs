using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;

namespace TMT.Mobile.Core.Entities
{
  public partial class Tag : IEntity
  {
    public Tag()
    {
      Tasks = new HashSet<Projecttasks>();
      UserTags = new HashSet<UserTags>();
    }
    public int Id { get; set; }

    public string? Synonyms { get; set; }

    public string? Derivative { get; set; }

    public string? Description { get; set; }

    public ICollection<Projecttasks> Tasks { get; set; }
    public ICollection<UserTags> UserTags { get; set; }
  }
}
