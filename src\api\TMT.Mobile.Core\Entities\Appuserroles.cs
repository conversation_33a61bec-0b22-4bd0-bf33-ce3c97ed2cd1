﻿using System;

namespace TMT.Mobile.Core.Entities
{
    public partial class Appuserroles
    {
        public int Id { get; set; }
        public int? Iduser { get; set; }
        public int? Idroles { get; set; }
        public string Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }

        public virtual Approles Role { get; set; }
        public virtual Appusers User { get; set; }
    }
}
