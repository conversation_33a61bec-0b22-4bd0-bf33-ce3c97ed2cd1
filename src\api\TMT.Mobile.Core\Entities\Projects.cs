﻿using System;
using System.Collections.Generic;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using System.ComponentModel.DataAnnotations.Schema;

namespace TMT.Mobile.Core.Entities
{
  public partial class Projects : IEntity
  {
    public Projects()
    {

      Projectlots = new HashSet<Projectlots>();
 
      Timesheets = new HashSet<Timesheets>();

    }

    public int Id { get; set; }
    public string Projectname { get; set; }
    public string? Projectdescription { get; set; }
    public int? Idcustomer { get; set; }
    public int? Idstatus { get; set; }
    public bool Isinternalproject { get; set; }
    public bool IsNotBillableproject { get; set; }
    public int? Idprojecttype { get; set; }
    public int? IdEngagementType { get; set; }
    public int? IDProjectBilling { get; set; }
    public DateTime? Datestart { get; set; }
    public DateTime? Dateend { get; set; }
    public bool Isdeleted { get; set; }
    public bool IsIdleTime { get; set; }
    public bool IsAbsence { get; set; }
    public bool IsAdmin { get; set; }
    public string Createdby { get; set; }
    public DateTime? Createddate { get; set; }
    public string? Updatedby { get; set; }
    public DateTime? Updateddate { get; set; }
    public double? TurnOverEstimated { get; set; }
    public double? DurationEstimated { get; set; }
    public double? FTEEstimated { get; set; }
    public bool IsOffshore { get; set; }
    public bool IsInternalActivities { get; set; }
    public bool? IsValidated { get; set; }
    public string? ValidatedBy { get; set; }
    public string? ValidatedComment { get; set; }
    public DateTime? ValidatedDate { get; set; }
    public double AverageDailyRate { get; set; }
    public double DaysSold { get; set; }
    public int IdBusinessUnit { get; set; }
    public int? IDProjectDirector { get; set; }
    public bool ProjectDocumentInit { get; set; }
        [NotMapped]
        public virtual Appvariables BusinessUnit { get; set; }
        [NotMapped]
        public virtual Appusers ProjectDirector { get; set; }
        [NotMapped]
        public virtual Appvariables ProjectType { get; set; }
        [NotMapped]

        public virtual Appvariables Status { get; set; }
        [NotMapped]

        public virtual Appvariables EngagementType { get; set; }
        [NotMapped]

        public virtual Appvariables ProjectBilling { get; set; }
  
    public virtual ICollection<Projectlots> Projectlots { get; set; }
 
    public virtual ICollection<Timesheets> Timesheets { get; set; }
        
  }
}
