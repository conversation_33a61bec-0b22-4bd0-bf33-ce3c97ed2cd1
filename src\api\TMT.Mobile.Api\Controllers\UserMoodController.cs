using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace TMT.Mobile.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserMoodController : ControllerBase
    {
        private readonly DynamicDbContext _dynamicDb;
        private readonly ILogger<UserMoodController> _logger;
        private readonly UserMoodRepository _userMoodRepository;

        public UserMoodController(
            ILogger<UserMoodController> logger,
            DynamicDbContext dynamicDb,
            UserMoodRepository userMoodRepository
        )
        {
            _dynamicDb = dynamicDb ?? throw new ArgumentNullException(nameof(dynamicDb));
            _logger = logger;
            _userMoodRepository = userMoodRepository;
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] UserMoodDto userMoodDto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var email = GetUserEmail();

                var user = await _dynamicDb.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new {
                        u.Id,
                        u.Firstname,
                        u.Lastname,
                        u.Email
                    })
                    .FirstOrDefaultAsync();

                if (user == null)
                    return BadRequest("Utilisateur non trouvé");

                if (userMoodDto.Score is not (1 or 3 or 5))
                    return BadRequest("Le score doit être 1, 3 ou 5");

                var today = DateTime.UtcNow.Date;

                var existingMood = await _dynamicDb.UserMoods
                    .AsNoTracking()
                    .FirstOrDefaultAsync(m => m.IdUser == user.Id && m.DateMood.Date == today);

                if (existingMood != null)
                {
                    return BadRequest("Vous avez déjà soumis votre humeur aujourd'hui.");
                }

                var userMood = new UserMood
                {
                    IdUser = user.Id,
                    IsAnonymous = userMoodDto.IsAnonymous,
                    Comment = userMoodDto.Comment,
                    Score = userMoodDto.Score,
                    DateMood = DateTime.UtcNow
                };

                await _userMoodRepository.AddAsync(userMood);

                return CreatedAtAction(nameof(GetById), new { id = userMood.Id }, userMood);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création du mood");
                return StatusCode(500, "Une erreur est survenue lors de la création");
            }
        }

        private string GetUserEmail()
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email);
            if (emailClaim == null)
                throw new UnauthorizedAccessException("Email non trouvé dans le token");

            return emailClaim.Value;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var moods = await _userMoodRepository.GetAllAsync();
            return Ok(moods);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            var mood = await _userMoodRepository.GetByIdAsync(id);
            if (mood == null)
                return NotFound();
            return Ok(mood);
        }

          [HttpGet("my-user-mood")]
        [Authorize]
        public async Task<IActionResult> GetMyUserMood()
        {
            try
            {
                var email = GetUserEmail();
                var user = await _dynamicDb.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new { u.Id })
                    .FirstOrDefaultAsync();
                    
                if (user == null)
                    return NotFound("Utilisateur non trouvé");
                    
                var userId = user.Id;
                var myRequests = await _userMoodRepository.GetByUserIdAsync(userId);
                
                return Ok(myRequests);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "Échec de la récupération de vos demandes de congés",
                    Error = ex.Message
                });
            }
        }
    }
}
