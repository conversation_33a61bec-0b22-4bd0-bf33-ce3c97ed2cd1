<lint-module
    format="1"
    dir="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\android\app"
    name=":app"
    type="APP"
    maven="android:app:"
    gradle="7.3.0"
    buildFolder="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\30.0.3\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
