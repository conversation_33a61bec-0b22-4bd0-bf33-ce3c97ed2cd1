import 'package:get/get.dart';

import 'package:tmt_mobile/models/userdata.dart';
import 'package:tmt_mobile/utils/userPrefrences.dart';
import 'package:tmt_mobile/utils/utils.dart';

class GlobalController extends GetxController {
  var appuser = appUser().obs;

  @override
  void onInit() async {
    devType.value = getDeviceType();
    await _loadUserData();
    super.onInit();
  }

  Future<void> _loadUserData() async {
    try {
      String? userEmail = UserPrefrences.getUserEmail();
      if (userEmail != null && userEmail.isNotEmpty) {
        appuser.value.email = userEmail;
        appuser.value.guid = UserPrefrences.getUserGuid();
        appuser.value.isvalidated = UserPrefrences.getCureentIsValid();
        appuser.value.organisation = UserPrefrences.getCureentOrg();
        print("GlobalController: Loaded user data - Email: $userEmail");
      } else {
        print("GlobalController: No user email found in preferences");
      }
    } catch (e) {
      print("GlobalController: Error loading user data: $e");
    }
  }

  RxString devType = "phone".obs;
  var lang = "fr".obs;
}
