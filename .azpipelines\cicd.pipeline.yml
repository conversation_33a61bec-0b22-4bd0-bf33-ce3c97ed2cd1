trigger:
 - release/**
 - main

name: TMT-Mobile-API.Date-$(date:ddMMyyy).BuildId-$(Build.BuildID)

pool:
  vmImage: ubuntu-latest

parameters:
  - name: Deploy
    displayName: Deploy
    type: boolean
    default: true
  - name: SkipQA
    displayName: Skip QA
    type: boolean
    default: false
stages:
- stage: Build
  displayName: Build Stage
  jobs:
  - job: BuildTMT
    displayName: Build TMT Mobile api
    steps:
    - template: templates/ci.build.yml
      parameters:
        deploy: ${{ parameters.Deploy}}
############
##DEPLOY
############
- ${{if eq(parameters.Deploy, true) }}:
############
##DEV
############
  - stage: DeployDev
    displayName: "Dev Deployment"
    dependsOn: [Build]
    condition: and(succeeded(), eq( variables['Build.SourceBranchName'], 'main' ))
    variables:
    - group: SAFOZI-31-PGSQL-CONFIG
    - template: variables/vars.dev.yml
    jobs:
    - template: templates/cd.wap.deploy.yml
      parameters:
        ServiceConnection: tmt-nprod-ado-sc
        WebAppName: tmt-mapi-dev-wap-01
        resourceGroup: tmt-dev-rsg-02
        TargetEnvironment: tmt-mobile-dev
        appsParameters:
          FileTransformFolderPath: Packages/TMT.Mobile.Api.zip
          appSettings: $(wap.settings)
      
            
############
##QA
############
  - stage: DeployQA
    displayName: "QA Deployment"
    dependsOn: [Build]
    condition: and(succeeded(), startsWith( variables['Build.SourceBranch'], 'refs/heads/release' ))
    variables:
    - group: SAFOZI-31-PGSQL-CONFIG
    - template: variables/vars.qa.yml
    jobs:  
    - template: templates/cd.wap.deploy.yml
      parameters:
        ServiceConnection: tmt-nprod-ado-sc
        WebAppName: tmt-qa-wap-01
        resourceGroup: tmt-qa-rsg-01
        TargetEnvironment: tmt-mobile-qa
        appsParameters:
          FileTransformFolderPath: Packages/TMT.Mobile.Api.zip
          appSettings: $(wap.settings)
       

        
