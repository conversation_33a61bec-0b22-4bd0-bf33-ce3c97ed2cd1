using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Infrastructure;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Api.Services;
using System.Text;
using System.Security.Claims;

namespace TMT.Mobile.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class LeaveRequestController : ControllerBase
    {
        private readonly DynamicDbContext _dynamicDb;
        private readonly LeaveRequestRepository _leaveRequestRepository;
        private readonly AppUserRepository _appUserRepository;
        private readonly AppvariableRepository _appvariableRepository;
        private readonly ILogger<LeaveRequestController> _logger;
        private readonly IEmailService _emailService;

        public LeaveRequestController(
            ILogger<LeaveRequestController> logger,
            DynamicDbContext dynamicDb,
            LeaveRequestRepository leaveRequestRepository,
            AppUserRepository appUserRepository,
            AppvariableRepository appvariableRepository,
            IEmailService emailService)
        {
            _leaveRequestRepository = leaveRequestRepository;
            _appUserRepository = appUserRepository;
            _appvariableRepository = appvariableRepository;
            _dynamicDb = dynamicDb ?? throw new ArgumentNullException(nameof(dynamicDb));
            _logger = logger;
            _emailService = emailService;
        }
      
        private string GetUserEmail()
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email);
            if (emailClaim == null)
                throw new UnauthorizedAccessException("Email not found in token.");

            return emailClaim.Value;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllLeaveRequests()
        {
            var requests = await _leaveRequestRepository.GetAllAsync();
            return Ok(requests);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetLeaveRequestById(int id)
        {
            var leaveRequest = await _leaveRequestRepository.GetByIdAsync(id);
            if (leaveRequest == null)
                return NotFound("Leave request not found.");

            return Ok(leaveRequest);
        }

        [HttpPost]
        public async Task<IActionResult> CreateLeaveRequest([FromBody] LeaveRequestDto requestDto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var email = GetUserEmail();
                var submittedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Submitted");
                if (!submittedStatusId.HasValue)
                    return BadRequest("Status 'Submitted' not found.");

                var employee = await _dynamicDb.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new {
                        u.Id,
                        u.Firstname,
                        u.Lastname,
                        u.Email
                    })
                    .FirstOrDefaultAsync();

                if (employee == null)
                    return BadRequest("Employee not found.");

                dynamic manager = null;

                if (requestDto.Idassignedtomanager.HasValue)
                {
                    manager = await _dynamicDb.Appusers
                        .Where(u => u.Id == requestDto.Idassignedtomanager)
                        .Select(u => new {
                            u.Id,
                            u.Firstname,
                            u.Lastname,
                            u.Email,
                            u.DefaultLanguage
                        })
                        .FirstOrDefaultAsync();
                }
                else
                {
                    var managerDto = await _appUserRepository.GetManagerByUserEmail(email);
                    if (managerDto != null)
                    {
                        manager = new {
                            Id = managerDto.Id,
                            Firstname = managerDto.FirstName,
                            Lastname = managerDto.LastName,
                            Email = managerDto.Email,
                            DefaultLanguage = managerDto.DefaultLanguage
                        };
                    }
                }

                var leaveRequest = new LeaveRequest
                {
                    Iduser = employee.Id,
                    Idtype = requestDto.Idtype,
                    Idassignedtomanager = manager?.Id ?? requestDto.Idassignedtomanager,
                    Title = requestDto.Title,
                    Description = requestDto.Description,
                    Datestart = requestDto.Datestart,
                    Dateend = requestDto.Dateend,
                    Quantity = requestDto.Quantity,
                    Idmanagervalidationstatus = submittedStatusId.Value,
                    Idhrvalidationstatus = submittedStatusId.Value,
                    Createddate = DateTime.UtcNow,
                    Updateddate = DateTime.UtcNow
                };

                await _leaveRequestRepository.AddAsync(leaveRequest);

                if (manager != null && !string.IsNullOrEmpty(manager.Email))
                {
                    await SendLeaveRequestEmail(employee, manager, leaveRequest, manager.DefaultLanguage);
                }

                return CreatedAtAction(nameof(GetLeaveRequestById), new { id = leaveRequest.Id }, leaveRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating leave request");
                return StatusCode(500, "An error occurred while creating the leave request.");
            }
        }

        private async Task SendLeaveRequestEmail(dynamic employee, dynamic manager, LeaveRequest leaveRequest, string? defaultLanguage)
        {
            try
            {
                string language = (defaultLanguage?.ToLower()) switch
                {
                    "en" => "en",
                    "fr" => "fr",
                    _ => "fr" 
                };

                string emailSubject;
                string emailBody;

                if (language == "en")
                {
                    emailSubject = $"New Leave Request - {employee.Firstname} {employee.Lastname}";
                    emailBody = $@"
                        <div style='font-family: Arial, sans-serif; color: #333;'>
                            <h2 style='color: #2c3e50;'>Leave Request Notification</h2>
                            <p>Dear {manager.Firstname} {manager.Lastname},</p>
                            <p>You have received a new leave request from your team member:</p>
                            <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 15px 0;'>
                                <p><strong>Employee:</strong> {employee.Firstname} {employee.Lastname}</p>
                                <p><strong>Request Title:</strong> {leaveRequest.Title}</p>
                                <p><strong>Period:</strong> {leaveRequest.Datestart:dd/MM/yyyy} to {leaveRequest.Dateend:dd/MM/yyyy}</p>
                                <p><strong>Duration:</strong> {leaveRequest.Quantity} day(s)</p>
                                <p><strong>Description:</strong> {leaveRequest.Description}</p>
                            </div>
                            <p>Please review this request in the HR Management System.</p>
                            <p>Best regards,<br><strong>Human Resources Department</strong></p>
                        </div>";
                }
                else
                {
                    emailSubject = $"Nouvelle Demande de Congé - {employee.Firstname} {employee.Lastname}";
                    emailBody = $@"
                        <div style='font-family: Arial, sans-serif; color: #333;'>
                            <h2 style='color: #2c3e50;'>Notification de Demande de Congé</h2>
                            <p>Bonjour {manager.Firstname} {manager.Lastname},</p>
                            <p>Vous avez reçu une nouvelle demande de congé de la part de votre collaborateur :</p>
                            <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 15px 0;'>
                                <p><strong>Employé :</strong> {employee.Firstname} {employee.Lastname}</p>
                                <p><strong>Titre :</strong> {leaveRequest.Title}</p>
                                <p><strong>Période :</strong> du {leaveRequest.Datestart:dd/MM/yyyy} au {leaveRequest.Dateend:dd/MM/yyyy}</p>
                                <p><strong>Durée :</strong> {leaveRequest.Quantity} jour(s)</p>
                                <p><strong>Description :</strong> {leaveRequest.Description}</p>
                            </div>
                            <p>Merci de consulter cette demande dans le système de gestion RH.</p>
                            <p>Cordialement,<br><strong>Service RH</strong></p>
                        </div>";
                }

                await _emailService.SendEmailAsync(manager.Email, emailSubject, emailBody);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send leave request email");
            }
        }



        [HttpGet("assigned-to-me")]
        [Authorize]
        public async Task<IActionResult> GetAssignedLeaveRequests()
        {
            try
            {
                var email = GetUserEmail();
                var user = await _dynamicDb.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new { u.Id })
                    .FirstOrDefaultAsync();
                    
                if (user == null)
                    return NotFound("Utilisateur non trouvé");
                    
                var userId = user.Id;
                var requests = await _leaveRequestRepository.GetAssignedToUserAsync(userId);
                
                return Ok(requests);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "Échec de la récupération des demandes",
                    Error = ex.Message
                });
            }
        }

        [HttpGet("my-leave-requests")]
        [Authorize]
        public async Task<IActionResult> GetMyLeaveRequests()
        {
            try
            {
                var email = GetUserEmail();
                var user = await _dynamicDb.Appusers
                    .Where(u => u.Email == email)
                    .Select(u => new { u.Id })
                    .FirstOrDefaultAsync();
                    
                if (user == null)
                    return NotFound("Utilisateur non trouvé");
                    
                var userId = user.Id;
                var myRequests = await _leaveRequestRepository.GetByUserIdAsync(userId);
                
                return Ok(myRequests);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "Échec de la récupération de vos demandes",
                    Error = ex.Message
                });
            }
        }
        
[HttpPut("{id}")]
public async Task<IActionResult> UpdateLeave(int id, [FromBody] LeaveRequestDto leaveUpdateDto)
{
    if (!ModelState.IsValid)
        return BadRequest(ModelState);

    var leave = await _leaveRequestRepository.GetByIdAsync(id);
    if (leave == null)
        return NotFound("Leave request not found.");

    var email = GetUserEmail();
    
    if (!string.IsNullOrEmpty(leave.Validatedbyhr) || !string.IsNullOrEmpty(leave.Validatedbymanger))
    {
        return BadRequest("Modification not allowed: request has already been validated or rejected.");
    }

    var submittedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Submitted");
    if (!submittedStatusId.HasValue)
        return BadRequest("Status 'Submitted' not found.");

    var employee = await _dynamicDb.Appusers
        .Where(u => u.Email == email)
        .Select(u => new {
            u.Id,
            u.Firstname,
            u.Lastname,
            u.Email
        })
        .FirstOrDefaultAsync();

    if (employee == null)
        return BadRequest("Employee not found.");

    dynamic manager = null;

    if (leaveUpdateDto.Idassignedtomanager.HasValue)
    {
        manager = await _dynamicDb.Appusers
            .Where(u => u.Id == leaveUpdateDto.Idassignedtomanager)
            .Select(u => new {
                u.Id,
                u.Firstname,
                u.Lastname,
                u.Email,
                u.DefaultLanguage
            })
            .FirstOrDefaultAsync();
    }
    else
    {
        var managerDto = await _appUserRepository.GetManagerByUserEmail(email);
        if (managerDto != null)
        {
            manager = new {
                Id = managerDto.Id,
                Firstname = managerDto.FirstName,
                Lastname = managerDto.LastName,
                Email = managerDto.Email,
                DefaultLanguage = managerDto.DefaultLanguage
            };
        }
    }

    leave.Idtype = leaveUpdateDto.Idtype;
    leave.Title = leaveUpdateDto.Title;
    leave.Description = leaveUpdateDto.Description;
    leave.Dateend = leaveUpdateDto.Dateend;
    leave.Datestart = leaveUpdateDto.Datestart;
    leave.Quantity = leaveUpdateDto.Quantity;
    leave.Idassignedtomanager = manager?.Id ?? leaveUpdateDto.Idassignedtomanager;
    leave.Idmanagervalidationstatus = submittedStatusId.Value;
    leave.Idhrvalidationstatus = submittedStatusId.Value;
    leave.Updateddate = DateTime.UtcNow;

    await _leaveRequestRepository.UpdateAsync(leave);

    if (manager != null && !string.IsNullOrEmpty(manager.Email))
    {
        await SendLeaveRequestUpdateEmail(employee, manager, leave, manager.DefaultLanguage);
    }

    return NoContent();
}

private async Task SendLeaveRequestUpdateEmail(dynamic employee, dynamic manager, LeaveRequest leaveRequest, string? defaultLanguage)
{
    try
    {
        string language = (defaultLanguage?.ToLower()) switch
        {
            "en" => "en",
            "fr" => "fr",
            _ => "fr" 
        };

        string emailSubject;
        string emailBody;

        if (language == "en")
        {
            emailSubject = $"Updated Leave Request - {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Leave Request Update Notification</h2>
                    <p>Dear {manager.Firstname} {manager.Lastname},</p>
                    <p>A leave request from your team member has been updated:</p>
                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #e74c3c; margin: 15px 0;'>
                        <p><strong>Employee:</strong> {employee.Firstname} {employee.Lastname}</p>
                        <p><strong>Request Title:</strong> {leaveRequest.Title}</p>
                        <p><strong>Period:</strong> {leaveRequest.Datestart:dd/MM/yyyy} to {leaveRequest.Dateend:dd/MM/yyyy}</p>
                        <p><strong>Duration:</strong> {leaveRequest.Quantity} day(s)</p>
                        <p><strong>Description:</strong> {leaveRequest.Description}</p>
                    </div>
                    <p>Please review this updated request in the HR Management System.</p>
                    <p>Best regards,<br><strong>Human Resources Department</strong></p>
                </div>";
        }
        else
        {
            emailSubject = $"Demande de Congé Modifiée - {employee.Firstname} {employee.Lastname}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Notification de Modification de Demande de Congé</h2>
                    <p>Bonjour {manager.Firstname} {manager.Lastname},</p>
                    <p>Une demande de congé de votre collaborateur a été modifiée :</p>
                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #e74c3c; margin: 15px 0;'>
                        <p><strong>Employé :</strong> {employee.Firstname} {employee.Lastname}</p>
                        <p><strong>Titre :</strong> {leaveRequest.Title}</p>
                        <p><strong>Période :</strong> du {leaveRequest.Datestart:dd/MM/yyyy} au {leaveRequest.Dateend:dd/MM/yyyy}</p>
                        <p><strong>Durée :</strong> {leaveRequest.Quantity} jour(s)</p>
                        <p><strong>Description :</strong> {leaveRequest.Description}</p>
                    </div>
                    <p>Merci de consulter cette demande mise à jour dans le système de gestion RH.</p>
                    <p>Cordialement,<br><strong>Service RH</strong></p>
                </div>";
        }

        await _emailService.SendEmailAsync(manager.Email, emailSubject, emailBody);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to send leave request update email");
    }
}

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLeave(int id)
        {
            var leave = await _leaveRequestRepository.GetByIdAsync(id);
            if (leave == null)
                return NotFound("Leave request not found.");

            var userId = GetUserEmail();
           
            await _leaveRequestRepository.DeleteAsync(leave);

            return NoContent(); 
        }

   [HttpPut("accept/manager/{id}")]
public async Task<IActionResult> AcceptByManager(int id, [FromBody] ManagerAssignmentDto dto)
{
    try
    {
        var leave = await _leaveRequestRepository.GetByIdAsync(id);
        if (leave == null)
            return NotFound("Leave request not found.");

        var approvedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Approved");
        if (!approvedStatusId.HasValue)
            return BadRequest("Status 'Approved' not found.");

        var rejectedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Rejected");
        if (leave.Idmanagervalidationstatus == approvedStatusId.Value)
            return BadRequest("Request is already approved.");
        if (leave.Idmanagervalidationstatus == rejectedStatusId.Value)
            return BadRequest("Request cannot be approved as it has already been rejected.");

        leave.Idmanagervalidationstatus = approvedStatusId.Value;
        leave.Idhrvalidationstatus = approvedStatusId.Value;
        leave.Updateddate = DateTime.UtcNow;
        leave.Managercomment = dto.Managercomment;

        await _leaveRequestRepository.UpdateAsync(leave);

        await SendStatusUpdateEmail(leave.Iduser, leave, "Approved", "Manager");

        return Ok(new { Message = "Request successfully Approved", Status = "Approved", leave });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error while accepting the leave request.");
        return StatusCode(500, "An error occurred while approving the leave request.");
    }
}


[HttpPut("reject/manager/{id}")]
public async Task<IActionResult> RejectByManager(int id, [FromBody] ManagerAssignmentDto dto)
{
    try
    {
        var leave = await _leaveRequestRepository.GetByIdAsync(id);
        if (leave == null)
            return NotFound("Leave request not found.");

        var rejectedStatusId = await _appvariableRepository.GetStatusIdByNameAsync("Rejected");
        if (!rejectedStatusId.HasValue)
            return BadRequest("Status 'Rejected' not found.");

        if (leave.Idmanagervalidationstatus == rejectedStatusId.Value)
            return BadRequest("Request is already Rejected.");

        leave.Idmanagervalidationstatus = rejectedStatusId.Value;
        leave.Idhrvalidationstatus = rejectedStatusId.Value;
        leave.Updateddate = DateTime.UtcNow;
        leave.Managercomment = dto.Managercomment;

        await _leaveRequestRepository.UpdateAsync(leave);

        await SendStatusUpdateEmail(leave.Iduser, leave, "Rejected", "Manager");

        return Ok(new { Message = "Request successfully Rejected", Status = "Rejected", leave });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error while rejecting the leave request.");
        return StatusCode(500, "An error occurred while rejecting the leave request.");
    }
}



 [HttpPut("reassign-manager/{id}")]
public async Task<IActionResult> ReassignManager(int id, [FromBody] ManagerAssignmentDto dto)
{
    try
    {
        var leave = await _leaveRequestRepository.GetByIdAsync(id);
        if (leave == null)
            return NotFound("Leave request not found.");

        int newManagerId = dto.Idassignedtomanager 
            ?? leave.Idassignedtomanager 
            ?? throw new Exception("No manager ID available.");

        var newManager = await _dynamicDb.Appusers
            .Where(u => u.Id == newManagerId)
            .Select(u => new {
                u.Id,
                u.Firstname,
                u.Lastname,
                u.Email,
                Language = u.DefaultLanguage
            })
            .FirstOrDefaultAsync();
        if (newManager == null)
            return BadRequest("Assigned manager not found.");

        leave.Idassignedtomanager = newManager.Id;
        leave.Managerfullname     = $"{newManager.Firstname} {newManager.Lastname}";
        leave.Managercomment      = dto.Managercomment;
        leave.Updateddate         = DateTime.UtcNow;
        await _leaveRequestRepository.UpdateAsync(leave);

        var employee = await _dynamicDb.Appusers
            .Where(u => u.Id == leave.Iduser)
            .Select(u => new {
                u.Firstname,
                u.Lastname,
                u.Email,
                Language = u.DefaultLanguage
            })
            .FirstOrDefaultAsync();
        if (employee == null)
            return BadRequest("Employee not found.");

      Func<string, string> formatComment = lang =>
            string.IsNullOrWhiteSpace(leave.Managercomment)
                ? ""
                : $"<p><strong>{(lang == "en" ? "Note" : "Note du manager")}:</strong> {leave.Managercomment}</p>";

        {
            string langManager = newManager.Language?.ToLower() == "en" ? "en" : "fr";
            string subjectManager = langManager == "en"
                ? $"New Leave Request Assignment - {employee.Firstname} {employee.Lastname}"
                : $"Nouvelle assignation de demande - {employee.Firstname} {employee.Lastname}";

            string commentManagerSection = formatComment(langManager);

            string bodyManager = langManager == "en"
                ? $@"
                <div style='font-family:Arial,sans-serif;color:#333;'>
                  <h2 style='color:#2c3e50;'>New Request Assignment</h2>
                  <p>Dear {newManager.Firstname} {newManager.Lastname},</p>
                  <p>You have been assigned a new leave request to review:</p>
                  <div style='background:#f8f9fa;padding:15px;border-left:4px solid #3498db;margin:15px 0;'>
                    <p><strong>Employee:</strong> {employee.Firstname} {employee.Lastname}</p>
                    <p><strong>Title:</strong> {leave.Title}</p>
                    <p><strong>Period:</strong> {leave.Datestart:dd/MM/yyyy} to {leave.Dateend:dd/MM/yyyy}</p>
                    <p><strong>Duration:</strong> {leave.Quantity} day(s)</p>
                    {commentManagerSection}
                  </div>
                  <p>Please log in to the HR Management System to review this request.</p>
                  <p>Thank you.</p>
                  <p><strong>Human Resources Department</strong></p>
                </div>"
                : $@"
                <div style='font-family:Arial,sans-serif;color:#333;'>
                  <h2 style='color:#2c3e50;'>Nouvelle assignation de demande</h2>
                  <p>Bonjour {newManager.Firstname} {newManager.Lastname},</p>
                  <p>Une nouvelle demande de congé vous a été assignée :</p>
                  <div style='background:#f8f9fa;padding:15px;border-left:4px solid #3498db;margin:15px 0;'>
                    <p><strong>Employé :</strong> {employee.Firstname} {employee.Lastname}</p>
                    <p><strong>Titre :</strong> {leave.Title}</p>
                    <p><strong>Période :</strong> du {leave.Datestart:dd/MM/yyyy} au {leave.Dateend:dd/MM/yyyy}</p>
                    <p><strong>Durée :</strong> {leave.Quantity} jour(s)</p>
                    {commentManagerSection}
                  </div>
                  <p>Veuillez vous connecter au système RH pour consulter cette demande.</p>
                  <p>Merci.</p>
                  <p><strong>Service RH</strong></p>
                </div>";

            await _emailService.SendEmailAsync(newManager.Email, subjectManager, bodyManager);
        }

        {
            string langEmployee = employee.Language?.ToLower() == "en" ? "en" : "fr";
            string subjectEmployee = langEmployee == "en"
                ? "Your Leave Request Has Been Reassigned"
                : "Votre demande de congé a été réassignée";

            string commentEmployeeSection = formatComment(langEmployee);

            string bodyEmployee = langEmployee == "en"
                ? $@"
                <div style='font-family:Arial,sans-serif;color:#333;'>
                  <h2 style='color:#2c3e50;'>Request Reassignment Notification</h2>
                  <p>Dear {employee.Firstname} {employee.Lastname},</p>
                  <p>Your leave request has been reassigned to:</p>
                  <div style='background:#f8f9fa;padding:15px;border-left:4px solid #3498db;margin:15px 0;'>
                    <p><strong>New Manager:</strong> {newManager.Firstname} {newManager.Lastname}</p>
                    <p><strong>Title:</strong> {leave.Title}</p>
                    <p><strong>Period:</strong> {leave.Datestart:dd/MM/yyyy} to {leave.Dateend:dd/MM/yyyy}</p>
                    {commentEmployeeSection}
                  </div>
                  <p>You will be notified once your request has been processed.</p>
                  <p>Best regards,</p>
                  <p><strong>Human Resources Department</strong></p>
                </div>"
                : $@"
                <div style='font-family:Arial,sans-serif;color:#333;'>
                  <h2 style='color:#2c3e50;'>Notification de réassignation</h2>
                  <p>Bonjour {employee.Firstname} {employee.Lastname},</p>
                  <p>Votre demande de congé a été réassignée à :</p>
                  <div style='background:#f8f9fa;padding:15px;border-left:4px solid #3498db;margin:15px 0;'>
                    <p><strong>Nouveau manager :</strong> {newManager.Firstname} {newManager.Lastname}</p>
                    <p><strong>Titre :</strong> {leave.Title}</p>
                    <p><strong>Période :</strong> du {leave.Datestart:dd/MM/yyyy} au {leave.Dateend:dd/MM/yyyy}</p>
                    {commentEmployeeSection}
                  </div>
                  <p>Vous serez informé(e) une fois que votre demande aura été traitée.</p>
                  <p>Cordialement,</p>
                  <p><strong>Service RH</strong></p>
                </div>";

            await _emailService.SendEmailAsync(employee.Email, subjectEmployee, bodyEmployee);
        }

        return Ok(new
        {
            Message = "Leave request updated. Notifications sent to manager and employee.",
            LeaveDetails = leave
        });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error while reassigning manager.");
        return StatusCode(500, "An error occurred while reassigning the manager.");
    }
}



        private async Task<dynamic> GetUserBasicInfo(int userId)
        {
            return await _dynamicDb.Appusers
                .Where(u => u.Id == userId)
                .Select(u => new {
                    u.Id,
                    u.Firstname,
                    u.Lastname,
                    u.Email,
                    u.DefaultLanguage
                })
                .FirstOrDefaultAsync();
        }

    private async Task SendStatusUpdateEmail(int userId, LeaveRequest leaveRequest, string status, string validatedBy)
{
    try
    {
        var employee = await GetUserBasicInfo(userId);
        if (employee?.Email == null) return;

        string lang = employee.DefaultLanguage?.ToLower() ?? "fr";
        string statusColor = status == "Approved" ? "#27ae60" : "#e74c3c";

        string emailSubject;
        string emailBody;

        var managerCommentSection = string.IsNullOrWhiteSpace(leaveRequest.Managercomment)
            ? ""
            : (lang == "en"
                ? $"<p><strong>Manager's Comment:</strong> {leaveRequest.Managercomment}</p>"
                : $"<p><strong>Commentaire du manager :</strong> {leaveRequest.Managercomment}</p>");

        if (lang == "en")
        {
            emailSubject = $"Leave Request Update: {status}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Leave Request Status Update</h2>
                    <p>Dear {employee.Firstname} {employee.Lastname},</p>

                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid {statusColor}; margin: 15px 0;'>
                        <p><strong>Status:</strong> <span style='color: {statusColor}; font-weight: bold;'>{status}</span></p>
                        <p><strong>Request Title:</strong> {leaveRequest.Title}</p>
                        <p><strong>Period:</strong> {leaveRequest.Datestart:dd/MM/yyyy} to {leaveRequest.Dateend:dd/MM/yyyy}</p>
                        <p><strong>Duration:</strong> {leaveRequest.Quantity} day(s)</p>
                        {managerCommentSection}
                    </div>

                    {(status == "Approved"
                        ? "<p>Your leave request has been approved. Please make the necessary arrangements for your absence.</p>"
                        : "<p>For more information about this decision, please contact your manager or HR representative.</p>")}

                    <p>Best regards,</p>
                    <p><strong>Human Resources Department</strong></p>
                </div>";
        }
        else
        {
            emailSubject = $"Mise à jour de la demande de congé : {status}";
            emailBody = $@"
                <div style='font-family: Arial, sans-serif; color: #333;'>
                    <h2 style='color: #2c3e50;'>Mise à jour du statut de la demande de congé</h2>
                    <p>Bonjour {employee.Firstname} {employee.Lastname},</p>

                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid {statusColor}; margin: 15px 0;'>
                        <p><strong>Statut :</strong> <span style='color: {statusColor}; font-weight: bold;'>{status}</span></p>
                        <p><strong>Titre de la demande :</strong> {leaveRequest.Title}</p>
                        <p><strong>Période :</strong> du {leaveRequest.Datestart:dd/MM/yyyy} au {leaveRequest.Dateend:dd/MM/yyyy}</p>
                        <p><strong>Durée :</strong> {leaveRequest.Quantity} jour(s)</p>
                        {managerCommentSection}
                    </div>

                    {(status == "Approved"
                        ? "<p>Votre demande de congé a été approuvée. Veuillez prendre les dispositions nécessaires.</p>"
                        : "<p>Pour plus d'informations concernant cette décision, veuillez contacter votre manager ou le service RH.</p>")}

                    <p>Cordialement,</p>
                    <p><strong>Service des Ressources Humaines</strong></p>
                </div>";
        }

        await _emailService.SendEmailAsync(employee.Email, emailSubject, emailBody);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to send status update email");
    }
}



    }
}