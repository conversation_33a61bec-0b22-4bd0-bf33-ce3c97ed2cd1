using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Infrastructure.Repositories;
using System.Security.Claims;
using TMT.Mobile.Core.Consts;
using Microsoft.EntityFrameworkCore;
using TMT.Mobile.Core.Entities.DTOs;

namespace TMT.Mobile.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CurrencyController : ControllerBase
    {        
        private readonly ILogger<CurrencyController> _logger;
        private readonly CurrencyRepository _currencyRepository;

        public CurrencyController(        
            ILogger<CurrencyController> logger,
            CurrencyRepository currencyRepository)
        {
            _logger = logger;
            _currencyRepository = currencyRepository;
        }


[HttpGet]
        public async Task<IActionResult> GetAllCurrency()
        {
            var requests = await _currencyRepository.GetAllAsync();
            return Ok(requests);
        }
    }
}
