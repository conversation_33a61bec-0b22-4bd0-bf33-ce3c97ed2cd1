<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\video_player_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\shared_preferences_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\path_provider_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\flutter_secure_storage\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\device_info_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\android\app\src\main\assets"/><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\intermediates\shader_assets\debug\out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\android\app\src\debug\assets"/></dataSet></merger>