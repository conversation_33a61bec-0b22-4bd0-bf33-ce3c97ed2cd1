import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';
import 'package:tmt_mobile/screens/landingScreen.dart';
import 'package:tmt_mobile/utils/userServices.dart';

class ResetPasswordController extends GetxController {
  var SignInKey = GlobalKey<FormState>();

  var email = TextEditingController().obs;
  var password = TextEditingController().obs;
  var confirmpassword = TextEditingController().obs;
  var showError = false.obs;
  var EmailSent = false.obs;
  var Codevalidation = TextEditingController().obs;
  var confirmpaswword = TextEditingController().obs;
  var errormsg = "Bad Credentials".obs;
  var passwtoggl = true.obs;

  var passwtogg2 = true.obs;
  var loading = false.obs;
  var context = UserServices();
  Future sendResetPassword(String email) async {
    loading.value = true;
    var response = await context.sendResetCode(email);
    if (response.statusCode == 200) {
      EmailSent.value = true;
    } else {
      errormsg.value = "email n'existe pas";
      showError.value = true;
    }
    loading.value = false;
  }

  Future changePassword(String email, String code, String passwrod) async {
    loading.value = true;
    var response = await context.changepassword(email, code, passwrod);
    if (response.statusCode == 200) {
      Get.offAll(LandingScreen());
    } else {
      errormsg.value = "code validation est incorrect ou expiré";
      showError.value = true;
    }
    loading.value = false;
  }

  String? validatePasswordlen(String c1) {
    GlobalController global = Get.find<GlobalController>();
    if (c1.isEmpty) {
      return global.lang.value == "fr"
          ? "Le mot de passe est obligatoire"
          : "Password is required";
    } else if (c1.length < 6) {
      return global.lang.value == "fr"
          ? "Minimum 6 caractères"
          : "Minimum 6 characters";
    }
    return null;
  }

  String? validatePassword(String c1, c2) {
    GlobalController global = Get.find<GlobalController>();
    if (c2.isEmpty || c2 == null) {
      return global.lang.value == "fr"
          ? "Confirmation mot de passe est obligatoire"
          : "Password confirmation is required";
    } else if (c1 != c2) {
      return global.lang.value == "fr"
          ? "Les mots de passe ne correspondent pas"
          : "Passwords do not match";
    }
    return null;
  }

  String? validateEmail(String c1) {
    GlobalController global = Get.find<GlobalController>();
    if (c1.isEmpty) {
      return global.lang.value == "fr"
          ? "Ce champ ne peut pas être vide"
          : "This field can't be empty";
    } else if (c1.contains("@") == false) {
      return global.lang.value == "fr"
          ? "Vérifiez le champ email"
          : "Verify email field";
    }
    return null;
  }
}
