﻿using System;
using System.Collections.Generic;

namespace TMT.Mobile.Core.Entities
{
    public partial class Projectlots :IEntity
    {
        public Projectlots()
        {
            Projecttasks = new HashSet<Projecttasks>();
            Timesheets = new HashSet<Timesheets>();
        }

        public int Id { get; set; }
        public string Lotname { get; set; }
        public string? Lotdescription { get; set; }
        public int? Idproject { get; set; }
        public int? Idstatus { get; set; }
        public double? Originalworkloadestimates { get; set; }
        public double? Workload { get; set; }
        public DateTime? Datestart { get; set; }
        public DateTime? Dateend { get; set; }
        public bool Isdeleted { get; set; }
        public string? Createdby { get; set; }
        public DateTime? Createddate { get; set; }
        public string? Updatedby { get; set; }
        public DateTime? Updateddate { get; set; }

        public virtual Projects Project { get; set; }
        public virtual Appvariables Status { get; set; }
        public virtual ICollection<Projecttasks> Projecttasks { get; set; }
        public virtual ICollection<Timesheets> Timesheets { get; set; }
    }
}
