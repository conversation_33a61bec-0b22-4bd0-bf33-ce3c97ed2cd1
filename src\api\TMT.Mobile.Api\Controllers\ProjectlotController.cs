﻿using Microsoft.AspNetCore.Mvc;
using TMT.Mobile.Api.Interfaces;
using TMT.Mobile.Infrastructure.Repositories;
using TMT.Mobile.Infrastructure;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using TMT.Mobile.Core.Consts;
using TMT.Mobile.Core.Entities.DTOs;
using TMT.Mobile.Core.Entities;

namespace TMT.Mobile.Api.Controllers
{
    public class ProjectlotController : Controller
    {
        private readonly DynamicDbContext _DynamicDb;
        private readonly ITokenService _tokenService;


        public ProjectlotController(DynamicDbContext dynamicDb, ITokenService tokenService)
        {

            _DynamicDb = dynamicDb;
            _tokenService = tokenService;

        }


        [HttpGet("getProjectlots")]
        [Authorize]
        public async Task<IActionResult> getProjectlots( int projectId)

        {
            var IsAuthenticated = User.Identity.IsAuthenticated;
            if (!IsAuthenticated)
            {
                return Unauthorized("your are not authentified");
            }

            var user = User as ClaimsPrincipal;
            var identity = user.Identity as ClaimsIdentity;
            var userid = (from c in user.Claims
                          where c.Type == TMTClaimsTypes.UserId
                          select c).Single().Value;
            var email = (from c in user.Claims
                         where c.Type == TMTClaimsTypes.UserLogin
                         select c).Single().Value;
            
            List<Projectlots> projects = _DynamicDb.Projectlots.Where(u => u.Idproject== projectId && u.Isdeleted == false).ToList();


            List<ProjectlotDto> result = projects.Select(u =>
            new ProjectlotDto
            {
                Id = u.Id,
                Name = u.Lotname,
                projectId= (int)u.Idproject

            }).ToList();

            return Ok(result);

        }
    }
}
