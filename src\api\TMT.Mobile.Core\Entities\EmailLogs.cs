using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMT.Mobile.Core.Entities;
using TMT.Mobile.Core;
using TMT.Mobile.Core.Consts;


namespace TMT.Mobile.Core.Entities
{
    public class EmailsLog : IEntity
    {
         public EmailsLog()
        {
           
        }
    public int Id { get; set; }
    public string? Sender { get; set; }
    public string? SenderEmail { get; set; }
    public string? Recipients { get; set; }
    public string? CarbonCopy { get; set; }
    public string? Subject { get; set; }
    public string? Body { get; set; }
    public string? Attachments { get; set; }
    public DateTime DateSent { get; set; } = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
    public string? Status { get; set; }
  }
}