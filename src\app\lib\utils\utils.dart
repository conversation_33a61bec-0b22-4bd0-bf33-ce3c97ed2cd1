import 'dart:io';

import 'package:flutter/widgets.dart';

// tmt-mapi-dev-wap-01-fubec3e0dyffa8e8.northeurope-01.azurewebsites.net for hosted
final String apiUrl =
    "https://tmt-mapi-dev-wap-01-fubec3e0dyffa8e8.northeurope-01.azurewebsites.net/"; //for emulator use http://********:7052  || for physical device with USB debug localhost or 127.0.0.1  SAME NETWORK FOR PHONE AND PC
final String QuerryUrl =
    "tmt-mapi-dev-wap-01-fubec3e0dyffa8e8.northeurope-01.azurewebsites.net/"; //for emulator use http://********:7052  || for physical device with USB debug localhost or 127.0.0.1  SAME NETWORK FOR PHONE AND PC
// without usb debug you can run app using the  local ipv4 Addresse like this one *************:7052

String getDeviceType() {
  final data = MediaQueryData.fromView(WidgetsBinding.instance.window);
  return data.size.shortestSide < 600 ? 'phone' : 'tablet';
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
