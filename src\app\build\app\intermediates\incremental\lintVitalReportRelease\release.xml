<variant
    name="release"
    package="com.example.tmt_mobile"
    minSdkVersion="21"
    targetSdkVersion="34"
    shrinking="true"
    mergedManifest="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\intermediates\default_proguard_files\global\proguard-android.txt-7.3.0;C:\Users\<USER>\AppData\Local\Flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifest="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin"
        resDirectories="src\main\res"
        assetsDirectories="src\main\assets"/>
    <sourceProvider
        manifest="src\release\AndroidManifest.xml"
        javaDirectories="src\release\java;src\release\kotlin"
        resDirectories="src\release\res"
        assetsDirectories="src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <mainArtifact
      classOutputs="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\intermediates\javac\release\classes;C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\tmp\kotlin-classes\release;C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\kotlinToolingMetadata;C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.example.tmt_mobile"
      generatedSourceFolders="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\generated\ap_generated_sources\release\out;C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\generated\aidl_source_output_dir\release\out;C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\generated\source\buildConfig\release;C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\generated\renderscript_source_output_dir\release\out"
      generatedResourceFolders="C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\generated\res\rs\release;C:\Users\<USER>\source\repos\tmt-mobile-1\src\app\build\app\generated\res\resValues\release">
  </mainArtifact>
</variant>
